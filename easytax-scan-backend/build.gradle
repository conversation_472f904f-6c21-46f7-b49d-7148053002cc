buildscript {
    ext {
        springBootVersion = '3.4.2'
        springVersion = '6.2.2'
        assertJVersion = '3.27.3'
        junitVersion = '5.11.4'
        mockitoVersion = '5.15.2'
        lombokVersion = '1.18.36'
        spotbugsVersion = '4.9.1'
        queryDslVersion = '5.1.0'
        spotbugsGradlePluginVersion = '6.1.5'
        cpdVersion = '3.5'
        aclLibVersion = '2.1.0-RELEASE'
        aclMigratorVersion = '0.5.0-RELEASE'
        jexl3Version = '3.2'
        kafkaVersion = '3.2.2'
        log4jVersion = '2.24.3'
        quartzVersion = '2.5.0'
    }
    repositories {
        //Apply build type
        if (!project.hasProperty("environment") || project.environment == "local") {
            mavenCentral()
            maven {
                url "https://plugins.gradle.org/m2/"
            }
            // workaround since class/methods not accessible at this point
            println "\u001B[34m[INFO]\u001B[0m Build script set to local repositories"
        } else {
            maven {
                credentials {
                    username "${nexusUsername}"
                    password "${nexusPassword}"
                }
                url "${nexusUrl}${nexusPath}"
                allowInsecureProtocol = true
            }
            maven {
                url "https://plugins.gradle.org/m2/"
            }
            println "\u001B[34m[INFO]\u001B[0m Build script set to nexus repositories"
        }
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath "de.aaschmid:gradle-cpd-plugin:${cpdVersion}"
        classpath 'com.palantir.gradle.docker:gradle-docker:0.34.0'
        classpath "com.github.spotbugs.snom:spotbugs-gradle-plugin:${spotbugsGradlePluginVersion}"
    }
}

plugins {
//    id "com.avast.gradle.docker-compose" version "0.16.11"
    id "com.avast.gradle.docker-compose" version "0.17.4"
    id "com.palantir.docker" version "0.36.0"
}

allprojects {
    apply plugin: 'java'
    apply plugin: 'java-library'

    repositories {
        maven {
            credentials {
                username "${nexusUsername}"
                password "${nexusPassword}"
            }
            url "${nexusUrl}${nexusPath}"
            allowInsecureProtocol = true
        }
    }

    dependencies {
        //lombok for payload POJOs
        compileOnly "org.projectlombok:lombok:${lombokVersion}"
        testCompileOnly "org.projectlombok:lombok:${lombokVersion}"
        annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
        testAnnotationProcessor "org.projectlombok:lombok:${lombokVersion}"

        testImplementation "org.assertj:assertj-core:${assertJVersion}"
        testImplementation "org.junit.jupiter:junit-jupiter-api:${junitVersion}"
        testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:${junitVersion}"
    }

    test {
        useJUnitPlatform()
    }
}

apply plugin: 'org.springframework.boot'
apply from: 'config/quality/quality.gradle'
apply plugin: 'com.palantir.docker'

defaultTasks 'bootRun'

bootRun.ext.activeProfiles = 'default'
bootRun {
    doFirst {
        jvmArgs = ["-Dspring.profiles.active=" + ext.activeProfiles]
    }
}

tasks.named("jar") {
    enabled = false
}

sourceCompatibility = JavaVersion.VERSION_21

def majorVersion = 0
def minorVersion = 1
def patchVersion = 0
version sprintf("%s.%s.%s", majorVersion, minorVersion, patchVersion)
println "Building EASYTAX-SCAN Backend v" + version
updateApplicationProperties()

//defaults
ext {
    defaultProfile = "local"
    defaultBuildType = "RELEASE"
}

//Check for input build type, if not provided assume snapshot build type
if (!project.hasProperty("buildType")) {
    ext.buildType = ext.defaultBuildType
    logger.info(sprintf("No build type provided usind default value %s", ext.defaultBuildType))
}

//Validate build types
version sprintf("%s.%s.%s-%s", majorVersion, minorVersion, patchVersion, ext.buildType)
logger.info(sprintf("Building artifact version %s", version))

//Check for input environment, if not provided assume local build
if (!project.hasProperty("environment")) {
    ext.environment = ext.defaultProfile
    logger.info(sprintf("No enviroment build input provided usind default value %s", ext.defaultProfile))
} else {
    logger.info(sprintf("Using  enviroment '%s' profile", ext.environment))
}

//load file name
def buildScript = sprintf("config/build-%s.gradle", environment)

//Throw exception in case of invalid option
if (!file(buildScript).exists()) {
    throw new GradleException(sprintf("Unknown environment: %s", environment))
}

logger.info(sprintf("Loading custom build '%s'", buildScript))

//setup profile script file
apply from: buildScript



dependencies {
    implementation project(':domain')
    implementation project(':api')
    implementation project(':data')
    implementation project(':network')

    // FreeMarker with Jakarta support
    implementation("org.freemarker:freemarker:2.3.34")
    compileOnly("jakarta.servlet:jakarta.servlet-api:6.1.0")

    implementation(platform("org.apache.logging.log4j:log4j-bom:${log4jVersion}"))
    implementation "org.springframework.boot:spring-boot-starter-log4j2:${springBootVersion}"
    implementation "org.quartz-scheduler:quartz:${quartzVersion}"
    implementation("org.springframework.boot:spring-boot-starter-actuator:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    testImplementation("org.springframework.boot:spring-boot-starter-test:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    //rest assured for endpoint testing
    testImplementation "io.rest-assured:rest-assured:5.3.0"
    implementation 'com.microsoft.sqlserver:mssql-jdbc:12.9.0.jre11-preview'

    // JAX-B dependencies for JDK 9+
    implementation "jakarta.xml.bind:jakarta.xml.bind-api:4.0.0"
    implementation "org.glassfish.jaxb:jaxb-runtime:4.0.1"

    testImplementation(group: 'org.springframework.kafka', name: 'spring-kafka-test', version: "${kafkaVersion}") {
        exclude group: 'com.fasterxml.jackson.module', module: 'jackson-module-scala_2.13'
        exclude group: 'ch.qos.logback', module: 'logback-classic'
        exclude group: 'org.apache.logging.log4j', module: 'log4j-slf4j2-impl'
    }
    implementation 'org.apache.kafka:kafka-clients:3.7.0'
}

dockerCompose.isRequiredBy(test)
dockerCompose {
    executable = "docker"
    useComposeFiles = ['./../dockers/docker-compose.yml']
    def includeOptionalDatabases = project.hasProperty('includeOptionalDatabases') && project.includeOptionalDatabases.toBoolean()
    if (project.gradle.startParameter.taskNames.contains('test')) {
        startedServices = ['easytax-scan-database']
    } else if (includeOptionalDatabases) {
        startedServices = ['easytax-scan-database', 'cashrec-database', 'nav-database']
    } else {
        startedServices = ['easytax-scan-database']
    }
}

def updateApplicationProperties() {
    def configFile = file('src/main/resources/application.yml')
    println "updating version to '${version}' in ${configFile}"
    String configContent = configFile.getText('UTF-8')
    configContent = configContent.replaceAll(/info\.build\.version: .*/, "info.build.version: ${version}")
    configFile.write(configContent, 'UTF-8')
}

// Process resources in order to turn gradle properties available in the application.properties file
processResources {
    filesMatching('**/*.properties') { expand(project.properties) }
}

def getGitBranchForDockerTag() {
    def branchName = System.getenv('CHANGE_BRANCH')
    if (!branchName) {
        return "latest"
    }
    return branchName.replaceAll(".+/", "") + "-" + ext.buildType
}

def branchName = getGitBranchForDockerTag()
def dockerRegistry = System.getenv('DOCKER_REGISTRY') ? System.getenv('DOCKER_REGISTRY') : 'nexus.dev.js:8084'

docker {
    name "${dockerRegistry}/easytaxscan"
    tags "${version}", "latest", "${branchName}"
    copySpec.from("build/libs").into("app")
    files 'rev.txt'
    buildArgs([NEXUS: "${dockerRegistry}"])
    dockerfile file('../dockers/easytaxscan-backend')
}

int readFileLineByLine(String filePath) {
    File file = new File(filePath)
    def line, noOfLines = 0
    file.withReader { reader ->
        while ((line = reader.readLine()) != null) {
            println "${line}"
            noOfLines++
        }
    }
    return noOfLines
}

configurations.configureEach {
    resolutionStrategy {
        force 'org.freemarker:freemarker:2.3.34'
        force 'org.freemarker:freemarker-jakarta-servlet:2.3.34'
    }
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        if (details.requested.group == 'org.springframework') {
            details.useVersion '6.2.2'
            details.because 'Force Spring Framework to use consistent version 6.2.2 to prevent conflicts'
        }
    }
}