package pt.jumia.services.easytaxscan.network.jokes;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.properties.NetworkProperties;
import pt.jumia.services.easytaxscan.network.NetworkClientFactory;
import pt.jumia.services.easytaxscan.network.testutil.MockResponse;
import retrofit2.mock.Calls;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Tests our main implementation of network requests {@link JokesNetworkRequester}
 */
@ExtendWith(MockitoExtension.class)
public class JokesNetworkRequesterTest {

    @Mock
    private NetworkClientFactory networkClientFactory;

    @Mock
    private JokesClient jokesClient;

    private JokesNetworkRequester networkRequester;

    @BeforeEach
    public void setUp() {
        NetworkProperties networkProperties = new NetworkProperties();
        when(networkClientFactory.createJokesClient(anyString()))
                .thenReturn(jokesClient);
        networkRequester = new JokesNetworkRequester(networkProperties, networkClientFactory);
    }

    @Test
    public void fetchJokes_serverError() throws Exception {
        when(jokesClient.getJokes(anyInt()))
                .thenReturn(Calls.response(MockResponse.serverDown()));

        assertThrows(JokesErrorException.class, () -> networkRequester.fetchJokes(5));
    }

    @Test
    public void fetchJokes_badRequest() throws Exception {
        when(jokesClient.getJokes(anyInt()))
                .thenReturn(Calls.response(MockResponse.badRequest()));

        assertThrows(JokesErrorException.class, () -> networkRequester.fetchJokes(5));
    }

    @Test
    public void fetchJokes_unauthorized() throws Exception {
        when(jokesClient.getJokes(anyInt()))
                .thenReturn(Calls.response(MockResponse.unauthorized()));

        assertThrows(JokesErrorException.class, () -> networkRequester.fetchJokes(5));
    }
}
