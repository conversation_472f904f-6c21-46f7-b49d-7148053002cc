package pt.jumia.services.easytaxscan.network.jokes;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import pt.jumia.services.easytaxscan.network.NetworkClientFactory;
import pt.jumia.services.easytaxscan.network.jokes.payloads.JokeResponse;
import pt.jumia.services.easytaxscan.network.jokes.payloads.JokeResponse.JokeItem;
import retrofit2.Response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Tests for {@link JokesClient}
 */
// TODO: url is no longer working, jira ticket: https://jira.jumia.com/browse/AFREXP-39547
@Disabled
public class JokesClientTest {

    private static final String SERVER_URL = "http://api.icndb.com/";

    private JokesClient jokesClient;


    @BeforeEach
    public void setUp() throws Exception {
        jokesClient = new NetworkClientFactory()
            .createJokesClient(SERVER_URL);
    }

    @Test
    public void testOneJoke() throws Exception {

        Response<JokeResponse>  response = jokesClient.getJokes(1).execute();

        assertTrue(response.isSuccessful());

        JokeResponse result = response.body();
        //print jokes to the console
        System.out.println("\nGot one joke:");
        for (JokeItem jokeItem : result.getValue()) {
            System.out.println(jokeItem.getJoke());
        }
        assertEquals(1, result.getValue().size());
    }

    @Test
    public void testManyJokes() throws Exception {

        Response<JokeResponse>  response = jokesClient.getJokes(20).execute();

        assertTrue(response.isSuccessful());

        JokeResponse result = response.body();
        //print jokes to the console
        System.out.println("\nGot a lot of jokes:");
        for (JokeItem jokeItem : result.getValue()) {
            System.out.println(jokeItem.getJoke());
        }
        assertEquals(20, result.getValue().size());
    }
}
