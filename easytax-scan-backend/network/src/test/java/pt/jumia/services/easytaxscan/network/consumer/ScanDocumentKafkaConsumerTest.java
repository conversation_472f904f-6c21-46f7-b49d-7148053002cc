package pt.jumia.services.easytaxscan.network.consumer;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import pt.jumia.services.easytaxscan.domain.BillRequester;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.kafka.ScanDocumentPayload;
import pt.jumia.services.easytaxscan.domain.usecases.document.UpdateDocumentUseCase;
import pt.jumia.services.easytaxscan.network.kafka.consumer.ScanDocumentKafkaConsumer;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ScanDocumentKafkaConsumerTest {

    @Mock
    private UpdateDocumentUseCase updateDocumentUseCase;

    @Mock
    private BillRequester BillRequester;

    @InjectMocks
    private ScanDocumentKafkaConsumer scanDocumentKafkaConsumer;

    private ScanDocumentPayload payload;
    private Document document;
    private final String topicName = "easytax-scan-document-events";

    @BeforeEach
    void setUp() {
        document = new Document();
        document.setId(1L);
        document.setRequestPayload("sample payload");

        payload = ScanDocumentPayload.builder()
                .document(document)
                .scanId(100L)
                .event("SCAN_DOCUMENT")
                .build();

        ReflectionTestUtils.setField(scanDocumentKafkaConsumer, "topicName", topicName);
    }

    @Test
    void shouldProcessDocumentSuccessfully() {
        // Given
        when(BillRequester.sendDocumentToBill(document.getRequestPayload(), document))
                .thenReturn(true);

        // When
        scanDocumentKafkaConsumer.consume(payload);

        // Then
        verify(BillRequester).sendDocumentToBill(document.getRequestPayload(), document);
        verify(updateDocumentUseCase).updateStatus(document, Document.Status.SUBMITTED);
    }

    @Test
    void shouldHandleUnsuccessfulDocumentSubmission() {
        // Given
        when(BillRequester.sendDocumentToBill(document.getRequestPayload(), document))
                .thenReturn(false);

        // When
        scanDocumentKafkaConsumer.consume(payload);

        // Then
        verify(BillRequester).sendDocumentToBill(document.getRequestPayload(), document);
        verify(updateDocumentUseCase).updateStatus(document, Document.Status.FAILED);
    }

    @Test
    void shouldHandleExceptionDuringProcessing() {
        // Given
        when(BillRequester.sendDocumentToBill(any(), any()))
                .thenThrow(new RuntimeException("Test exception"));

        // When
        scanDocumentKafkaConsumer.consume(payload);

        // Then
        verify(BillRequester).sendDocumentToBill(document.getRequestPayload(), document);
        verify(updateDocumentUseCase).updateStatus(document, Document.Status.ERROR);
    }

    @Test
    void shouldNotModifyDocumentBeforeUpdatingStatus() {
        // Given
        when(BillRequester.sendDocumentToBill(document.getRequestPayload(), document))
                .thenReturn(true);

        doAnswer(invocation -> {
            Document doc = invocation.getArgument(0);
            Document.Status status = invocation.getArgument(1);

            assertNull(doc.getStatus(), "Document status should not be modified before calling updateStatus");
            return null;
        }).when(updateDocumentUseCase).updateStatus(any(), any());

        // When
        scanDocumentKafkaConsumer.consume(payload);

        // Then
        verify(updateDocumentUseCase).updateStatus(document, Document.Status.SUBMITTED);
    }

    private void assertNull(Object obj, String message) {
        if (obj != null) {
            throw new AssertionError(message);
        }
    }
}