package pt.jumia.services.easytaxscan.network.producer;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.kafka.ScanDocumentPayload;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;
import pt.jumia.services.easytaxscan.network.kafka.producer.ScanDocumentKafkaProducer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ScanDocumentKafkaProducerTest {

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    @Mock
    private JsonUtils jsonUtils;

    @InjectMocks
    private ScanDocumentKafkaProducer kafkaProducer;

    @Captor
    private ArgumentCaptor<String> topicCaptor;

    @Captor
    private ArgumentCaptor<String> keyCaptor;

    @Captor
    private ArgumentCaptor<String> valueCaptor;

    private final String topicName = "test-document-topic";
    private ScanDocumentPayload payload;
    private String serializedPayload;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(kafkaProducer, "topicName", topicName);

        Document document = new Document();
        document.setId(1L);
        document.setRequestPayload("test request payload");

        payload = ScanDocumentPayload.builder()
                .document(document)
                .scanId(100L)
                .event("SCAN_DOCUMENT")
                .build();

        serializedPayload = "{\"document\":{\"id\":1,\"requestPayload\":\"test request payload\"},\"scanId\":100,\"event\":\"SCAN_DOCUMENT\"}";
        when(jsonUtils.toJsonOrNull(payload)).thenReturn(serializedPayload);
    }

    @Test
    void shouldSendMessageToKafka() {
        // Given
        when(kafkaTemplate.send(anyString(), anyString(), anyString())).thenReturn(null);

        // When
        kafkaProducer.sendMessage(payload);

        // Then
        verify(kafkaTemplate).send(topicCaptor.capture(), keyCaptor.capture(), valueCaptor.capture());
        assertEquals(topicName, topicCaptor.getValue());
        assertEquals(payload.toString(), keyCaptor.getValue());
        assertEquals(serializedPayload, valueCaptor.getValue());
    }

    @Test
    void shouldUsePayloadToStringAsKey() {
        // Given
        String expectedKey = payload.toString();
        when(kafkaTemplate.send(anyString(), anyString(), anyString())).thenReturn(null);

        // When
        kafkaProducer.sendMessage(payload);

        // Then
        verify(kafkaTemplate).send(any(), eq(expectedKey), any());
    }

    @Test
    void shouldUseCorrectTopic() {
        // Given
        final String customTopic = "custom-topic";
        ReflectionTestUtils.setField(kafkaProducer, "topicName", customTopic);
        when(kafkaTemplate.send(anyString(), anyString(), anyString())).thenReturn(null);

        // When
        kafkaProducer.sendMessage(payload);

        // Then
        verify(kafkaTemplate).send(eq(customTopic), any(), any());
    }
}