package pt.jumia.services.easytaxscan.network.kafka.producer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.kafka.ScanDocumentPayload;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;
import pt.jumia.services.easytaxscan.domain.utils.KafkaProducer;


//@Component
//@Slf4j
//@RequiredArgsConstructor
public class ScanDocumentKafkaProducer implements KafkaProducer<ScanDocumentPayload> {

    //private final KafkaTemplate<String, String> kafkaTemplate;
    //private final JsonUtils jsonUtils;

    @Value("${network.kafka.streams.scan-documents.name}")
    private String topicName;

    @Override
    public void sendMessage(ScanDocumentPayload payload) {
        //log.info("Sending document to topic : {}, payload --> {}", topicName, payload);
        //this.kafkaTemplate.send(topicName, payload.toString(), jsonUtils.toJsonOrNull(payload));

    }
}
