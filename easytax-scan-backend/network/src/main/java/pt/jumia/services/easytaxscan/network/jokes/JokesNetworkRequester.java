package pt.jumia.services.easytaxscan.network.jokes;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.properties.NetworkProperties;
import pt.jumia.services.easytaxscan.network.NetworkClientFactory;
import pt.jumia.services.easytaxscan.network.jokes.payloads.JokeResponse;
import pt.jumia.services.easytaxscan.network.jokes.payloads.JokeResponse.JokeItem;
import retrofit2.Response;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class JokesNetworkRequester {

    private final JokesClient jokesClient;

    @Autowired
    public JokesNetworkRequester(NetworkProperties networkProperties, NetworkClientFactory networkClientFactory) {
        this.jokesClient = networkClientFactory.createJokesClient(networkProperties.getJokes().getUrl());
    }

    public List<String> fetchJokes(int num) throws IOException {
        Response<JokeResponse> response = jokesClient.getJokes(num).execute();
        if (response.isSuccessful()) {

            return response.body().getValue()
                    .stream()
                    .map(JokeItem::getJoke)
                    .collect(Collectors.toList());
        }

        throw new JokesErrorException(response);
    }
}
