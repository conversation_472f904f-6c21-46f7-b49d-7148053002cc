package pt.jumia.services.easytaxscan.network.jokes.payloads;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Package event representation from HMT
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JokeResponse {

    private String type;
    private List<JokeItem> value;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JokeItem {

        private int id;
        private String joke;
    }
}
