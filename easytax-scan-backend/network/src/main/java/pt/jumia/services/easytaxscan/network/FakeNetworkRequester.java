package pt.jumia.services.easytaxscan.network;

import java.util.Arrays;
import java.util.List;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.NetworkRequester;
import pt.jumia.services.easytaxscan.domain.Profiles;

/**
 * Fake implementation of the {@link NetworkRequester}, which can be used for tests and dev environment, when you want
 * to mock the network requests and inject data at your will
 */
@Component
@Profile(Profiles.FAKE_CLIENTS)
public class FakeNetworkRequester implements NetworkRequester {

    @Override
    public List<String> fetchJokes(int num) {
        return Arrays.asList("joke1", "joke2", "joke3");
    }

}
