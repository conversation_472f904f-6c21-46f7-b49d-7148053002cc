package pt.jumia.services.easytaxscan.network.kafka.consumer;

import com.newrelic.api.agent.Trace;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.BillRequester;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.kafka.ScanDocumentPayload;
import pt.jumia.services.easytaxscan.domain.usecases.document.UpdateDocumentUseCase;

//@Component
//@Slf4j
//@RequiredArgsConstructor
public class ScanDocumentKafkaConsumer {

    //private final UpdateDocumentUseCase updateDocumentUseCase;
    //private final BillRequester billRequester;

    @Value("${network.kafka.streams.scan-documents.name}")
    private String topicName;

    @Trace(dispatcher = true)
    @KafkaListener(
            topics = "${network.kafka.streams.scan-documents.name}",
            autoStartup = "${network.kafka.streams.scan-documents.auto-start}",
            groupId = "${network.kafka.streams.scan-documents.group-id}",
            containerFactory = "scanDocumentListenerContainerFactory"
    )
    public void consume(@Payload ScanDocumentPayload payload) {
        /*
        log.info("Received message from topic '{}': {}", topicName, payload);
        Document.Status status;
        try {
            status = billRequester.sendDocumentToBill(payload.getDocument().getRequestPayload(),
                    payload.getDocument())
                    ? Document.Status.SUBMITTED
                    : Document.Status.FAILED;
        } catch (Exception e) {
            log.error("Error processing document: {}", e.getMessage());
            status = Document.Status.ERROR;
        }
        updateDocumentUseCase.updateStatus(payload.getDocument(), status);
        log.info("Document {} processed with status: {}", payload.getDocument().getId(), status);
         */
    }
}

