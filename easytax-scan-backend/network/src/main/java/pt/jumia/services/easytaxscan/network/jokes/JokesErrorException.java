package pt.jumia.services.easytaxscan.network.jokes;

import java.io.IOException;
import retrofit2.Response;

/**
 * Exception thrown when Jokes API returns an error
 */
public class JokesErrorException extends IOException {

    private static final long serialVersionUID = -2161078089133191264L;

    public JokesErrorException(String message) {
        super(message);
    }

    public JokesErrorException(Response<?> response) {
        super(response.code() + " -> " + response.message());
    }
}
