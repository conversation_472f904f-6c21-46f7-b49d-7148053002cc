package pt.jumia.services.easytaxscan.network.acl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.acl.lib.payloads.ApplicationResponsePayload;
import pt.jumia.services.acl.lib.payloads.PermissionRequestPayload;
import pt.jumia.services.acl.lib.payloads.PermissionResponsePayload;
import pt.jumia.services.acl.migrator.AclMigrator;
import pt.jumia.services.acl.migrator.callback.MigrationCallback;
import pt.jumia.services.easytaxscan.domain.Permissions;
import pt.jumia.services.easytaxscan.domain.properties.AclProperties;
import pt.jumia.services.easytaxscan.domain.properties.InfoProperties;

import java.net.HttpURLConnection;
import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class AclMigratorService {

    private static final String APPLICATION_TARGET_TYPE = "APPLICATION";

    private final InfoProperties infoProperties;
    private final AclProperties aclProperties;
    private final AclNetworkRequester aclNetworkRequester;

    public void migrate() {
        try {
            AclMigrator aclMigrator = new AclMigrator(aclNetworkRequester.getAclConnectApiClient());
            RequestUser requestUser = aclNetworkRequester.authorize(
                    aclProperties.getMigratorUser().getUsername(),
                    aclProperties.getMigratorUser().getPassword());

            aclMigrator.run(
                    requestUser,
                    aclProperties.getAppName(),
                    infoProperties.getBuild().getVersion(),
                    createMigrationCallbacks()
            );
        } catch (AclErrorException e) {
            if (e.getCode() == HttpURLConnection.HTTP_FORBIDDEN) {
                log.error("Application {} does not have the necessary permissions to create new permissions. Migrations were not run," +
                        " fix the application profile before trying again", aclProperties.getAppName());
            } else if (e.getCode() == HttpURLConnection.HTTP_CONFLICT) {
                log.error("Application {} has no current version stored. Migrations were not run, set the application version before" +
                        " trying again", aclProperties.getAppName());
            } else {
                log.error("Unable to authenticate user {}. Migrations were not run.", aclProperties.getMigratorUser().getUsername());
            }
        } catch (Exception e) {
            log.error("Something unexpected happened. Migrations were not run. {}", ExceptionUtils.getStackTrace(e));
        }
    }

    private Map<String, MigrationCallback> createMigrationCallbacks() {
        Map<String, MigrationCallback> migrationMap = new HashMap<>();

        migrationMap.put("0.1.0", (app, user) -> {
            log.info("Running migrations for version 0.1.0");
            createPermission(app, user, Permissions.CAN_ACCESS, "Allows the user to access EASYTAXSCAN application.", APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.SETTING_MANAGE, "Allows the user to edit settings", APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.SETTING_ACCESS, "Allows the user to access settings", APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.DATASOURCE_ACCESS, "Allows the user to access DataSource", APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.DATASOURCE_MANAGE, "Allows the user to manage DataSource", APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.SCAN_ACCESS, "Allows the user to access Scan", APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.SCAN_MANAGE, "Allows the user to manage Scan", APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.QUERY_MANAGE, "Allows the user to manage Queries", APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.QUERY_ACCESS, "Allows the user to access Queries", APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.EXECUTION_LOG_MANAGE, "Allows the user to manage Execution Logs", APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.EXECUTION_LOG_ACCESS, "Allows the user to access Execution Logs", APPLICATION_TARGET_TYPE);
            
            return true;
        });

        return migrationMap;
    }

    private void createPermission(ApplicationResponsePayload application,
                                  RequestUser requestUser,
                                  String permissionCode,
                                  String description) {

        this.createPermission(application, requestUser, permissionCode, description, APPLICATION_TARGET_TYPE);
    }

    private void createPermission(ApplicationResponsePayload application,
                                  RequestUser requestUser,
                                  String permissionCode,
                                  String description,
                                  String targetType) {

        PermissionRequestPayload permissionRequest = new PermissionRequestPayload();

        permissionRequest.setApplication(application.getId());
        permissionRequest.setCode(permissionCode);
        permissionRequest.setTargetType(targetType);
        permissionRequest.setDescription(description);

        try {
            PermissionResponsePayload permission =
                    aclNetworkRequester.getAclConnectApiClient().management().permissions().create(requestUser, permissionRequest);
            log.info("Created permission: {}", permission.toString());
        } catch (AclErrorException e) {

            if (Integer.valueOf(HttpURLConnection.HTTP_CONFLICT).equals(e.getCode())) {
                log.info("Permission {} already exists, skipping.", permissionRequest.getCode());
            } else {
                log.error("Permission creation failed: {}", e.getMessage());
                throw (e);
            }
        }
    }
}
