package pt.jumia.services.easytaxscan.network.kafka;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;


//@Data
//@Component
//@ConfigurationProperties(prefix = "network.kafka.streams")
//@Slf4j
public class KafkaTopicsConfiguration {

    private TopicProperties scanDocuments;

    //private final ConfigurableBeanFactory beanFactory;

    @PostConstruct
    public void createTopicBeans() {
/*
        List.of(scanDocuments)
                .forEach(topicConfig -> {
                    log.info("Starting to create topic beans...");
                    final String[] topics = topicConfig.name.split(" ");
                    if (!topicConfig.create) {
                        log.debug("Skipping topic configuration for '{}': create flag is false.", topicConfig.name);
                        return;
                    }
                    for (final String topic : topics) {
                        log.info("Creating topic '{}' with partitions: {} and replication: {}.",
                                topic, topicConfig.partitions, topicConfig.replication);
                        final NewTopic newTopic = new NewTopic(topic, topicConfig.partitions, topicConfig.replication);
                        beanFactory.registerSingleton("bean-" + topic, newTopic);
                        log.info("Registered topic bean: 'bean-{}'", topic);

                    }
                });
        log.info("Completed topic bean creation.");


 */
    }

    @Data
    public static class TopicProperties {
        private boolean create;
        private boolean autoStart;
        private String name;
        private int partitions = 1;
        private short replication = 1;
    }

}

