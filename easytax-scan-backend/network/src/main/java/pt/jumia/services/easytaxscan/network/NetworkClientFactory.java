package pt.jumia.services.easytaxscan.network;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.utils.LocalDateTimeDeserializer;
import pt.jumia.services.easytaxscan.network.jokes.JokesClient;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * Factory that creates our Retrofit instances, so that we can make HTTP requests
 */
@Component
public class NetworkClientFactory {

    private static final boolean LOGGING = false;

    public JokesClient createJokesClient(String endpoint) {
        return createRetrofitInstance(endpoint, createOkHttpInstance(LOGGING))
                .create(JokesClient.class);
    }

    public <T> T createClient(String endpoint, final Class<T> client, boolean logging) {
        return createRetrofitInstance(endpoint, createOkHttpInstance(logging))
                .create(client);
    }

    @NotNull
    private OkHttpClient.Builder createOkHttpInstance(boolean logging) {
        HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor();
        interceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .readTimeout(30, TimeUnit.SECONDS);
        if (!logging) {
            return builder;
        } else {
            return builder
                    .addInterceptor(interceptor);
        }
    }

    @NotNull
    private Retrofit createRetrofitInstance(String endpoint, OkHttpClient.Builder okHttpBuilder) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // Create and register a custom module for LocalDateTime
        SimpleModule customModule = new SimpleModule();
        customModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer());
        objectMapper.registerModule(customModule);

        return new Retrofit.Builder()
                .baseUrl(endpoint)
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .client(okHttpBuilder.build()).build();
    }

}
