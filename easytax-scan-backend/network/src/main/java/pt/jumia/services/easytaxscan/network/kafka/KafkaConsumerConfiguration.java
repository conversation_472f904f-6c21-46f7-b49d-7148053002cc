package pt.jumia.services.easytaxscan.network.kafka;


import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import pt.jumia.services.easytaxscan.domain.entities.kafka.ScanDocumentPayload;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

//@Configuration
public class KafkaConsumerConfiguration {

    private <T> ConsumerFactory<String, T> getConsumerFactory(Class<T> payloadClass, KafkaProperties properties) {
        return new DefaultKafkaConsumerFactory<>(
                properties.buildConsumerProperties(null),
                new StringDeserializer(),
                new JsonDeserializer<>(payloadClass, false)
        );
    }

    private <T> ConcurrentKafkaListenerContainerFactory<String, T> getListenerContainerFactory(ConsumerFactory<String, T> consumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, T> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        return factory;
    }

    /**
     * Scan Documents consumer
     */
    @Bean
    public ConsumerFactory<String, ScanDocumentPayload> scanDocumentConsumerFactory
    (@Qualifier(AppConstants.SCAN_DOCUMENTS) KafkaProperties properties) {

        return this.getConsumerFactory(ScanDocumentPayload.class, properties);
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, ScanDocumentPayload> scanDocumentListenerContainerFactory(
            @Qualifier("scanDocumentConsumerFactory")
            ConsumerFactory<String, ScanDocumentPayload> consumerFactory
    ) {
        return this.getListenerContainerFactory(consumerFactory);
    }

}
