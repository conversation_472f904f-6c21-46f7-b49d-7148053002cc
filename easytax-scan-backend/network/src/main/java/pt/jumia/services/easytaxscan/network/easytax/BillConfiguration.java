package pt.jumia.services.easytaxscan.network.easytax;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Base64;

@Data
@ConfigurationProperties(prefix = "network.bill")
@Configuration
public class BillConfiguration {
    private static final String BASIC = "Basic ";
    private String url;
    private String userName;
    private String password;


    public String getAuth() {
        String encoding = Base64.getEncoder().encodeToString((userName + ":" + password).getBytes());
        return BASIC + encoding;

    }
}
