package pt.jumia.services.easytaxscan.network.bill;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.BillRequester;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.bill.RequestResult;
import pt.jumia.services.easytaxscan.network.easytax.BillConfiguration;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

@Component
@RequiredArgsConstructor
@Slf4j
public class BillNetworkRequester implements BillRequester {

    private final BillConfiguration billConfiguration;

    @Override
    public RequestResult sendDocumentToBill(String payload, Document document) {
        if (document == null || document.getId() == null) {
            log.warn("Document is null or missing ID. Cannot proceed with Bill API submission.");
            return RequestResult.builder()
                    .status(RequestResult.Status.ERROR)
                    .errorDescription("Document is null or missing ID. Cannot proceed with Bill API submission.")
                    .build();
        }
        try {
            Response<ResponseBody> response = callBillApi(document);
            return RequestResult.builder()
                    .errorCode(String.valueOf(response.code()))
                    .errorDescription(response.errorBody().string())
                    .status(HttpStatus.CREATED.value() == response.code() ? RequestResult.Status.OK : RequestResult.Status.ERROR)
                    .build();

        } catch (Exception e) {
            log.error("Exception occurred while submitting document to Bill API and Exception is : {} ", ExceptionUtils.getStackTrace(e));
            return RequestResult.builder()
                    .status(RequestResult.Status.ERROR)
                    .errorDescription(ExceptionUtils.getStackTrace(e))
                    .build();
        }
    }

    private Response<ResponseBody> callBillApi(Document document) throws Exception {
        Retrofit retrofit = createConnection(billConfiguration);
        BillClient billClient = retrofit.create(BillClient.class);

        RequestBody requestBody = RequestBody.create(
                document.getRequestPayload(),
                MediaType.parse("application/json")
        );
        Call<ResponseBody> call = billClient.submitDocument(billConfiguration.getAuth(), requestBody);
        return call.execute();
    }

    private Retrofit createConnection(BillConfiguration config) {
        return new Retrofit.Builder()
                .baseUrl(config.getUrl())
                .addConverterFactory(GsonConverterFactory.create())
                .build();
    }
}
