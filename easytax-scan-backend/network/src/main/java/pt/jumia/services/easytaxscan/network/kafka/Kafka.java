package pt.jumia.services.easytaxscan.network.kafka;


import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;

import java.util.Locale;

public class Kafka {
    public static final String EVENT_HEADER = "event";
    public static final String TYPE_HEADER = "type";
    public static final String USERNAME = "EASYTAXSCAN";

    public static <T extends Enum<T>> T getEventHeader(String eventValue, Class<T> clazz) throws NotFoundException {
        if (eventValue != null) {
            try {
                return Enum.valueOf(clazz, eventValue.toUpperCase(Locale.ROOT));
            } catch (IllegalArgumentException ex) {
                throw NotFoundException.build(clazz, "type", eventValue);
            }
        } else {
            throw NotFoundException.build(clazz, "type", EVENT_HEADER);
        }
    }
}
