package pt.jumia.services.easytaxscan.network.bill;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;

public interface BillClient {

    @Headers("Content-Type: application/json")
    @POST("/api/documents")
    Call<ResponseBody> submitDocument(
            @Header(AUTHORIZATION) String auth,
            @Body RequestBody body);

}
