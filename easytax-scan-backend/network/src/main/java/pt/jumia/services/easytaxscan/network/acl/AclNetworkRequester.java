package pt.jumia.services.easytaxscan.network.acl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.acl.lib.*;
import pt.jumia.services.acl.lib.cache.InMemoryCacheStrategy;
import pt.jumia.services.acl.lib.cache.RedisCacheStrategy;
import pt.jumia.services.acl.lib.client.authorization.DefaultAuthorizationClient;
import pt.jumia.services.acl.lib.payloads.AclLoginRequest;
import pt.jumia.services.acl.lib.payloads.AclTokenResponse;
import pt.jumia.services.easytaxscan.domain.AccessController;
import pt.jumia.services.easytaxscan.domain.properties.AclProperties;
import pt.jumia.services.easytaxscan.domain.properties.AclProperties.Cache;
import pt.jumia.services.easytaxscan.domain.properties.AclProperties.Cache.InMemory;
import pt.jumia.services.easytaxscan.domain.properties.AclProperties.Cache.Redis;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Implements all the interactions with ACL Module
 */
@Component
public class AclNetworkRequester implements AccessController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AclNetworkRequester.class);

    private final AclConnectApiClient<DefaultAuthorizationClient> aclConnectApiClient;
    private final AclInstance aclInstance;

    @Autowired
    public AclNetworkRequester(AclProperties aclProperties) {
        this.aclInstance = createAclInstance(aclProperties);
        this.aclConnectApiClient = createAclConnectApiClient(aclProperties, aclInstance);
    }

    private AclInstance createAclInstance(AclProperties aclProperties) {
        return AclInstance.of(aclProperties.getUrl());
    }

    private AclConnectApiClient<DefaultAuthorizationClient> createAclConnectApiClient(AclProperties aclProperties, AclInstance aclInstance) {
        return new AclConnectApiClientBuilder()
                .addAclInstance(aclInstance)
                .setApplicationCode(aclProperties.getAppName())
                .setLogger(new AclLogger())
                .setCacheStrategy(chooseCacheStrategy(aclProperties.getCache()))
                .buildAsDefaultAuthClient();
    }

    private CacheStrategy chooseCacheStrategy(Cache cache) {
        if (cache == null) {
            return null;
        }
        switch (cache.getStrategy()) {
            case "redis":
                return createRedisCache(cache.getRedis());
            case "in-memory":
                return createInMemoryCache(cache.getInMemory());
            default:
                return null;
        }
    }

    private CacheStrategy createRedisCache(Redis redis) {
        return new RedisCacheStrategy.Builder()
            .setHost(redis.getHost())
            .setPort(redis.getPort())
            .setUsernamePrefix(redis.getUsernameKeyPrefix())
            .setPassword(redis.getPassword())
            .setExpireDuration(redis.getExpirationDuration().getSeconds(), TimeUnit.SECONDS)
            .setTimeout(redis.getTimeout().getSeconds(), TimeUnit.SECONDS)
            .build();
    }

    private CacheStrategy createInMemoryCache(InMemory inMemory) {
        return InMemoryCacheStrategy.newInstance(inMemory.getExpirationDuration().getSeconds(), TimeUnit.SECONDS);
    }

    AclConnectApiClient<DefaultAuthorizationClient> getAclConnectApiClient() {
        return aclConnectApiClient;
    }


    public Map<String, Map<String, List<String>>> getPermissions(RequestUser requestUser) {
        return aclConnectApiClient.authorization().getPermissions(requestUser, requestUser.getUsername());
    }

    public List<String> getPermissionTargets(RequestUser requestUser, String permissionCode) {
        return aclConnectApiClient.authorization().getPermissionTargets(requestUser, requestUser.getUsername(), permissionCode);
    }

    public boolean hasPermission(RequestUser requestUser, String permission) {
        return aclConnectApiClient.authorization().hasPermission(requestUser, requestUser.getUsername(), permission);
    }

    public RequestUser decodeToken(String userToken) {
        RequestUser requestUser = aclConnectApiClient.authentication().decodeToken(userToken);
        return requestUser;
    }

    public AclTokenResponse login(String providerName, AclLoginRequest requestPayload) {
        return aclConnectApiClient.authentication().login(providerName, requestPayload);
    }

    public void logout(RequestUser requestUser) {
        aclConnectApiClient.authentication().logout(requestUser);
    }

    public RequestUser authorize(String username, String password) {
        return aclConnectApiClient.authentication().authorize(username, password);
    }

    public String findRealToken(String temporaryToken) {
        return this.aclConnectApiClient.authentication().tempTokenSwap(this.aclInstance, temporaryToken);
    }
}
