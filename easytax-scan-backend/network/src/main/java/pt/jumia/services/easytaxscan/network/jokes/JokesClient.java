package pt.jumia.services.easytaxscan.network.jokes;

import pt.jumia.services.easytaxscan.network.jokes.payloads.JokeResponse;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Path;

/**
 * Jokes api client with all the interactions we have with them
 */
public interface JokesClient {

    @GET("/jokes/random/{num}")
    Call<JokeResponse> getJokes(@Path("num") int numJokes);
}
