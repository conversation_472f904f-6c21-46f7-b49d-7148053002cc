package pt.jumia.services.easytaxscan.network.kafka;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.util.Map;

//@Configuration
public class KafkaProducerConfiguration {

    @Bean
    public KafkaTemplate<String, String> scanDocumentsKafkaTemplate
            (@Qualifier(AppConstants.SCAN_DOCUMENTS) KafkaProperties properties) {
        return new KafkaTemplate<>(scanDocumentsProducerFactory(properties));
    }

    @Bean
    public ProducerFactory<String, String> scanDocumentsProducerFactory(KafkaProperties properties) {
        Map<String, Object> producerProps = properties.buildProducerProperties(null);
        return new DefaultKafkaProducerFactory<>(producerProps);
    }


    @Bean
    @ConfigurationProperties("network.kafka.clusters.scan-documents")
    @Qualifier(AppConstants.SCAN_DOCUMENTS)
    @Primary
    public KafkaProperties scanDocuments() {
        return new KafkaProperties();
    }
}
