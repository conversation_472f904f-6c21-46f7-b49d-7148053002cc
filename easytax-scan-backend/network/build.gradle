apply plugin: 'idea'
apply from: '../config/quality/quality.gradle'


configurations {
    all {
        exclude group: 'ch.qos.logback', module: 'logback-classic'
        exclude group: 'org.apache.logging.log4j', module: 'log4j-slf4j2-impl'
    }
}

dependencies {

    implementation project(':domain')
    api("org.springframework.boot:spring-boot-starter-web:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
        // Exclude Logback
        exclude group: 'ch.qos.logback', module: 'logback-classic'
        // Exclude Log4j SLF4J binding
        exclude group: 'org.apache.logging.log4j', module: 'log4j-slf4j2-impl'
    }

    // ACL Migrator
    implementation group: 'com.jumia.services', name: 'acl-migrator', version: "${aclMigratorVersion}"

    implementation "org.springframework.boot:spring-boot:${springBootVersion}"

    //implementation "org.springframework:spring-web:${springVersion}"


    // spotbugs annotations, so that lombok can add suppress warnings in generated code
    implementation group: 'com.github.spotbugs', name: 'spotbugs-annotations', version: "${spotbugsVersion}"

    //basic utils, mainly for Base64
    implementation group: 'pt.aig.aigx', name: 'aigx-elasticsearch-components', version: '3.3.4-RELEASE'

    implementation "pt.aig.aigx:logging-context:0.3.0-RELEASE"

    //new relic, for monitoring
    // https://mvnrepository.com/artifact/com.newrelic.agent.java/newrelic-api
    implementation group: 'com.newrelic.agent.java', name: 'newrelic-api', version: '7.11.0'

    testImplementation ("org.springframework.boot:spring-boot-starter-test:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
        // Exclude Logback
        exclude group: 'ch.qos.logback', module: 'logback-classic'
        // Exclude Log4j SLF4J binding
        exclude group: 'org.apache.logging.log4j', module: 'log4j-slf4j2-impl'
    }
    //basic utils, mainly for Base64
    implementation group: 'commons-codec', name: 'commons-codec', version: '1.15'

    //retrofit for network requests
    implementation group: 'com.squareup.retrofit2', name: 'retrofit', version: '2.9.0'
    implementation "com.squareup.okhttp3:logging-interceptor:4.10.0"
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    testImplementation "com.squareup.retrofit2:retrofit-mock:2.9.0"
    implementation 'com.squareup.retrofit2:converter-jackson:2.9.0'
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.4"
    implementation "com.squareup.retrofit2:converter-simplexml:2.9.0"

    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.15.0'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.0'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.0'

    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
    testImplementation "org.mockito:mockito-junit-jupiter:${mockitoVersion}"
    // aws for S3 file management
    implementation platform('software.amazon.awssdk:bom:2.13.17')
    implementation 'software.amazon.awssdk:s3'

    //kafka
    // https://mvnrepository.com/artifact/org.springframework.kafka/spring-kafka
    implementation group: 'org.springframework.kafka', name: 'spring-kafka', version: "${kafkaVersion}"

    implementation 'org.apache.kafka:kafka-clients:3.7.0'
}
