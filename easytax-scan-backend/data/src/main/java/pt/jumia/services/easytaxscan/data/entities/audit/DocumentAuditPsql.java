package pt.jumia.services.easytaxscan.data.entities.audit;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RevisionType;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.Mode;

import java.time.LocalDateTime;

@Entity
@Audited
@Table(name = "document_aud", schema = "audit")
@NoArgsConstructor
@Data
public class DocumentAuditPsql {


    @EmbeddedId
    private DefaultAudIdPsql id;

    @Column(name = "sid", nullable = false)
    private String sid;


    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "mode", nullable = false)
    private String mode;

    @Column(name = "query_data", nullable = false)
    private String queryData;

    @Column(name = "request_payload", nullable = false)
    private String requestPayload;


    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @Enumerated(EnumType.ORDINAL)
    @Column(name = "revtype")
    private RevisionType revType;

    // Convert back to Document domain model
    public Document toEntity(ExecutionLog executionLog) {
        return Document.builder()
                .id(this.id.getId())
                .sid(sid)
                .mode(mode != null ? Mode.valueOf(mode) : null)
                .requestPayload(requestPayload)
                .status(Document.Status.valueOf(status))
                .queryData(queryData)
                .executionLog(executionLog)
                .createdBy(createdBy)
                .createdAt(createdAt)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .build();
    }
}
