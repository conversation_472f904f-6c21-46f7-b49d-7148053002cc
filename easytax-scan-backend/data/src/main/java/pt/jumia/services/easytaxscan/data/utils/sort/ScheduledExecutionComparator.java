package pt.jumia.services.easytaxscan.data.utils.sort;

import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.ScheduledExecution;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScheduledExecutionSortFilters;

import java.util.Comparator;

public class ScheduledExecutionComparator implements Comparator<ScheduledExecution> {

    private final ScheduledExecutionSortFilters sortFilters;

    public ScheduledExecutionComparator(ScheduledExecutionSortFilters sortFilters) {
        this.sortFilters = sortFilters;
    }

    @Override
    public int compare(ScheduledExecution o1, ScheduledExecution o2) {
        Comparator<ScheduledExecution> comparator = switch (sortFilters.getField()) {
            case STATUS -> Comparator.comparing(se -> se.getStatus().name(), Comparator.nullsFirst(String::compareTo));
            case NEXT_FIRE_TIME ->
                    Comparator.comparing(ScheduledExecution::getNextFireTimeLocal, Comparator.nullsFirst(Comparable::compareTo));
        };
        if (OrderDirection.DESC.equals(sortFilters.getDirection())) {
            comparator = comparator.reversed();
        }
        return comparator.compare(o1, o2);
    }
}
