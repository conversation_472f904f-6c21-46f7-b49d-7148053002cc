package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.easytaxscan.data.audit.RevisionTypeConverter;
import pt.jumia.services.easytaxscan.data.entities.QRevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.RevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.QScanAudPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.ScanAudPsql;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.ScanAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.ScanAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.scan.ReadScanUseCase;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class PsqlScanAuditRepository implements ScanAuditRepository {

    private static final QScanAudPsql Q_SCAN_AUD_PSQL = QScanAudPsql.scanAudPsql;
    private static final QRevisionPsql Q_REVISION_PSQL = QRevisionPsql.revisionPsql;

    private final EntityManager entityManager;
    private final ReadScanUseCase readScanUseCase;

    public PsqlScanAuditRepository(EntityManager entityManager, ReadScanUseCase readScanUseCase) {
        this.entityManager = entityManager;
        this.readScanUseCase = readScanUseCase;

    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditedEntity<Scan>> getScanAuditLogById(ScanAuditFilters filters, Scan scan) {
        JPAQuery<Tuple> query = baseQuery(filters)
                .select(Q_SCAN_AUD_PSQL, Q_REVISION_PSQL);

        return query.fetch().stream().map(tuple -> {
            RevisionPsql revisionPsql = tuple.get(Q_REVISION_PSQL);
            ScanAudPsql scanAudPsql = tuple.get(Q_SCAN_AUD_PSQL);

            AuditedEntity.OperationType operationType = RevisionTypeConverter.toOperationType(
                    Objects.requireNonNull(scanAudPsql).getRevType());

            assert scanAudPsql != null;
            assert revisionPsql != null;
            return AuditedEntity.<Scan>builder()
                    .auditedEntity(AuditedEntities.SCAN)
                    .operationType(operationType)
                    .revisionInfo(revisionPsql.toEntity())
                    .entity(scanAudPsql.toEntity())
                    .build();
        }).collect(Collectors.toList());
    }

    private JPAQuery<?> baseQuery(ScanAuditFilters filters) {
        JPAQuery<?> query = new JPAQueryFactory(entityManager)
                .from(Q_SCAN_AUD_PSQL)
                .innerJoin(Q_REVISION_PSQL).on(Q_SCAN_AUD_PSQL.id.rev.eq(Q_REVISION_PSQL.id));

        if (Objects.nonNull(filters.getId())) {
            query.where(Q_SCAN_AUD_PSQL.id.id.eq(filters.getId()));
        }

        AuditRepositoryHelper.applyGenericFiltersAndSorting(query, Q_REVISION_PSQL, filters);
        return query;
    }

    @Override
    public long getAuditLogCountById(ScanAuditFilters filters) {
        return baseQuery(filters).fetchCount();
    }
}
