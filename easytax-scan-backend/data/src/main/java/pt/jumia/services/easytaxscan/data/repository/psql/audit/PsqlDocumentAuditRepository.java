package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.easytaxscan.data.audit.RevisionTypeConverter;
import pt.jumia.services.easytaxscan.data.entities.QRevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.RevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.DocumentAuditPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.QDocumentAuditPsql;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.DocumentAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.DocumentAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.document.ReadDocumentUseCase;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class PsqlDocumentAuditRepository implements DocumentAuditRepository {

    private static final QDocumentAuditPsql Q_DOCUMENT_AUDIT_PSQL = QDocumentAuditPsql.documentAuditPsql;
    private static final QRevisionPsql Q_REVISION_PSQL = QRevisionPsql.revisionPsql;

    private final EntityManager entityManager;

    private final ReadDocumentUseCase readDocumentUseCase;


    public PsqlDocumentAuditRepository(EntityManager entityManager, ReadDocumentUseCase readDocumentUseCase) {
        this.entityManager = entityManager;
        this.readDocumentUseCase = readDocumentUseCase;
    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditedEntity<Document>> getDocumentAuditLogById(DocumentAuditFilters filters, Document document) {

        JPAQuery<Tuple> queryFetch = baseQuery(filters)
                .select(Q_DOCUMENT_AUDIT_PSQL, Q_REVISION_PSQL);

        return queryFetch.fetch().stream().map(tuple -> {
            RevisionPsql revisionPsql = tuple.get(Q_REVISION_PSQL);
            DocumentAuditPsql documentAuditPsql = tuple.get(Q_DOCUMENT_AUDIT_PSQL);

            AuditedEntity.OperationType operationType = RevisionTypeConverter.toOperationType(
                    Objects.requireNonNull(tuple.get(Q_DOCUMENT_AUDIT_PSQL)).getRevType());

            assert documentAuditPsql != null;
            assert revisionPsql != null;
            return AuditedEntity.<Document>builder()
                    .auditedEntity(AuditedEntities.DOCUMENT)
                    .operationType(operationType)
                    .revisionInfo(revisionPsql.toEntity())
                    .entity(documentAuditPsql.toEntity(
                            document.getExecutionLog()
                    ))
                    .build();
        }).collect(Collectors.toList());
    }

    private JPAQuery<?> baseQuery(DocumentAuditFilters filters) {
        JPAQuery<?> query = new JPAQueryFactory(entityManager)
                .from(Q_DOCUMENT_AUDIT_PSQL)
                .innerJoin(Q_REVISION_PSQL).on(Q_DOCUMENT_AUDIT_PSQL.id.rev.eq(Q_REVISION_PSQL.id));

        if (Objects.nonNull(filters.getId())) {
            query.where(Q_DOCUMENT_AUDIT_PSQL.id.id.eq(filters.getId()));
        }
        AuditRepositoryHelper.applyGenericFiltersAndSorting(query, Q_REVISION_PSQL, filters);

        return query;
    }

    @Override
    public long getAuditLogCountById(DocumentAuditFilters filters) {
        return baseQuery(filters).fetchCount();
    }
}
