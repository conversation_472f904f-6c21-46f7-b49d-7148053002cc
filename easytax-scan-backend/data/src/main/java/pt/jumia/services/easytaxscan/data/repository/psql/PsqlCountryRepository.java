package pt.jumia.services.easytaxscan.data.repository.psql;

import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.easytaxscan.data.entities.CountryPsql;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaCountryRepository;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.CountryRepository;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.util.Optional;

@Slf4j
@Repository
public class PsqlCountryRepository extends PsqlRepository implements CountryRepository {

    private final JpaCountryRepository jpaCountryRepository;
    private final EntityManager entityManager;

    public PsqlCountryRepository(TransactionTemplate transactionTemplate,
                                 JpaCountryRepository jpaCountryRepository, EntityManager entityManager) {
        super(transactionTemplate);
        this.jpaCountryRepository = jpaCountryRepository;
        this.entityManager = entityManager;
    }

    @Override
    @Transactional
    public Country insert(Country country) {
        return jpaCountryRepository.save(new CountryPsql(country)).toEntity();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Country> findById(Long id) {
        return jpaCountryRepository.findById(id).map(CountryPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Country> findByCode(String code) {
        return jpaCountryRepository.findByCountryCode(code)
                .map(CountryPsql::toEntity);
    }

    @Override
    @Transactional
    public Country update(long id, Country updateCountry) {
        CountryPsql countryPsql = jpaCountryRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(Country.class, id));

        countryPsql.setCountryName(updateCountry.getCountryName());
        countryPsql.setUpdatedBy(RequestContext.getUsername());
        countryPsql.setUpdatedAt(DateUtils.getCurrentTimeStamp());
        return jpaCountryRepository.save(countryPsql).toEntity();
    }

    @Override
    @Transactional
    public void deleteById(long id) {
        jpaCountryRepository.deleteById(id);
    }
}
