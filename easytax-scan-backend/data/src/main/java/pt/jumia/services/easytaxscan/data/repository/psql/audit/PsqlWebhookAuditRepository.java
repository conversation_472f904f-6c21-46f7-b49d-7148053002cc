package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.easytaxscan.data.audit.RevisionTypeConverter;
import pt.jumia.services.easytaxscan.data.entities.QRevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.RevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.QWebhookAuditPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.WebhookAuditPsql;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.WebhookAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.WebhookAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.webhook.ReadWebhooksUseCase;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class PsqlWebhookAuditRepository implements WebhookAuditRepository {

    private static final QWebhookAuditPsql Q_WEBHOOK_AUDIT_PSQL = QWebhookAuditPsql.webhookAuditPsql;
    private static final QRevisionPsql Q_REVISION_PSQL = QRevisionPsql.revisionPsql;

    private final EntityManager entityManager;

    private final ReadWebhooksUseCase readWebhooksUseCase;


    public PsqlWebhookAuditRepository(EntityManager entityManager, ReadWebhooksUseCase readWebhooksUseCase) {
        this.entityManager = entityManager;
        this.readWebhooksUseCase = readWebhooksUseCase;
    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditedEntity<WebHooks>> getWebhookAuditLogById(WebhookAuditFilters filters, WebHooks webHooks) {

        JPAQuery<Tuple> queryFetch = baseQuery(filters)
                .select(Q_WEBHOOK_AUDIT_PSQL, Q_REVISION_PSQL);

        return queryFetch.fetch().stream().map(tuple -> {
            RevisionPsql revisionPsql = tuple.get(Q_REVISION_PSQL);
            WebhookAuditPsql webhookAuditPsql = tuple.get(Q_WEBHOOK_AUDIT_PSQL);

            AuditedEntity.OperationType operationType = RevisionTypeConverter.toOperationType(
                    Objects.requireNonNull(tuple.get(Q_WEBHOOK_AUDIT_PSQL)).getRevType());

            assert webhookAuditPsql != null;
            assert revisionPsql != null;
            assert revisionPsql != null;
            return AuditedEntity.<WebHooks>builder()
                    .auditedEntity(AuditedEntities.WEBHOOKS)
                    .operationType(operationType)
                    .revisionInfo(revisionPsql.toEntity())
                    .entity(webhookAuditPsql.toEntity(
                            webHooks.getDocument()
                    ))
                    .build();
        }).collect(Collectors.toList());
    }

    private JPAQuery<?> baseQuery(WebhookAuditFilters filters) {
        JPAQuery<?> query = new JPAQueryFactory(entityManager)
                .from(Q_WEBHOOK_AUDIT_PSQL)
                .innerJoin(Q_REVISION_PSQL).on(Q_WEBHOOK_AUDIT_PSQL.id.rev.eq(Q_REVISION_PSQL.id));

        if (Objects.nonNull(filters.getId())) {
            query.where(Q_WEBHOOK_AUDIT_PSQL.id.id.eq(filters.getId()));
        }
        AuditRepositoryHelper.applyGenericFiltersAndSorting(query, Q_REVISION_PSQL, filters);

        return query;
    }

    @Override
    public long getAuditLogCountById(WebhookAuditFilters filters) {
        return baseQuery(filters).fetchCount();
    }
}
