package pt.jumia.services.easytaxscan.data.repository.psql;

import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.data.entities.DocumentPsql;
import pt.jumia.services.easytaxscan.data.entities.QWebhooksPsql;
import pt.jumia.services.easytaxscan.data.entities.WebhooksPsql;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaDocumentRepository;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaWebhooksRepository;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.filter.WebhooksSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.WebhooksRepository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsFirst;
import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsLast;

@Repository
public class PsqlWebhooksRepository extends PsqlRepository implements WebhooksRepository {

    private final EntityManager entityManager;

    private final JpaWebhooksRepository jpaWebhooksRepository;

    private final JpaDocumentRepository jpaDocumentRepository;

    private final QWebhooksPsql root = QWebhooksPsql.webhooksPsql;

    public PsqlWebhooksRepository(TransactionTemplate transactionTemplate,
                                  EntityManager entityManager,
                                  JpaWebhooksRepository jpaWebhooksRepository, JpaDocumentRepository jpaDocumentRepository) {

        super(transactionTemplate);
        this.entityManager = entityManager;
        this.jpaWebhooksRepository = jpaWebhooksRepository;
        this.jpaDocumentRepository = jpaDocumentRepository;
    }


    @Override
    @Transactional(readOnly = true)
    public Optional<WebHooks> findById(long id) {

        return jpaWebhooksRepository.findById(id)
                .map(WebhooksPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<WebHooks> findAll(WebhooksSortFilters webhooksSortFilters, PageFilters pageFilters) {
        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<WebhooksPsql> query = queryFactory
                .selectFrom(root)
                .distinct();
        query.orderBy(getOrderSpecifier(webhooksSortFilters))
                .offset((long) (pageFilters.getPage() - 1) * pageFilters.getSize())
                .limit(pageFilters.getSize() + 1);
        return query.fetch().stream()
                .map(WebhooksPsql::toEntity).toList();
    }

    @Override
    @Transactional(readOnly = true)
    public Long executeCount() {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);
        return Objects.requireNonNull(query.fetchOne());
    }

    @Override
    public void deleteById(long id) {
        jpaWebhooksRepository.deleteById(id);
    }

    @Override
    @Transactional
    public WebHooks insert(WebHooks webhooks) {
        DocumentPsql documentPsql = jpaDocumentRepository.findById(
                        Objects.requireNonNull(webhooks.getDocument().getId()))
                .orElseThrow(() -> NotFoundException.build(Document.class, "ID", webhooks.getDocument().getId()));
        webhooks.setDocument(documentPsql.toEntity());
        return jpaWebhooksRepository.save(new WebhooksPsql(webhooks)).toEntity();
    }

    private OrderSpecifier<?> getOrderSpecifier(WebhooksSortFilters sortFilters) {
        String field = WebhooksPsql.ENTITY_FIELDS.get(sortFilters.getField());
        if (OrderDirection.ASC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.ASC, ExpressionUtils.path(QWebhooksPsql.class, root, field), NullsFirst);
        } else if (OrderDirection.DESC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.DESC, ExpressionUtils.path(QWebhooksPsql.class, root, field), NullsLast);
        } else {
            throw new IllegalArgumentException("Order must be either ASC or DESC");
        }
    }


}
