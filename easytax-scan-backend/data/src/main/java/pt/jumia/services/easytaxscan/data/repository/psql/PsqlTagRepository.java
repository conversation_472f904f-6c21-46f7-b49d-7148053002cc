package pt.jumia.services.easytaxscan.data.repository.psql;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.easytaxscan.data.entities.TagPsql;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaTagRepository;
import pt.jumia.services.easytaxscan.domain.entities.Tag;
import pt.jumia.services.easytaxscan.domain.repository.TagRepository;

@Repository
@RequiredArgsConstructor
public class PsqlTagRepository implements TagRepository {

    private final JpaTagRepository jpaTagRepository;

    @Override
    @Transactional
    public Tag insert(Tag tag) {
        return jpaTagRepository.save(new TagPsql(tag)).toEntity();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Tag> findById(long id) {
        return jpaTagRepository.findById(id)
            .map(TagPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Tag> findAll() {
        return jpaTagRepository.findAll().stream()
            .map(TagPsql::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Tag update(Tag toUpdateTag) {
        return jpaTagRepository.save(new TagPsql(toUpdateTag)).toEntity();
    }

    @Override
    @Transactional
    public void deleteById(long id) {
        jpaTagRepository.deleteById(id);
    }
}
