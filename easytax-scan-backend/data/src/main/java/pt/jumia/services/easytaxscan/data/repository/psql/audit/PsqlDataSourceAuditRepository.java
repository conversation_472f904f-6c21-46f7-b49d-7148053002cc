package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.easytaxscan.data.audit.RevisionTypeConverter;
import pt.jumia.services.easytaxscan.data.entities.QRevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.RevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.DataSourceAuditPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.QDataSourceAuditPsql;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.DataSourceAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.DataSourceAuditRepository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class PsqlDataSourceAuditRepository implements DataSourceAuditRepository {

    private static final QDataSourceAuditPsql Q_DATASOURCE_AUDIT_PSQL = QDataSourceAuditPsql.dataSourceAuditPsql;
    private static final QRevisionPsql Q_REVISION_PSQL = QRevisionPsql.revisionPsql;

    private final EntityManager entityManager;

    public PsqlDataSourceAuditRepository(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditedEntity<DataSource>> getDataSourceAuditLogById(DataSourceAuditFilters filters, DataSource dataSource) {
        JPAQuery<Tuple> query = baseQuery(filters)
                .select(Q_DATASOURCE_AUDIT_PSQL, Q_REVISION_PSQL);

        return query.fetch().stream().map(tuple -> {
            RevisionPsql revisionPsql = tuple.get(Q_REVISION_PSQL);
            DataSourceAuditPsql dataSourceAuditPsql = tuple.get(Q_DATASOURCE_AUDIT_PSQL);

            AuditedEntity.OperationType operationType = RevisionTypeConverter.toOperationType(
                    Objects.requireNonNull(dataSourceAuditPsql).getRevType());

            assert dataSourceAuditPsql != null;
            assert revisionPsql != null;
            return AuditedEntity.<DataSource>builder()
                    .auditedEntity(AuditedEntities.DATA_SOURCE)
                    .operationType(operationType)
                    .revisionInfo(revisionPsql.toEntity())
                    .entity(dataSourceAuditPsql.toEntity())
                    .build();
        }).collect(Collectors.toList());
    }

    private JPAQuery<?> baseQuery(DataSourceAuditFilters filters) {
        JPAQuery<?> query = new JPAQueryFactory(entityManager)
                .from(Q_DATASOURCE_AUDIT_PSQL)
                .innerJoin(Q_REVISION_PSQL).on(Q_DATASOURCE_AUDIT_PSQL.id.rev.eq(Q_REVISION_PSQL.id));

        if (Objects.nonNull(filters.getId())) {
            query.where(Q_DATASOURCE_AUDIT_PSQL.id.id.eq(filters.getId()));
        }

        AuditRepositoryHelper.applyGenericFiltersAndSorting(query, Q_REVISION_PSQL, filters);
        return query;
    }

    @Override
    public long getAuditLogCountById(DataSourceAuditFilters filters) {
        return baseQuery(filters).fetchCount();
    }
}
