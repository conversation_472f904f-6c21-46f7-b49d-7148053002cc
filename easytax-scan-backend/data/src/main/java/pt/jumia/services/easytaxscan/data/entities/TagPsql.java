package pt.jumia.services.easytaxscan.data.entities;

import jakarta.persistence.*;
import org.hibernate.envers.Audited;
import pt.jumia.services.easytaxscan.domain.entities.Tag;


import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

/**
 * Postgresql pojo representation of Tag
 */
@Entity
@Audited
@Table(name = "tags")
public class TagPsql {

    @Id
    @SequenceGenerator(name = "tags_seq", sequenceName = "tags_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tags_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "name", nullable = false, unique = true)
    private String name;

    @Column(name = "description", nullable = false)
    private String description;

    @Column(name = "color")
    private String color;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    public TagPsql() {
    }

    public TagPsql(Tag tag) {
        this.id = tag.getId();
        this.name = tag.getName();
        this.description = tag.getDescription();
        this.color = tag.getColor();
        this.createdAt = Objects.nonNull(tag.getCreatedAt())
                ? tag.getCreatedAt().truncatedTo(ChronoUnit.MICROS)
                : LocalDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.MICROS);
    }

    public Tag toEntity() {
        return Tag
                .builder()
                .id(id)
                .name(name)
                .description(description)
                .color(color)
                .createdAt(createdAt)
                .build();
    }

}
