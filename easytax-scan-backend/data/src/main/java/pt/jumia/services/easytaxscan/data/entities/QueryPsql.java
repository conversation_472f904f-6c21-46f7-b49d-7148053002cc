package pt.jumia.services.easytaxscan.data.entities;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

@Entity
@Audited
@Table(name = "query")
@NoArgsConstructor
@Data
public class QueryPsql {

    public static final Map<Query.SortingFields, String> ENTITY_FIELDS;

    static {
        ENTITY_FIELDS = Map.ofEntries(
                Map.entry(Query.SortingFields.ID, "id"),
                Map.entry(Query.SortingFields.UPDATED_AT, "updatedAt")
        );
    }

    @Id
    @SequenceGenerator(name = "query_seq", sequenceName = "query_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "query_seq")
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "sql", nullable = false)
    private String sql;

    @Column(name = "code", nullable = false)
    private String code;

    @Column(name = "description", nullable = false)
    private String description;

    @Column(name = "page_size", nullable = false)
    private Integer pageSize;

    @Column(name = "pagination_field", nullable = false)
    private String paginationField;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fk_datasource")
    private DataSourcePsql dataSourcePsql;

    @Column(name = "sample_result", nullable = false)
    private String sampleResult;

    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    public QueryPsql(Query query) {
        this.id = query.getId();
        this.sql = query.getSql();
        this.code = query.getCode();
        this.description = query.getDescription();
        this.pageSize = query.getPageSize();
        this.paginationField = query.getPaginationField();
        this.dataSourcePsql = new DataSourcePsql(query.getDataSource());
        this.sampleResult = query.getSampleResult();
        this.createdBy = Objects.isNull(query.getCreatedBy()) ? RequestContext.getUsername() : query.getCreatedBy();
        this.updatedBy = Objects.isNull(query.getUpdatedBy()) ? RequestContext.getUsername() : query.getUpdatedBy();
        this.createdAt = DateUtils.getTimeStamp(query.getCreatedAt());
        this.updatedAt = DateUtils.getTimeStamp(query.getUpdatedAt());


    }

    // Convert back to Query domain model
    public Query toEntity() {
        return Query.builder()
                .id(id)
                .sql(sql)
                .code(code)
                .description(description)
                .pageSize(pageSize)
                .paginationField(paginationField)
                .dataSource(dataSourcePsql.toEntity())
                .sampleResult(sampleResult)
                .createdBy(createdBy)
                .createdAt(createdAt)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .build();
    }
}
