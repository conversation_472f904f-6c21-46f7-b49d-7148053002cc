package pt.jumia.services.easytaxscan.data.configuration;

import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.quartz.Trigger;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import pt.jumia.services.easytaxscan.domain.properties.DataProperties;

import java.io.IOException;
import java.util.Properties;

@Configuration
@RequiredArgsConstructor
public class JobSchedulerConfig implements ApplicationContextAware {
    private static final String JOB_REPEAT_INTERVAL = "0 */5 * * * ?";
    private ApplicationContext applicationContext;
    private final DataProperties dataProperties;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Bean
    public Properties quartzProperties() throws IOException {
        PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
        propertiesFactoryBean.setLocation(new ClassPathResource("quartz.properties"));
        propertiesFactoryBean.afterPropertiesSet();

        Properties quartzProperties = propertiesFactoryBean.getObject();

        quartzProperties.put("org.quartz.dataSource.quartzDataSource.driver", dataProperties.getDb().getDriver());
        quartzProperties.put("org.quartz.dataSource.quartzDataSource.URL", dataProperties.getDb().getUrl());
        quartzProperties.put("org.quartz.dataSource.quartzDataSource.user", dataProperties.getDb().getUsername());
        quartzProperties.put("org.quartz.dataSource.quartzDataSource.password", dataProperties.getDb().getPassword());

        return quartzProperties;
    }

    @Bean
    public SchedulerFactoryBean scheduler(Trigger... triggers) throws IOException {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setConfigLocation(new ClassPathResource("quartz.properties"));
        factory.setQuartzProperties(quartzProperties());
        factory.setAutoStartup(true);
        factory.setTriggers(triggers);
        factory.setApplicationContextSchedulerContextKey("applicationContext");
        return factory;
    }
}
