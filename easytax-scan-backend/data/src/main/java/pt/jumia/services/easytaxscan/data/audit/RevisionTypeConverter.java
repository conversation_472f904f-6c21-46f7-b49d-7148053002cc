package pt.jumia.services.easytaxscan.data.audit;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.hibernate.envers.RevisionType;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;

import java.util.Objects;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RevisionTypeConverter {

    public static AuditedEntity.OperationType toOperationType(RevisionType type) {
        if (Objects.isNull(type)) {
            return null;
        }
        return switch (type) {
            case ADD -> AuditedEntity.OperationType.CREATE;
            case MOD -> AuditedEntity.OperationType.UPDATE;
            case DEL -> AuditedEntity.OperationType.DELETE;
            default -> null;
        };
    }

}
