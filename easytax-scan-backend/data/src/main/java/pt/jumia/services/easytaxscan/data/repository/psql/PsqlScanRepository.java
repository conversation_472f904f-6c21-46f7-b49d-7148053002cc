package pt.jumia.services.easytaxscan.data.repository.psql;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.data.entities.*;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaQueryRepository;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaScanRepository;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsFirst;
import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsLast;

@Slf4j
@Repository
public class PsqlScanRepository extends PsqlRepository implements ScanRepository {

    private final JpaScanRepository jpaScanRepository;
    private final JpaQueryRepository jpaQueryRepository;
    private final EntityManager entityManager;
    private final QScanPsql root = QScanPsql.scanPsql;
    private final PsqlCountryRepository countryRepository;


    public PsqlScanRepository(TransactionTemplate transactionTemplate,
                              JpaScanRepository jpaScanRepository, JpaQueryRepository jpaQueryRepository,
                              EntityManager entityManager, PsqlCountryRepository countryRepository) {
        super(transactionTemplate);
        this.jpaScanRepository = jpaScanRepository;
        this.jpaQueryRepository = jpaQueryRepository;
        this.entityManager = entityManager;
        this.countryRepository = countryRepository;
    }

    @Override
    @Transactional
    public Scan insert(Scan scan) {
        Country country = Optional.ofNullable(scan.getCountry())
                .map(Country::getId)
                .flatMap(countryRepository::findById)
                .orElse(null);

        scan.setCountry(country);
        return jpaScanRepository.save(new ScanPsql(scan)).toEntity();
    }

    @Override
    @Transactional(readOnly = true)
    public Scan findById(Long id) {
        return jpaScanRepository.findById(id).map(ScanPsql::toEntity)
                .orElseThrow(() -> NotFoundException.build(Scan.class, id));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Scan> findByCode(String code) {
        return jpaScanRepository.findByCode(code).map(ScanPsql::toEntity);
    }


    @Override
    @Transactional(readOnly = true)
    public List<Scan> findAll(ScanFilters filters,
                              ScanSortFilters sortFilters,
                              PageFilters pageFilters) {
        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ScanPsql> query = queryFactory
                .selectFrom(root)
                .join(root.queryPsql, QQueryPsql.queryPsql)
                .leftJoin(root.country, QCountryPsql.countryPsql)
                .distinct();
        buildWhereClauses(filters, query);
        query.orderBy(getOrderSpecifier(sortFilters))
                .offset((long) (pageFilters.getPage() - 1) * pageFilters.getSize())
                .limit(pageFilters.getSize() + 1);
        return query.fetch().stream()
                .map(ScanPsql::toEntity).toList();
    }

    private void buildWhereClauses(ScanFilters filters, JPAQuery<?> query) {

        if (!Objects.isNull(filters)) {
            BooleanBuilder builder = new BooleanBuilder();
            filterText(filters, builder);
            filterStatus(filters, builder);
            filterMode(filters, builder);
            filterQueryId(filters, builder);
            filterCountryId(filters, builder);
            query.where(builder.getValue());
        }

    }

    private void filterQueryId(ScanFilters filters, BooleanBuilder builder) {
        if (filters.getQueryId() != null) {
            jpaQueryRepository.findById(filters.getQueryId())
                    .orElseThrow(() -> NotFoundException.build(Query.class, filters.getQueryId()));
            builder.and(
                    root.queryPsql.id.eq(filters.getQueryId()));
        }
    }

    private void filterCountryId(ScanFilters filters, BooleanBuilder builder) {
        if (filters.getCountryId() != null) {
            countryRepository.findById(filters.getCountryId())
                    .orElseThrow(() -> NotFoundException.build(Country.class, filters.getCountryId()));
            builder.and(
                    root.country.id.eq(filters.getCountryId()));

        }
    }

    private void filterText(ScanFilters filter, BooleanBuilder builder) {
        if (filter.getText() != null) {
            builder.andAnyOf(
                    root.description.containsIgnoreCase(filter.getText()),
                    root.code.containsIgnoreCase(filter.getText()));
        }
    }

    private void filterStatus(ScanFilters filter, BooleanBuilder builder) {
        if (filter.getStatus() != null) {
            builder.and(
                    root.status.eq(filter.getStatus().toString()));
        }
    }

    private void filterMode(ScanFilters filter, BooleanBuilder builder) {
        if (filter.getMode() != null) {
            builder.and(
                    root.mode.eq(filter.getMode().toString()));
        }
    }

    @Override
    @Transactional
    public Scan update(long id, Scan toUpdateScan) {
        ScanPsql scanPsql = jpaScanRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(Scan.class, id));

        assert toUpdateScan.getQuery().getId() != null;
        QueryPsql queryPsql = jpaQueryRepository.findById(toUpdateScan.getQuery().getId())
                .orElseThrow(() -> NotFoundException.build(Query.class, toUpdateScan.getQuery().getId()));
        toUpdateScan.setQuery(queryPsql.toEntity());
        updatedFields(toUpdateScan, scanPsql);
        return insert(scanPsql.toEntity());
    }

    private static void updatedFields(Scan updateScan, ScanPsql scanPsql) {
        scanPsql.setStatus(updateScan.getStatus().name());
        scanPsql.setDescription(updateScan.getDescription());
        scanPsql.setCronExpression(updateScan.getCronExpression());
        scanPsql.setMode(updateScan.getMode().name());
        scanPsql.setMapping(updateScan.getMapping());
        scanPsql.setSidColumn(updateScan.getSidColumn());
        scanPsql.setUpdatedBy(RequestContext.getUsername());
        scanPsql.setUpdatedAt(DateUtils.getCurrentTimeStamp());
    }

    @Override
    @Transactional
    public void deleteById(long id) {
        jpaScanRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Long executeCount(ScanFilters filters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(filters, query);
        return Objects.requireNonNull(query.fetchOne());
    }

    private OrderSpecifier<?> getOrderSpecifier(ScanSortFilters sortFilters) {
        String field = ScanPsql.ENTITY_FIELDS.get(sortFilters.getField());
        if (OrderDirection.ASC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.ASC, ExpressionUtils.path(QScanPsql.class, root, field), NullsFirst);
        } else if (OrderDirection.DESC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.DESC, ExpressionUtils.path(QScanPsql.class, root, field), NullsLast);
        } else {
            throw new IllegalArgumentException("Order must be either ASC or DESC");
        }
    }

}
