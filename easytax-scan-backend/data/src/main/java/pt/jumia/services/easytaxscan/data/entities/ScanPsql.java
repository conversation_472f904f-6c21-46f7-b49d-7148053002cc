package pt.jumia.services.easytaxscan.data.entities;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

@Entity
@Audited
@Table(name = "scan")
@NoArgsConstructor
@Data
public class ScanPsql {

    public static final Map<Scan.SortingFields, String> ENTITY_FIELDS;

    static {
        ENTITY_FIELDS = Map.ofEntries(
                Map.entry(Scan.SortingFields.ID, "id"),
                Map.entry(Scan.SortingFields.UPDATED_AT, "updatedAt")
        );
    }

    @Id
    @SequenceGenerator(name = "scan_seq", sequenceName = "scan_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "scan_seq")
    @Column(name = "id")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fk_query")
    private QueryPsql queryPsql;

    @Column(name = "cron_expression", nullable = false)
    private String cronExpression;

    @Column(name = "code", nullable = false, unique = true)
    private String code;

    @Column(name = "description", nullable = false)
    private String description;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "mode", nullable = false)
    private String mode;

    @Column(name = "mapping", nullable = false)
    private String mapping;

    @Column(name = "sid_column", nullable = false)
    private String sidColumn;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fk_country", nullable = true)
    private CountryPsql country;

    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false, length = 256)
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false, length = 256)
    private String updatedBy;

    public ScanPsql(Scan scan) {
        this.id = scan.getId();
        this.queryPsql = new QueryPsql(scan.getQuery());
        this.cronExpression = scan.getCronExpression();
        this.code = scan.getCode();
        this.description = scan.getDescription();
        this.status = scan.getStatus().name();
        this.mode = scan.getMode().name();
        this.mapping = scan.getMapping();
        this.sidColumn = scan.getSidColumn();
        this.country = scan.getCountry() != null ? new CountryPsql(scan.getCountry()) : null;
        this.createdAt = DateUtils.getTimeStamp(scan.getCreatedAt());
        this.createdBy = Objects.isNull(scan.getCreatedBy()) ? RequestContext.getUsername() : scan.getCreatedBy();
        this.updatedAt = DateUtils.getTimeStamp(scan.getUpdatedAt());
        this.updatedBy = Objects.isNull(scan.getUpdatedBy()) ? RequestContext.getUsername() : scan.getUpdatedBy();


    }

    public Scan toEntity() {
        return Scan
                .builder()
                .id(id)
                .query(queryPsql.toEntity())
                .cronExpression(cronExpression)
                .code(code)
                .description(description)
                .status(Scan.Status.valueOf(status))
                .mode(Scan.Mode.valueOf(mode))
                .mapping(mapping)
                .sidColumn(sidColumn)
                .country(country != null ? country.toEntity() : null)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}
