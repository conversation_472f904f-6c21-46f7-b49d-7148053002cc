package pt.jumia.services.easytaxscan.data.repository.psql;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.data.job.DocumentRetryJob;
import pt.jumia.services.easytaxscan.data.job.ScanProcessingJob;
import pt.jumia.services.easytaxscan.domain.entities.Job;
import pt.jumia.services.easytaxscan.domain.repository.JobsRepository;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.time.LocalDateTime;
import java.util.List;
import java.util.TimeZone;

@Component
@Primary
@RequiredArgsConstructor
@Slf4j
public class PsqlJobsRepository implements JobsRepository {

    private final SchedulerFactoryBean schedulerFactoryBean;

    @Override
    public List<Job> findAll() throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        return scheduler.getJobGroupNames().stream()
                .flatMap(group -> {
                    try {
                        return scheduler.getJobKeys(GroupMatcher.jobGroupEquals(group)).stream();
                    } catch (SchedulerException e) {
                        log.error("Failed to get job keys for group={}, Error={}", group, e.getMessage());
                        throw new RuntimeException("Failed to get job keys for group: " + group, e);
                    }
                })
                .map(jobKey -> {
                    try {
                        return getJobDetail(scheduler, jobKey);
                    } catch (SchedulerException e) {
                        log.error("Failed to get job detail for job:{}, errorMessage={} ", jobKey, e);
                        throw new RuntimeException("Failed to get job detail for job: " + jobKey, e);
                    }
                })
                .toList();
    }

    @Override
    public void pauseAllJobs() throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        scheduler.pauseAll();
    }

    @Override
    public void resumeAllJobs() throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        scheduler.resumeAll();
    }

    @Override
    public Job createJob(Job job, String jobType) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobKey jobKey = new JobKey(job.getJobName());
        List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
        if (triggers.isEmpty()) {
            JobDetail jobDetail = createJobDetails(job, jobType, jobKey);
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity("Trigger_" + job.getJobName(), "ScanGroup")
                    .withSchedule(CronScheduleBuilder.cronSchedule(job.getCronExpression()))
                    .forJob(jobDetail)
                    .build();
            scheduler.scheduleJob(jobDetail, trigger);
            log.info("Job details created: Name:{}, CronExpression:{}", job.getJobName(), job.getCronExpression());
        }
        return getJobDetail(scheduler, jobKey);
    }

    private static JobDetail createJobDetails(Job job, String jobType, JobKey jobKey) {
        if (AppConstants.SCAN_JOB.equals(jobType)) {
            return JobBuilder.newJob(ScanProcessingJob.class)
                    .withIdentity(jobKey)
                    .usingJobData("jobName", job.getJobName())
                    .build();
        }
        if (AppConstants.RETRY_JOB.equals(jobType)) {
            return JobBuilder.newJob(DocumentRetryJob.class)
                    .withIdentity(jobKey)
                    .usingJobData("jobName", job.getJobName())
                    .build();
        }
        throw new IllegalArgumentException("Unsupported job type=" + jobType);
    }

    @Override
    public Job updateJob(Job job) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobKey jobKey = new JobKey(job.getJobName());
        List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
        if (!triggers.isEmpty()) {
            Trigger trigger = triggers.getFirst();
            TriggerKey triggerKey = trigger.getKey();
            CronTrigger cronTrigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(job.getCronExpression());
            trigger = cronTrigger.getTriggerBuilder().withSchedule(cronScheduleBuilder).build();
            scheduler.rescheduleJob(triggerKey, trigger);
            log.info("Job details updated: Name:{}, CronExpression:{}", job.getJobName(), job.getCronExpression());
        }
        return getJobDetail(scheduler, jobKey);
    }

    @Override
    public void deleteJob(Job job) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobKey jobKey = new JobKey(job.getJobName());
        if (scheduler.checkExists(jobKey)) {
            scheduler.deleteJob(jobKey);
            log.info("Job :{} is removed from scheduler ", job.getJobName());
        }
    }

    private Job getJobDetail(Scheduler scheduler, JobKey jobKey) throws SchedulerException {
        List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
        if (triggers.isEmpty()) {
            throw new IllegalStateException("No triggers found for job: " + jobKey);
        }

        Trigger trigger = triggers.getFirst();
        if (!(trigger instanceof CronTrigger cronTrigger)) {
            throw new IllegalStateException("Trigger is not a CronTrigger: " + trigger.getKey());
        }

        TimeZone timeZone = cronTrigger.getTimeZone();
        LocalDateTime lastFiredTime = cronTrigger.getPreviousFireTime() != null ?
                LocalDateTime.ofInstant(cronTrigger.getPreviousFireTime().toInstant(), timeZone.toZoneId()) : null;
        LocalDateTime nextFireTime = cronTrigger.getNextFireTime() != null ?
                LocalDateTime.ofInstant(cronTrigger.getNextFireTime().toInstant(), timeZone.toZoneId()) : null;

        return new Job(jobKey.getName(),
                cronTrigger.getCronExpression(),
                scheduler.getTriggerState(trigger.getKey()).name(),
                timeZone.getID(),
                lastFiredTime,
                nextFireTime);
    }

}
