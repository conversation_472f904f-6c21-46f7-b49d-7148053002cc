package pt.jumia.services.easytaxscan.data.repository.psql;

import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsFirst;
import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsLast;

import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.data.entities.QSettingPsql;
import pt.jumia.services.easytaxscan.data.entities.SettingPsql;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaSettingRepository;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.entities.filter.SettingFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SettingSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.SettingRepository;

@Repository
public class PsqlSettingRepository extends PsqlRepository implements SettingRepository {

    private final QSettingPsql root = new QSettingPsql("root");
    private final EntityManager entityManager;

    private final JpaSettingRepository jpaSettingRepository;

    public PsqlSettingRepository(TransactionTemplate transactionTemplate,
        EntityManager entityManager,
        JpaSettingRepository jpaSettingRepository) {

        super(transactionTemplate);
        this.entityManager = entityManager;
        this.jpaSettingRepository = jpaSettingRepository;
    }


    @Override
    @Transactional(readOnly = true)
    public Optional<Setting> findById(long id) {

        return jpaSettingRepository.findById(id)
            .map(SettingPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Setting> findByProperty(String property) {

        List<SettingPsql> settingsPsql = jpaSettingRepository.findByProperty(property);
        return settingsPsql == null ? null : settingsPsql.stream()
            .map(SettingPsql::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Setting> findAll() {

        return jpaSettingRepository.findAll().stream()
            .map(SettingPsql::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    public Setting insert(Setting setting) {

        return executeInTransaction(status -> jpaSettingRepository.save(new SettingPsql(setting)).toEntity());
    }

    @Override
    public Setting update(long id, Setting setting) {

        return executeInTransaction(status -> {
            Optional<SettingPsql> optSettingPsql = jpaSettingRepository.findById(id);

            if (optSettingPsql.isEmpty()) {
                throw NotFoundException.build(Setting.class, id);
            }

            SettingPsql toUpdate = optSettingPsql.get();
            toUpdate.setProperty(setting.getProperty());
            toUpdate.setValue(setting.getValue());
            if (!Objects.isNull(setting.getType())) {
                toUpdate.setType(setting.getType().name());
            }
            toUpdate.setDescription(setting.getDescription());
            toUpdate.setOverrideKey(setting.getOverrideKey());
            toUpdate.setUpdatedBy(setting.getUpdatedBy());

            return jpaSettingRepository.save(toUpdate).toEntity();
        });
    }

    @Override
    public void deleteById(long id) {

        executeInTransaction(status -> {
            jpaSettingRepository.deleteById(id);
            return null;
        });
    }

    @Override
    public List<Setting> findAll(SettingFilters filters,
        SettingSortFilters sortFilters) {

        JPAQuery<SettingPsql> query = new JPAQueryFactory(entityManager).selectFrom(root);
        buildWhereClauses(filters, query);
        applySort(sortFilters, query);

        return query.distinct().fetch().stream()
            .map(SettingPsql::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    public long count(SettingFilters filters) {

        JPAQuery<SettingPsql> query = new JPAQueryFactory(entityManager).selectFrom(root);

        buildWhereClauses(filters, query);

        return query.fetchCount();
    }

    private void buildWhereClauses(SettingFilters filters, JPAQuery<SettingPsql> query) {

        if (!Objects.isNull(filters)) {
            filterProperty(filters, query);
            filterType(filters, query);
            filterOverrideKey(filters, query);
            filterValue(filters, query);
        }
    }

    private void filterValue(SettingFilters filters, JPAQuery<SettingPsql> query) {

        if (filters.getValue() != null) {
            query.where(root.value.likeIgnoreCase("%".concat(filters.getValue().concat("%"))));
        }
    }

    private void filterOverrideKey(SettingFilters filters, JPAQuery<SettingPsql> query) {

        if (filters.getOverrideKey() != null) {
            query.where(root.overrideKey.likeIgnoreCase("%".concat(filters.getOverrideKey().concat("%"))));
        }
    }

    private void filterType(SettingFilters filters, JPAQuery<SettingPsql> query) {

        if (filters.getType() != null) {
            query.where(root.type.eq(filters.getType().name()));
        }
    }

    private void filterProperty(SettingFilters filters, JPAQuery<SettingPsql> query) {

        if (filters.getProperty() != null) {
            query.where(root.property.likeIgnoreCase("%".concat(filters.getProperty().concat("%"))));
        }
    }

    private void applySort(SettingSortFilters sortFilters,
        JPAQuery<SettingPsql> query) {

        if (sortFilters != null) {
            String field = SettingPsql.ENTITY_FIELDS.get(sortFilters.getField());
            if (OrderDirection.ASC.equals(sortFilters.getDirection())) {
                query.orderBy(new OrderSpecifier(Order.ASC,
                    ExpressionUtils.path(QSettingPsql.class, root, field), NullsFirst));
            } else if (OrderDirection.DESC.equals(sortFilters.getDirection())) {
                query.orderBy(new OrderSpecifier(Order.DESC,
                    ExpressionUtils.path(QSettingPsql.class, root, field), NullsLast));
            } else {
                throw new IllegalArgumentException("Order must be either asc or desc");
            }
        }
    }

}
