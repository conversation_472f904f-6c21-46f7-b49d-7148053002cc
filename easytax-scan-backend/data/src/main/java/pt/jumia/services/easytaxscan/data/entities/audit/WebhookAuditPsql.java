package pt.jumia.services.easytaxscan.data.entities.audit;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RevisionType;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;

import java.time.LocalDateTime;

@Entity
@Audited
@Table(name = "webhooks_aud", schema = "audit")
@NoArgsConstructor
@Data
public class WebhookAuditPsql {


    @EmbeddedId
    private DefaultAudIdPsql id;

    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @Enumerated(EnumType.ORDINAL)
    @Column(name = "revtype")
    private RevisionType revType;

    // Convert back to WebHooks domain model
    public WebHooks toEntity(Document document) {
        return WebHooks.builder()
                .id(this.id.getId())
                .document(document)
                .createdBy(createdBy)
                .createdAt(createdAt)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .build();
    }
}
