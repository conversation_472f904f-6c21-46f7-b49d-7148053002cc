package pt.jumia.services.easytaxscan.data.repository.psql;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.data.entities.QSubQueryPsql;
import pt.jumia.services.easytaxscan.data.entities.QueryPsql;
import pt.jumia.services.easytaxscan.data.entities.SubQueryPsql;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaQueryRepository;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaSubQueryRepository;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.SubQueryRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsFirst;
import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsLast;

@Slf4j
@Repository
public class PsqlSubQueryRepository extends PsqlRepository implements SubQueryRepository {

    private final JpaSubQueryRepository jpaSubQueryRepository;
    private final JpaQueryRepository jpaQueryRepository;
    private final EntityManager entityManager;
    private final QSubQueryPsql root = new QSubQueryPsql("root");


    public PsqlSubQueryRepository(TransactionTemplate transactionTemplate,
                                  JpaSubQueryRepository jpaSubQueriesRepository, JpaQueryRepository jpaQueryRepository,
                                  EntityManager entityManager) {
        super(transactionTemplate);
        this.jpaSubQueryRepository = jpaSubQueriesRepository;
        this.jpaQueryRepository = jpaQueryRepository;
        this.entityManager = entityManager;
    }


    @Override
    @Transactional
    public SubQuery insert(Long id, SubQuery subQuery) {
        QueryPsql queryPsql = getQueryPsql(id);
        QueryPsql subQueryPsql = getQueryPsql(subQuery.getSubQuery().getId());

        subQuery.setQuery(queryPsql.toEntity());
        subQuery.setSubQuery(subQueryPsql.toEntity());
        return jpaSubQueryRepository.save(new SubQueryPsql(subQuery)).toEntity();
    }

    @Override
    @Transactional
    public SubQuery update(Long id, Long subQueryId, SubQuery subQuery) {
        SubQueryPsql subQueryPsql = jpaSubQueryRepository.findByQueryIdAndSubQueryId(id, subQueryId)
                .orElseThrow(() -> NotFoundException.build(SubQueryPsql.class, "ID", id));

        subQueryPsql.setUpdatedAt(LocalDateTime.now());
        subQuery.setUpdatedBy(RequestContext.getUsername());
        subQuery.setIsList(subQueryPsql.getIsList());
        subQuery.setMainQueryColumn(subQueryPsql.getMainQueryColumn());
        subQuery.setSubQueryColumn(subQueryPsql.getSubQueryColumn());
        subQuery.setIsList(subQueryPsql.getIsList());

        return jpaSubQueryRepository.save(subQueryPsql).toEntity();
    }

    private QueryPsql getQueryPsql(Long id) {
        return jpaQueryRepository.findById(Objects.requireNonNull(id))
                .orElseThrow(() -> NotFoundException.build(QueryPsql.class, "ID", id));
    }

    @Override
    @Transactional
    public Long executeCount(Long id, SubQueryFilters filters) {
        JPAQuery<SubQueryPsql> query = new JPAQueryFactory(entityManager).selectFrom(root).where(root.query.id.eq(id));
        buildWhereClauses(filters, query);
        return query.fetchCount();
    }

    @Override
    public List<SubQuery> findByQueryId(Long id) {
        return jpaSubQueryRepository.findAllByQueryId(id).stream()
                .map(SubQueryPsql::toEntity)
                .collect(Collectors.toList());
    }


    @Override
    @Transactional
    public List<SubQuery> findAll(Long id, SubQueryFilters filters, SubQuerySortFilters sortFilters, PageFilters pageFilters) {

        JPAQuery<SubQueryPsql> query = new JPAQueryFactory(entityManager)
                .selectFrom(root).where(root.query.id.eq(id));
        buildWhereClauses(filters, query);
        query.orderBy(getOrderSpecifier(sortFilters))
                .offset((long) (pageFilters.getPage() - 1) * pageFilters.getSize())
                .limit(pageFilters.getSize() + 1);

        List<SubQueryPsql> subQueryPsqlList = query.fetch();
        return subQueryPsqlList.stream().map(SubQueryPsql::toEntity).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void deleteSubQueryByQueryId(Long queryId) {
        jpaSubQueryRepository.deleteSubQueryByQueryId(queryId);
    }

    @Override
    @Transactional
    public void delete(Long mainQueryId, Long subQueryId) {
        jpaSubQueryRepository.deleteByQueryIdAndSubQueryId(mainQueryId, subQueryId);
    }

    private void buildWhereClauses(SubQueryFilters filters, JPAQuery<?> query) {

        if (!Objects.isNull(filters)) {
            BooleanBuilder builder = new BooleanBuilder();
            filterText(filters, builder);
            query.where(builder.getValue());
        }

    }

    private void filterText(SubQueryFilters filter, BooleanBuilder builder) {
        if (filter.getText() != null) {
            builder.and(root.status.eq(filter.getText()));
        }

    }

    private OrderSpecifier<?> getOrderSpecifier(SubQuerySortFilters sortFilters) {
        String field = SubQueryPsql.ENTITY_FIELDS.get(sortFilters.getField());
        if (OrderDirection.ASC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.ASC, ExpressionUtils.path(QSubQueryPsql.class, root, field), NullsFirst);
        } else if (OrderDirection.DESC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.DESC,
                    ExpressionUtils.path(QSubQueryPsql.class, root, field), NullsLast);
        } else {
            throw new IllegalArgumentException("Order must be either asc or desc");
        }
    }
}
