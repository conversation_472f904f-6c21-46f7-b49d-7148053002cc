package pt.jumia.services.easytaxscan.data.repository.psql.events.channels;

import com.impossibl.postgres.jdbc.PGDataSource;
import java.sql.SQLException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.data.repository.psql.PsqlSettingRepository;
import pt.jumia.services.easytaxscan.domain.properties.DataProperties;
import pt.jumia.services.easytaxscan.domain.usecases.setting.ReloadSettingUseCase;

@Component
@RequiredArgsConstructor
public class PsqlChannelFactory {

    private final PGDataSource pgDataSource;
    private final PsqlSettingRepository settingRepository;
    private final ReloadSettingUseCase reloadSettingsUseCase;
    private final DataProperties dataProperties;

    public PsqlSettingsChangeChannel createSettingsChangeChannel() throws SQLException {

        return new PsqlSettingsChangeChannel(
            pgDataSource, settingRepository, reloadSettingsUseCase,
            (int) dataProperties.getEvents().getCheckConnectionTimeout().toMillis());
    }

}
