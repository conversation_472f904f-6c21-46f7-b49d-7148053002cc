package pt.jumia.services.easytaxscan.data.repository.dao;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.data.repository.dao.sql.SQLEntityDataSourceAdapter;
import pt.jumia.services.easytaxscan.data.repository.dao.sql.SQLServerEntityDataSourceAdapter;
import pt.jumia.services.easytaxscan.domain.exceptions.DataSourceProtocolNotSupportedException;
import pt.jumia.services.easytaxscan.domain.properties.DatabaseProperties;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceFactory;
import pt.jumia.services.easytaxscan.domain.repository.EntityDataSourceRepository;

import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class EntityDataSourceFactory implements DataSourceFactory {

    private static final String SQLSERVER = "sqlserver";
    private static final String POSTGRESQL = "postgresql";

    private Map<String , EntityDataSourceRepository> dataSourceAdapters = new HashMap<>();

    @Override
    public EntityDataSourceRepository getDatasourceAdapter(DatabaseProperties.DatasourceProperties dataSource) {

        String dataSourceKey = buildKey(dataSource);
        if (dataSourceAdapters.containsKey(dataSourceKey)) {
            return dataSourceAdapters.get(dataSourceKey);
        }

        EntityDataSourceRepository result = null;
        switch (dataSource.getConnection().getDatasource()) {
            case SQLSERVER -> result = new SQLServerEntityDataSourceAdapter(dataSource);
            case POSTGRESQL -> result = new SQLEntityDataSourceAdapter(dataSource);
            default ->
                    throw DataSourceProtocolNotSupportedException.createNotSupported(dataSource.getConnection().getDatasource());

        };

        dataSourceAdapters.put(dataSourceKey, result);
        return result;
    }

    private String buildKey(DatabaseProperties.DatasourceProperties dataSource) {
        return dataSource.getConnection().getUrl() + "::" + dataSource.getConnection().getDatabase();
    }
}
