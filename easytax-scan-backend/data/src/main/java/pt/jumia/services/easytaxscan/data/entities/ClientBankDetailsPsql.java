package pt.jumia.services.easytaxscan.data.entities;

import jakarta.persistence.*;
import lombok.Data;
import pt.jumia.services.easytaxscan.domain.entities.ClientBankDetails;

import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "client_bankdetails")
public class ClientBankDetailsPsql {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "bank_details_id")
    private Long bankDetailsId;

    @Column(name = "client_code", nullable = false)
    private String clientCode;

    @Column(name = "bank_name", nullable = false)
    private String bankName;

    @Column(name = "account_number", nullable = false)
    private String accountNumber;

    @Column(name = "account_holder_name", nullable = false)
    private String accountHolderName;

    @Column(name = "branch_name")
    private String branchName;

    @Column(name = "ifsc_code")
    private String ifscCode;

    @Column(name = "swift_code")
    private String swiftCode;

    @Column(name = "account_type")
    private String accountType;

    @Column(name = "date_added")
    private LocalDateTime dateAdded;

    public ClientBankDetails toEntity() {
        return ClientBankDetails
                .builder()
                .clientCode(this.clientCode)
                .bankName(this.bankName)
                .accountNumber(this.accountNumber)
                .accountHolderName(this.accountHolderName)
                .branchName(this.branchName)
                .ifscCode(this.ifscCode)
                .swiftCode(this.swiftCode)
                .accountType(this.accountType)
                .dateAdded(this.dateAdded)
                .build();
    }
}

