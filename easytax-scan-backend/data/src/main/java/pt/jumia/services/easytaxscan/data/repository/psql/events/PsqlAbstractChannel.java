package pt.jumia.services.easytaxscan.data.repository.psql.events;

import com.impossibl.postgres.api.jdbc.PGConnection;
import com.impossibl.postgres.jdbc.PGDataSource;
import java.sql.SQLException;
import java.sql.Statement;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class PsqlAbstractChannel implements PsqlChannel {

    private static final String SPACE = " ";
    private static final String COMMA = ",";
    private static final String SINGLE_QUOTE = "'";
    private static final String LISTEN_COMMAND = "LISTEN";
    private static final String NOTIFY_COMMAND = "NOTIFY";

    private PGConnection currentConnection;

    private final PGDataSource pgDataSource;
    private final String channelName;
    private final String listenCommand;
    private final String notifyCommand;
    private final int checkConnectionTimeout;

    protected PsqlAbstractChannel(PGDataSource dataSource, String channelName, int checkConnectionTimeout)
            throws SQLException {

        this.pgDataSource = dataSource;
        this.channelName = channelName;
        this.listenCommand = LISTEN_COMMAND.concat(SPACE).concat(channelName);
        this.notifyCommand = NOTIFY_COMMAND.concat(SPACE).concat(channelName);
        this.checkConnectionTimeout = checkConnectionTimeout;
        this.currentConnection = (PGConnection) pgDataSource.getConnection();
    }

    @Override
    public void start() {
        currentConnection.addNotificationListener(this);
        runCommand(listenCommand);
    }

    @Override
    public void notifyMe() {
        updateCurrentConnectionIfNotValid();

        // If we have a payload to add to notify command, append it
        String notifyCommandPayload = loadNotifyCommandPayload();
        if (notifyCommandPayload != null) {
            runCommand(notifyCommand
                    .concat(COMMA)
                    .concat(SPACE)
                    .concat(SINGLE_QUOTE)
                    .concat(notifyCommandPayload)
                    .concat(SINGLE_QUOTE));
            return;
        }
        // Run notify without payload
        runCommand(notifyCommand);
    }

    @Override
    public void stop() {
        try {
            currentConnection.removeNotificationListener(this);
            currentConnection.close();
        } catch (SQLException e) {
            // Convert to RuntimeException so caller doesnt need to be forced to try catch it and
            // we dont have a recover fallback to trigger this again.
            throw new RuntimeException(e);
        }
    }

    protected abstract String loadNotifyCommandPayload();

    private void runCommand(String command) {
        try (Statement statement = currentConnection.createStatement()) {
            statement.execute(command);
        } catch (SQLException e) {
            // Convert to RuntimeException so caller doesnt need to be forced to try catch it and
            // we dont have a recover fallback to trigger this again.
            throw new RuntimeException(e);
        }
    }

    private void updateCurrentConnectionIfNotValid() {
        try {
            if (!currentConnection.isValid(checkConnectionTimeout)) {
                log.warn("Lost connection for channel {}, creating new one.", channelName);
                stop();
                currentConnection = (PGConnection) pgDataSource.getConnection();
                start();
            }
        } catch (Exception e) {
            log.error("Unable to recreate connection for channel {}", channelName);
        }
    }
}
