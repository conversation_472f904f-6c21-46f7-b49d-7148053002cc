package pt.jumia.services.easytaxscan.data.entities;


import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks.SortingFields;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;

@Entity
@Audited
@Table(name = "webhooks")
@NoArgsConstructor
@Data
public class WebhooksPsql {

    public static final Map<SortingFields, String> ENTITY_FIELDS = Map.ofEntries(
            Map.entry(SortingFields.ID, "id"),
            Map.entry(SortingFields.PAYLOAD, "payload"),
            Map.entry(SortingFields.DOCUMENT, "document"),
            Map.entry(SortingFields.CREATED_AT, "createdAt"),
            Map.entry(SortingFields.CREATED_BY, "createdBy"),
            Map.entry(SortingFields.UPDATED_AT, "updatedAt"),
            Map.entry(SortingFields.UPDATED_BY, "updatedBy")
    );


    @Id
    @Column(name = "id")
    @SequenceGenerator(name = "webhooks_seq", sequenceName = "webhooks_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "webhooks_id_seq")
    private Long id;

    @Column(name = "payload")
    private String payload;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fk_document")
    private DocumentPsql documentPsql;

    @Column(name = "created_by", updatable = false)
    protected String createdBy;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    protected LocalDateTime createdAt;

    @Column(name = "updated_by")
    protected String updatedBy;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    protected LocalDateTime updatedAt;


    public WebhooksPsql(WebHooks webHooks) {

        this.id = webHooks.getId();
        this.payload = webHooks.getPayload();
        this.documentPsql = new DocumentPsql(webHooks.getDocument());
        this.createdBy = ObjectUtils.defaultIfNull(webHooks.getCreatedBy(), RequestContext.getUsername());
        this.createdAt = webHooks.getCreatedAt() == null
                ? DateUtils.getCurrentTimeStamp()
                : DateUtils.getTimeStamp(webHooks.getCreatedAt());
        this.updatedBy = ObjectUtils.defaultIfNull(webHooks.getUpdatedBy(), RequestContext.getUsername());
        this.updatedAt = webHooks.getUpdatedAt() == null
                ? DateUtils.getCurrentTimeStamp()
                : DateUtils.getTimeStamp(webHooks.getUpdatedAt());
    }

    public WebHooks toEntity() {

        return WebHooks.builder()
                .id(this.id)
                .payload(payload)
                .document(documentPsql.toEntity())
                .createdBy(this.createdBy)
                .createdAt(this.createdAt)
                .updatedAt(this.updatedAt)
                .updatedBy(this.updatedBy)
                .build();
    }

}
