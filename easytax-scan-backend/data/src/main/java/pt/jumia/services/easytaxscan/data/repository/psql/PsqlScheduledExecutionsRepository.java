package pt.jumia.services.easytaxscan.data.repository.psql;

import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.impl.matchers.GroupMatcher;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.easytaxscan.data.utils.sort.ScheduledExecutionComparator;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.ScheduledExecution;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScheduledExecutionFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScheduledExecutionSortFilters;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;
import pt.jumia.services.easytaxscan.domain.repository.ScheduledExecutionsRepository;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;


@Slf4j
@Repository
public class PsqlScheduledExecutionsRepository extends PsqlRepository implements ScheduledExecutionsRepository {


    private final EntityManager entityManager;

    private final SchedulerFactoryBean schedulerFactoryBean;

    private final ScanRepository scanRepository;

    public PsqlScheduledExecutionsRepository(TransactionTemplate transactionTemplate,
                                             EntityManager entityManager,
                                             SchedulerFactoryBean schedulerFactoryBean,
                                             PsqlScanRepository scanRepository) {
        super(transactionTemplate);
        this.entityManager = entityManager;
        this.schedulerFactoryBean = schedulerFactoryBean;
        this.scanRepository = scanRepository;
    }

    @Override
    public List<ScheduledExecution> findAll(ScheduledExecutionFilters filters, ScheduledExecutionSortFilters sortFilters) {
        List<ScheduledExecution> scheduledExecutions = new ArrayList<>();
        try {
            Scheduler scheduler = schedulerFactoryBean.getScheduler();
            for (String groupName : scheduler.getJobGroupNames()) {
                for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(groupName))) {
                    List<? extends Trigger> quartzTriggers = Optional.ofNullable(scheduler.getTriggersOfJob(jobKey)).orElse(Collections.emptyList());
                    if (quartzTriggers.isEmpty()){
                        continue;
                    }

                    Trigger firstTrigger = quartzTriggers.getFirst();
                    if (!fitsOnFilters(filters, jobKey.getName(), firstTrigger)) {
                        continue;
                    }

                    ScheduledExecution scheduledExecution = prepareScheduleExecution(jobKey, scheduler, firstTrigger);
                    Optional<Scan> scan = scanRepository.findByCode(jobKey.getName());
                    scan.ifPresent(value -> {
                        if (value.getCountry() != null) {
                            scheduledExecution.setCountryId(value.getCountry().getId());
                        }
                        scheduledExecution.setScanId(value.getId());
                    });

                    scheduledExecutions.add(scheduledExecution);
                }
            }
        } catch (SchedulerException e) {
            log.error("Unable to retrieve scheduled executions: {}", e.getMessage(), e);
        }
        scheduledExecutions.sort(new ScheduledExecutionComparator(sortFilters));
        return scheduledExecutions;
    }

    private ScheduledExecution prepareScheduleExecution(JobKey jobKey, Scheduler scheduler, Trigger firstTrigger) {

        ZoneId localTimeZone = ZoneId.of(((CronTriggerImpl) firstTrigger).getTimeZone().getID());
        Instant nextFireInstant = firstTrigger.getNextFireTime().toInstant();

        return ScheduledExecution.builder()
                .scanCode(jobKey.getName())
                .nextFireTimeUtc(LocalDateTime.ofInstant(nextFireInstant, ZoneOffset.UTC))
                .nextFireTimeLocal(LocalDateTime.ofInstant(nextFireInstant, localTimeZone))
                .localTimeZone(localTimeZone.getId())
                .status(getStatus(scheduler, jobKey.getName(), jobKey.getGroup()))
                .build();
    }


    private boolean fitsOnFilters(
            ScheduledExecutionFilters filters,
            String triggerName,
            Trigger trigger) {

        if (StringUtils.isNotBlank(filters.getFilterText()) &&
                !triggerName.toLowerCase(Locale.ROOT).contains(filters.getFilterText().toLowerCase(Locale.ROOT))) {
            return false;
        }

        LocalDateTime nextFireTime = LocalDateTime.ofInstant(trigger.getNextFireTime().toInstant(), ZoneOffset.UTC);
        if (filters.getNextFireTimeFrom() != null && nextFireTime.isBefore(filters.getNextFireTimeFrom())) {
            return false;
        }
        return filters.getNextFireTimeTo() == null || !nextFireTime.isAfter(filters.getNextFireTimeTo());
    }

    private boolean checkRunning(String triggerName, String triggerGroup) {
        String query = "SELECT COUNT(*) FROM quartz.qrtz_fired_triggers " +
                "WHERE trigger_name = :triggerName AND trigger_group = :triggerGroup";
        try {
            Number count = (Number) entityManager.createNativeQuery(query)
                    .setParameter("triggerName", triggerName)
                    .setParameter("triggerGroup", triggerGroup)
                    .getSingleResult();

            return count.intValue() > 0;
        } catch (Exception e) {
            log.error("Error checking executing triggers for trigger: {}, group: {}. Exception: {}",
                    triggerName, triggerGroup, e.getMessage(), e);
        }
        return false;
    }

    private ScheduledExecution.Status getStatus(Scheduler scheduler, String triggerName, String triggerGroup) {
        if (checkRunning(triggerName, triggerGroup)) {
            return ScheduledExecution.Status.RUNNING;
        }

        try {
            return scheduler.getPausedTriggerGroups().contains(triggerGroup)
                    ? ScheduledExecution.Status.PAUSED
                    : ScheduledExecution.Status.SCHEDULED;
        } catch (SchedulerException e) {
            log.error("Error retrieving trigger status for '{}' in group '{}': {}",
                    triggerName, triggerGroup, e.getMessage(), e);
        }

        return ScheduledExecution.Status.SCHEDULED;
    }
}
