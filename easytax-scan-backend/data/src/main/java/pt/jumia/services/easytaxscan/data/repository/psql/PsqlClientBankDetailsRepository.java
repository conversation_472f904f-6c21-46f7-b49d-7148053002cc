package pt.jumia.services.easytaxscan.data.repository.psql;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.easytaxscan.data.entities.ClientBankDetailsPsql;
import pt.jumia.services.easytaxscan.domain.entities.ClientBankDetails;
import pt.jumia.services.easytaxscan.domain.properties.DatabaseProperties;
import pt.jumia.services.easytaxscan.domain.repository.ClientBankDetailsRepository;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceFactory;
import pt.jumia.services.easytaxscan.domain.repository.EntityDataSourceRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Repository
public class PsqlClientBankDetailsRepository extends PsqlRepository implements ClientBankDetailsRepository {

    private final DataSourceFactory dataSourceFactory;
    private final DatabaseProperties databaseProperties;

    public PsqlClientBankDetailsRepository(TransactionTemplate transactionTemplate,
                                           DataSourceFactory dataSourceFactory,
                                           DatabaseProperties databaseProperties) {
        super(transactionTemplate);
        this.dataSourceFactory = dataSourceFactory;
        this.databaseProperties = databaseProperties;
    }

    @Override
    public Optional<ClientBankDetails> findByClientCode(String clientCode) {
        // Get the appropriate data source adapter
        EntityDataSourceRepository dataSourceAdapter = dataSourceFactory.getDatasourceAdapter(
                databaseProperties.getDataSourceProperties("nav"));

        // Define the query to fetch client bank details by client code
        String query = "SELECT * FROM client_bankdetails WHERE client_code = :clientCode";

        List<Map<String, Object>> results = dataSourceAdapter.query(query, Map.of("clientCode", clientCode));

        if (!results.isEmpty()) {
            Map<String, Object> result = results.get(0);
            ClientBankDetailsPsql clientBankDetailsPsql = new ClientBankDetailsPsql();
            clientBankDetailsPsql.setBankDetailsId(((Number) result.get("bank_details_id")).longValue());
            clientBankDetailsPsql.setClientCode((String) result.get("client_code"));
            clientBankDetailsPsql.setBankName((String) result.get("bank_name"));
            clientBankDetailsPsql.setAccountNumber((String) result.get("account_number"));
            clientBankDetailsPsql.setAccountHolderName((String) result.get("account_holder_name"));
            clientBankDetailsPsql.setBranchName((String) result.get("branch_name"));
            clientBankDetailsPsql.setIfscCode((String) result.get("ifsc_code"));
            clientBankDetailsPsql.setSwiftCode((String) result.get("swift_code"));
            clientBankDetailsPsql.setAccountType((String) result.get("account_type"));
            clientBankDetailsPsql.setDateAdded(((java.sql.Timestamp) result.get("date_added")).toLocalDateTime());
            return Optional.of(clientBankDetailsPsql.toEntity());
        } else {
            return Optional.empty();
        }
    }
}
