package pt.jumia.services.easytaxscan.data.entities.audit;

import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.RevisionType;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;

import java.time.LocalDateTime;

@Data
@Entity
@NoArgsConstructor
@Getter
@Setter
@Table(name = "datasource_aud", schema = "audit")
public class DataSourceAuditPsql {

    @EmbeddedId
    private DefaultAudIdPsql id;

    @Column(name = "code", nullable = false, unique = true)
    private String code;

    @Column(name = "description", nullable = false)
    private String description;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "country_segregated", nullable = false)
    private Boolean countrySegregated;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false, length = 256)
    private String createdBy;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false, length = 256)
    private String updatedBy;

    @Enumerated(EnumType.ORDINAL)
    @Column(name = "revtype")
    private RevisionType revType;

    public DataSource toEntity() {
        return DataSource
                .builder()
                .id(id.getId())
                .code(code)
                .description(description)
                .status(DataSource.Status.valueOf(status))
                .countrySegregated(countrySegregated)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}
