package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.easytaxscan.data.audit.RevisionTypeConverter;
import pt.jumia.services.easytaxscan.data.entities.QRevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.RevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.QQueryAuditPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.QueryAuditPsql;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.QueryAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.QueryAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ReadQueryUseCase;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class PsqlQueryAuditRepository implements QueryAuditRepository {

    private static final QQueryAuditPsql Q_QUERY_AUDIT_PSQL = QQueryAuditPsql.queryAuditPsql;
    private static final QRevisionPsql Q_REVISION_PSQL = QRevisionPsql.revisionPsql;

    private final EntityManager entityManager;

    private final ReadQueryUseCase readQueryUseCase;


    public PsqlQueryAuditRepository(EntityManager entityManager, ReadQueryUseCase readQueryUseCase) {
        this.entityManager = entityManager;
        this.readQueryUseCase = readQueryUseCase;
    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditedEntity<Query>> getQueryAuditLogById(QueryAuditFilters filters, Query query) {

        JPAQuery<Tuple> queryFetch = baseQuery(filters)
                .select(Q_QUERY_AUDIT_PSQL, Q_REVISION_PSQL);

        return queryFetch.fetch().stream().map(tuple -> {
            RevisionPsql revisionPsql = tuple.get(Q_REVISION_PSQL);
            QueryAuditPsql queryAuditPsql = tuple.get(Q_QUERY_AUDIT_PSQL);

            AuditedEntity.OperationType operationType = RevisionTypeConverter.toOperationType(
                    Objects.requireNonNull(tuple.get(Q_QUERY_AUDIT_PSQL)).getRevType());

            assert queryAuditPsql != null;
            assert revisionPsql != null;
            return AuditedEntity.<Query>builder()
                    .auditedEntity(AuditedEntities.QUERY)
                    .operationType(operationType)
                    .revisionInfo(revisionPsql.toEntity())
                    .entity(queryAuditPsql.toEntity(
                            query.getDataSource()
                    ))
                    .build();
        }).collect(Collectors.toList());
    }

    private JPAQuery<?> baseQuery(QueryAuditFilters filters) {
        JPAQuery<?> query = new JPAQueryFactory(entityManager)
                .from(Q_QUERY_AUDIT_PSQL)
                .innerJoin(Q_REVISION_PSQL).on(Q_QUERY_AUDIT_PSQL.id.rev.eq(Q_REVISION_PSQL.id));

        if (Objects.nonNull(filters.getId())) {
            query.where(Q_QUERY_AUDIT_PSQL.id.id.eq(filters.getId()));
        }
        AuditRepositoryHelper.applyGenericFiltersAndSorting(query, Q_REVISION_PSQL, filters);

        return query;
    }

    @Override
    public long getAuditLogCountById(QueryAuditFilters filters) {
        return baseQuery(filters).fetchCount();
    }
}
