package pt.jumia.services.easytaxscan.data.utils;

import com.zaxxer.hikari.HikariDataSource;
import lombok.RequiredArgsConstructor;
import org.flywaydb.core.Flyway;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import pt.jumia.services.easytaxscan.domain.properties.DataProperties;
import pt.jumia.services.easytaxscan.domain.properties.SpringProperties;

import javax.sql.DataSource;

@Service
@RequiredArgsConstructor
public class MigrationService {

    private final SpringProperties springProperties;
    private final DataProperties dataProperties;

    public void migrateDatabaseSchema(DataSource dataSource, String schema) {
        try {
            Flyway flyway = createFlywayInstance(dataSource, schema);
            if (dataProperties.getDb().getFlyway().isRepair()) {
                flyway.repair();
            }
            flyway.migrate();
        } catch (Exception e) {
            // Handle exceptions appropriately (log, throw custom exception, etc.)
            throw new RuntimeException("Flyway migration failed for schema " + schema + ": " + e.getMessage(), e);
        }
    }

    public void cleanDatabaseSchema(DataSource dataSource, String schema) {
        Flyway flyway = createFlywayInstance(dataSource, schema);
        if (dataProperties.getDb().getFlyway().isRepair()) {
            flyway.repair();
        }
        flyway.clean();
    }

    @NotNull
    private Flyway createFlywayInstance(DataSource dataSource, String schema) {
        String dir = springProperties.getFlyway().getLocations() + "/" + schema;
        return Flyway.configure()
                .dataSource(dataSource)
                .locations(dir)
                .schemas(schema)
                .baselineOnMigrate(true)
                .load();
    }
}
