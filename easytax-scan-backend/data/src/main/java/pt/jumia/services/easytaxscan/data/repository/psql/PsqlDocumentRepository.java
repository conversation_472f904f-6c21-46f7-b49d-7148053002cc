package pt.jumia.services.easytaxscan.data.repository.psql;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.data.entities.DocumentPsql;
import pt.jumia.services.easytaxscan.data.entities.ExecutionLogPsql;
import pt.jumia.services.easytaxscan.data.entities.QDocumentPsql;
import pt.jumia.services.easytaxscan.data.entities.QExecutionLogPsql;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaDocumentRepository;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaExecutionLogRepository;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaScanRepository;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.DocumentRepository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsFirst;
import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsLast;

@Slf4j
@Repository
public class PsqlDocumentRepository extends PsqlRepository implements DocumentRepository {

    private final JpaDocumentRepository jpaDocumentRepository;
    private final JpaExecutionLogRepository jpaExecutionLogRepository;
    private final JpaScanRepository jpaScanRepository;
    private final EntityManager entityManager;
    private final QDocumentPsql root = QDocumentPsql.documentPsql;

    public PsqlDocumentRepository(TransactionTemplate transactionTemplate,
                                  JpaDocumentRepository jpaDocumentRepository,
                                  JpaExecutionLogRepository jpaExecutionLogRepository,
                                  JpaScanRepository jpaScanRepository,
                                  EntityManager entityManager) {
        super(transactionTemplate);
        this.jpaDocumentRepository = jpaDocumentRepository;
        this.jpaExecutionLogRepository = jpaExecutionLogRepository;
        this.jpaScanRepository = jpaScanRepository;
        this.entityManager = entityManager;
    }

    @Override
    @Transactional
    public Document insert(Document document) {
        ExecutionLogPsql executionLogPsql = jpaExecutionLogRepository.findById(
                        Objects.requireNonNull(document.getExecutionLog().getId()))
                .orElseThrow(() -> NotFoundException.build(ExecutionLog.class, "ID", document.getExecutionLog().getId()));
        document.setExecutionLog(executionLogPsql.toEntity());
        return jpaDocumentRepository.save(new DocumentPsql(document)).toEntity();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Document> findAll(DocumentFilters filters, DocumentSortFilters sortFilters, PageFilters pageFilters) {
        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<DocumentPsql> query = queryFactory.selectFrom(root).distinct();
        buildWhereClauses(filters, query);
        query.orderBy(getOrderSpecifier(sortFilters))
                .offset((long) (pageFilters.getPage() - 1) * pageFilters.getSize())
                .limit(pageFilters.getSize() + 1);
        return query.fetch().stream().map(DocumentPsql::toEntity).toList();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Document> findById(Long id) {
        return jpaDocumentRepository.findById(id).map(DocumentPsql::toEntity);
    }

    private OrderSpecifier<?> getOrderSpecifier(DocumentSortFilters sortFilters) {
        String field = DocumentPsql.ENTITY_FIELDS.get(sortFilters.getField());
        if (OrderDirection.ASC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.ASC, ExpressionUtils.path(QDocumentPsql.class, root, field), NullsFirst);
        } else if (OrderDirection.DESC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.DESC, ExpressionUtils.path(QDocumentPsql.class, root, field), NullsLast);
        } else {
            throw new IllegalArgumentException("Order must be either asc or desc");
        }
    }


    @Override
    public void deleteById(long id) {
        jpaDocumentRepository.deleteById(id);
    }


    @Override
    @Transactional(readOnly = true)
    public Document findById(String sid) {
        return jpaDocumentRepository.findBySid(sid)
                .orElseThrow(() -> NotFoundException.build(Document.class, "sid", sid))
                .toEntity();
    }

    @Override
    @Transactional
    public Document update(long id, Document updateDocument) {
        DocumentPsql documentPsql = jpaDocumentRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(Document.class, id));


        documentPsql.setStatus(String.valueOf(updateDocument.getStatus()));
        documentPsql.setMode(String.valueOf(updateDocument.getMode()));
        documentPsql.setQueryData(updateDocument.getQueryData());
        documentPsql.setRequestPayload(updateDocument.getRequestPayload());
        documentPsql.setResponseCode(updateDocument.getResponseCode());
        documentPsql.setResponsePayload(updateDocument.getResponsePayload());
        documentPsql.setUpdatedAt(updateDocument.getUpdatedAt());
        documentPsql.setUpdatedBy(RequestContext.getUsername());

        return jpaDocumentRepository.save(documentPsql).toEntity();
    }


    @Override
    public Long executeCount(DocumentFilters filters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager).select(root.id.count()).from(root);
        buildWhereClauses(filters, query);
        return Objects.requireNonNull(query.fetchOne());
    }

    private void buildWhereClauses(DocumentFilters filters, JPAQuery<?> query) {
        if (!Objects.isNull(filters)) {
            BooleanBuilder builder = new BooleanBuilder();
            filterStatus(filters, builder);
            filterMode(filters, builder);
            filterExecutionLog(filters, builder);
            filterCreatedAt(filters, builder);
            filterSid(filters, builder);
            filterScanId(filters, builder);
            query.where(builder.getValue());
        }
    }

    private void filterScanId(DocumentFilters filters, BooleanBuilder builder) {
        if (filters.getScanId() != null) {
            QExecutionLogPsql executionLog = QExecutionLogPsql.executionLogPsql;
            builder.and(executionLog.scanPsql.id.eq(filters.getScanId()));
        }
    }

    private void filterCreatedAt(DocumentFilters filter, BooleanBuilder builder) {
        if (filter.getCreatedFrom() != null) {
            builder.and(root.createdAt.goe(filter.getCreatedFrom()));
        }
        if (filter.getCreatedTo() != null) {
            builder.and(root.createdAt.loe(filter.getCreatedTo()));
        }
    }

    private void filterExecutionLog(DocumentFilters filters, BooleanBuilder builder) {
        if (filters.getExecutionLogId() != null) {
            jpaExecutionLogRepository.findById(filters.getExecutionLogId())
                    .orElseThrow(() -> NotFoundException.build(Document.class, filters.getExecutionLogId()));
            builder.and(root.executionLogPsql.id.eq(filters.getExecutionLogId()));
        }
    }

    private void filterMode(DocumentFilters filter, BooleanBuilder builder) {
        if (filter.getMode() != null) {
            builder.and(root.mode.eq(filter.getMode()));
        }
    }

    private void filterSid(DocumentFilters filters, BooleanBuilder builder) {
        if (filters.getSid() != null && !filters.getSid().isEmpty()) {
            builder.and(root.sid.eq(filters.getSid()));
        }
    }

    private void filterStatus(DocumentFilters filter, BooleanBuilder builder) {
        if (!CollectionUtils.isEmpty(filter.getStatusList())) {
            builder.andAnyOf(root.status.in(filter.getStatusList()));
        }
    }
}
