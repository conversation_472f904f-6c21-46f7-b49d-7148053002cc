package pt.jumia.services.easytaxscan.data.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionEntity;
import pt.jumia.services.easytaxscan.data.audit.listener.UserRevisionListener;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.HelpCenterTicket;

import java.io.Serial;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "revision_info", schema = "\"audit\"")
@RevisionEntity(UserRevisionListener.class)
@EqualsAndHashCode(callSuper = true)
public class RevisionPsql extends DefaultRevisionEntity {

    @Serial
    private static final long serialVersionUID = -2621042342875858176L;

    private String email;

    private LocalDateTime time;

    @Column(name = "hc_ticket_link")
    private String hcTicketLink;

    @Column(name = "hc_ticket_justification")
    private String hcTicketJustification;

    public AuditedEntity.RevisionInfo toEntity() {
        return AuditedEntity.RevisionInfo.builder()
                .datetime(getTime())
                .email(getEmail())
                .helpCenterTicket(HelpCenterTicket.builder()
                        .link(hcTicketLink)
                        .justification(hcTicketJustification)
                        .build())
                .build();
    }
}
