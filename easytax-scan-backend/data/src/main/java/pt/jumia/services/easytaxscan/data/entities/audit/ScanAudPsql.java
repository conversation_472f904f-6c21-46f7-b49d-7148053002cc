package pt.jumia.services.easytaxscan.data.entities.audit;

import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.RevisionType;
import pt.jumia.services.easytaxscan.data.entities.CountryPsql;
import pt.jumia.services.easytaxscan.data.entities.QueryPsql;
import pt.jumia.services.easytaxscan.domain.entities.Scan;

import java.time.LocalDateTime;

@Data
@Entity
@NoArgsConstructor
@Getter
@Setter
@Table(name = "scan_aud", schema = "audit")
public class ScanAudPsql {

    @EmbeddedId
    private DefaultAudIdPsql id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fk_query")
    private QueryPsql queryPsql;

    @Column(name = "cron_expression", nullable = false)
    private String cronExpression;

    @Column(name = "code", nullable = false, unique = true)
    private String code;

    @Column(name = "description", nullable = false)
    private String description;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "mode", nullable = false)
    private String mode;

    @Column(name = "mapping", nullable = false)
    private String mapping;

    @Column(name = "sid_column", nullable = false)
    private String sidColumn;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fk_country", nullable = true)
    private CountryPsql country;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false, length = 256)
    private String createdBy;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false, length = 256)
    private String updatedBy;

    @Enumerated(EnumType.ORDINAL)
    @Column(name = "revtype")
    private RevisionType revType;

    public Scan toEntity() {
        return Scan
                .builder()
                .id(id.getId())
                .query(queryPsql != null ? queryPsql.toEntity() : null)
                .cronExpression(cronExpression)
                .code(code)
                .description(description)
                .status(Scan.Status.valueOf(status))
                .mode(Scan.Mode.valueOf(mode))
                .mapping(mapping)
                .sidColumn(sidColumn)
                .country(country != null ? country.toEntity() : null)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}
