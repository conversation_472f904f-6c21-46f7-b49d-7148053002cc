package pt.jumia.services.easytaxscan.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import pt.jumia.services.easytaxscan.data.entities.DataSourcePsql;

import java.util.Optional;

/**
 * JPA repository for DataSource
 */
public interface JpaDataSourceRepository extends JpaRepository<DataSourcePsql, Long> {

    Optional<DataSourcePsql> findByCode(String code);

}
