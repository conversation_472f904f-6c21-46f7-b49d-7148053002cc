package pt.jumia.services.easytaxscan.data.repository.dao.sql;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import pt.jumia.services.easytaxscan.domain.properties.DatabaseProperties;
import pt.jumia.services.easytaxscan.domain.repository.EntityDataSourceRepository;

import javax.sql.DataSource;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class SQLServerEntityDataSourceAdapter implements EntityDataSourceRepository {

    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    public SQLServerEntityDataSourceAdapter(DatabaseProperties.DatasourceProperties dataSource) {
        this.namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(buildDataSource(dataSource));
    }

    @Override
    public List query(String query, Map<String, Object> args) {
        try {
            return this.namedParameterJdbcTemplate.query(query, args, new EntityRowMapper());
        } catch (org.springframework.jdbc.CannotGetJdbcConnectionException e) {
            // Second attempt after connection exception
            return this.namedParameterJdbcTemplate.query(query, args, new EntityRowMapper());
        } catch (DataAccessException e) {
            throw new RuntimeException(
                    MessageFormat.format("Connection exception while executing query: {0}",
                            ExceptionUtils.getStackTrace(e)),
                    e);
        }
    }

    private DataSource buildDataSource(DatabaseProperties.DatasourceProperties datasourceProperties) {
        HikariConfig config = new HikariConfig();

        // SQL Server specific JDBC URL format with semicolons
        String jdbcUrl = String.format("jdbc:%s://%s:%d;databaseName=%s%s",
                datasourceProperties.getConnection().getDatasource(),
                datasourceProperties.getConnection().getUrl(),
                datasourceProperties.getConnection().getPort(),
                datasourceProperties.getConnection().getDatabase(),
                buildConnectionArguments(datasourceProperties));

        config.setJdbcUrl(jdbcUrl);
        config.setUsername(datasourceProperties.getConnection().getUsername());
        config.setPassword(datasourceProperties.getConnection().getPassword());
        config.setMaximumPoolSize(datasourceProperties.getConnection().getThreadPoolSize());

        return new HikariDataSource(config);
    }

    private String buildConnectionArguments(DatabaseProperties.DatasourceProperties datasourceProperties) {
        // SQL Server uses semicolons (;) instead of ampersands (&) for parameter separation
        StringBuilder sbArgs = new StringBuilder(";autoReconnect=true;trustServerCertificate=true");

        if (datasourceProperties.getConnection().isAllowPublicKeyRetrieval()) {
            sbArgs.append(";allowPublicKeyRetrieval=true");
        }

        if (!StringUtils.isEmpty(datasourceProperties.getConnection().getSchema())) {
            sbArgs.append(";currentSchema=");
            sbArgs.append(datasourceProperties.getConnection().getSchema());
        }

        return sbArgs.toString();
    }

    @Override
    public void disconnect() {
        DataSource dataSource = namedParameterJdbcTemplate.getJdbcTemplate().getDataSource();
        if (dataSource != null) {
            ((HikariDataSource) dataSource).close();
        }
    }

    private static class EntityRowMapper implements RowMapper<LinkedHashMap> {
        @Override
        public LinkedHashMap mapRow(ResultSet resultSet, int i) throws SQLException {
            LinkedHashMap<String, Object> row = new LinkedHashMap<>();

            ResultSetMetaData rsmd = resultSet.getMetaData();
            int columnCount = rsmd.getColumnCount();

            for (int ci = 1; ci <= columnCount; ++ci) {
                String columnName = rsmd.getColumnName(ci);
                Object cio = resultSet.getObject(ci);
                row.put(columnName, cio);
            }

            return row;
        }
    }
}

