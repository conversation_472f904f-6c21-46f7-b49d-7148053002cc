package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.easytaxscan.data.audit.RevisionTypeConverter;
import pt.jumia.services.easytaxscan.data.entities.QRevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.RevisionPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.ExecutionLogAuditPsql;
import pt.jumia.services.easytaxscan.data.entities.audit.QExecutionLogAuditPsql;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.ExecutionLogAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.ExecutionLogAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.ReadExecutionLogUseCase;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class PsqlExecutionLogAuditRepository implements ExecutionLogAuditRepository {

    private static final QExecutionLogAuditPsql Q_EXECUTION_LOG_AUD_PSQL = QExecutionLogAuditPsql.executionLogAuditPsql;
    private static final QRevisionPsql Q_REVISION_PSQL = QRevisionPsql.revisionPsql;

    private final EntityManager entityManager;
    private final ReadExecutionLogUseCase readExecutionLogUseCase;

    public PsqlExecutionLogAuditRepository(EntityManager entityManager, ReadExecutionLogUseCase readExecutionLogUseCase) {
        this.entityManager = entityManager;
        this.readExecutionLogUseCase = readExecutionLogUseCase;
    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditedEntity<ExecutionLog>> getExecutionLogAuditLogById(ExecutionLogAuditFilters filters, ExecutionLog executionLog) {
        JPAQuery<Tuple> query = baseQuery(filters)
                .select(Q_EXECUTION_LOG_AUD_PSQL, Q_REVISION_PSQL);

        return query.fetch().stream().map(tuple -> {
            RevisionPsql revisionPsql = tuple.get(Q_REVISION_PSQL);
            ExecutionLogAuditPsql executionLogAudPsql = tuple.get(Q_EXECUTION_LOG_AUD_PSQL);

            AuditedEntity.OperationType operationType = RevisionTypeConverter.toOperationType(
                    Objects.requireNonNull(executionLogAudPsql).getRevType());

            assert executionLogAudPsql != null;
            assert revisionPsql != null;
            return AuditedEntity.<ExecutionLog>builder()
                    .auditedEntity(AuditedEntities.EXECUTION_LOG)
                    .operationType(operationType)
                    .revisionInfo(revisionPsql.toEntity())
                    .entity(executionLogAudPsql.toEntity())
                    .build();
        }).collect(Collectors.toList());
    }

    private JPAQuery<?> baseQuery(ExecutionLogAuditFilters filters) {
        JPAQuery<?> query = new JPAQueryFactory(entityManager)
                .from(Q_EXECUTION_LOG_AUD_PSQL)
                .innerJoin(Q_REVISION_PSQL).on(Q_EXECUTION_LOG_AUD_PSQL.id.rev.eq(Q_REVISION_PSQL.id));

        if (Objects.nonNull(filters.getId())) {
            query.where(Q_EXECUTION_LOG_AUD_PSQL.id.id.eq(filters.getId()));
        }

        AuditRepositoryHelper.applyGenericFiltersAndSorting(query, Q_REVISION_PSQL, filters);
        return query;
    }

    @Override
    public long getAuditLogCountById(ExecutionLogAuditFilters filters) {
        return baseQuery(filters).fetchCount();
    }
}
