package pt.jumia.services.easytaxscan.data.entities.audit;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RevisionType;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.Query;

import java.time.LocalDateTime;

@Entity
@Audited
@Table(name = "query_aud", schema = "audit")
@NoArgsConstructor
@Data
public class QueryAuditPsql {


    @EmbeddedId
    private DefaultAudIdPsql id;

    @Column(name = "sql", nullable = false)
    private String sql;

    @Column(name = "code", nullable = false)
    private String code;

    @Column(name = "description", nullable = false)
    private String description;

    @Column(name = "page_size", nullable = false)
    private Integer pageSize;

    @Column(name = "pagination_field", nullable = false)
    private String paginationField;

    @Column(name = "fk_datasource")
    private Long dataSourcePsql;

    @Column(name = "sample_result", nullable = false)
    private String sampleResult;

    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @Enumerated(EnumType.ORDINAL)
    @Column(name = "revtype")
    private RevisionType revType;

    // Convert back to Query domain model
    public Query toEntity(DataSource dataSource) {
        return Query.builder()
                .id(this.id.getId())
                .sql(sql)
                .code(code)
                .description(description)
                .pageSize(pageSize)
                .paginationField(paginationField)
                .dataSource(dataSource)
                .sampleResult(sampleResult)
                .createdBy(createdBy)
                .createdAt(createdAt)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .build();
    }
}
