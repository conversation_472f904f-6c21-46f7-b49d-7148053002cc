package pt.jumia.services.easytaxscan.data.audit.listener;

import org.hibernate.envers.RevisionListener;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.data.entities.RevisionPsql;
import pt.jumia.services.easytaxscan.domain.entities.HelpCenterTicket;
import pt.jumia.services.easytaxscan.domain.RequestContext;

import static pt.jumia.services.easytaxscan.domain.utils.AppConstants.SYSTEM;

/**
 * {@link RevisionListener} implementation, which will be called every time there is a changed to an audited entity
 */
public class UserRevisionListener implements RevisionListener {

    @Override
    public void newRevision(Object revisionEntity) {
        RevisionPsql revision = (RevisionPsql) revisionEntity;
        RequestUser user = RequestContext.getUser();
        revision.setEmail(user == null ? SYSTEM : user.getEmail());
        revision.setTime(DateUtil.getCurrentLocalDateTimeForUTC());
        HelpCenterTicket helpCenterTicket = RequestContext.getHelpCenterTicket();
        revision.setHcTicketLink(helpCenterTicket != null ? helpCenterTicket.getLink() : null);
        revision.setHcTicketJustification(helpCenterTicket != null ? helpCenterTicket.getJustification() : null);
    }
}
