package pt.jumia.services.easytaxscan.data.utils;

import org.jetbrains.annotations.NotNull;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

public class DateUtils {
    public static @NotNull LocalDateTime getTimeStamp(LocalDateTime timeStamp) {
        return Objects.nonNull(timeStamp)
                ? timeStamp.truncatedTo(ChronoUnit.MICROS)
                : LocalDateTime.now().truncatedTo(ChronoUnit.MICROS);
    }

    public static @NotNull LocalDateTime getCurrentTimeStamp() {
        return LocalDateTime.now().truncatedTo(ChronoUnit.MICROS);
    }
}
