package pt.jumia.services.easytaxscan.data.entities;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.Mode;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

@Entity
@Audited
@Table(name = "document")
@NoArgsConstructor
@Data
public class DocumentPsql {

    public static final Map<Document.SortingFields, String> ENTITY_FIELDS;

    static {
        ENTITY_FIELDS = Map.ofEntries(
                Map.entry(Document.SortingFields.ID, "id"),
                Map.entry(Document.SortingFields.UPDATED_AT, "updatedAt")
        );
    }

    @Id
    @SequenceGenerator(name = "document_seq", sequenceName = "document_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "document_seq")
    @Column(name = "id")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fk_execution_log")
    private ExecutionLogPsql executionLogPsql;

    @Column(name = "sid", nullable = false, length = 64)
    private String sid;

    @Column(name = "status", nullable = false, length = 32)
    private String status;

    @Column(name = "mode", nullable = false, length = 32)
    private String mode;

    @Column(name = "query_data", nullable = false)
    private String queryData;

    @Column(name = "request_payload", nullable = false)
    private String requestPayload;

    @Column(name = "response_code", nullable = false)
    private String responseCode;

    @Column(name = "response_payload", nullable = false)
    private String responsePayload;

    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    public DocumentPsql(Document document) {
        this.id = document.getId();
        this.executionLogPsql = new ExecutionLogPsql(document.getExecutionLog());
        this.sid = document.getSid();
        this.status = document.getStatus().name();
        this.mode = document.getMode().name();
        this.queryData = document.getQueryData();
        this.requestPayload = document.getRequestPayload();
        this.responsePayload = document.getResponsePayload();
        this.responseCode = document.getResponseCode();
        this.createdAt = DateUtils.getTimeStamp(document.getCreatedAt());
        this.createdBy = Objects.isNull(document.getCreatedBy()) ? RequestContext.getUsername() : document.getCreatedBy();
        this.updatedAt = DateUtils.getTimeStamp(document.getUpdatedAt());
        this.updatedBy = Objects.isNull(document.getUpdatedBy()) ? RequestContext.getUsername() : document.getUpdatedBy();
    }

    public Document toEntity() {
        return Document
                .builder()
                .id(id)
                .executionLog(executionLogPsql.toEntity())
                .sid(sid)
                .status(Document.Status.valueOf(status))
                .mode(Mode.valueOf(mode))
                .queryData(queryData)
                .requestPayload(requestPayload)
                .responsePayload(responsePayload)
                .responseCode(responseCode)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}
