package pt.jumia.services.easytaxscan.data.entities;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "country")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Audited
public class CountryPsql {

    @Id
    @SequenceGenerator(name = "country_seq", sequenceName = "country_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "country_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "country_code", nullable = false)
    private String countryCode;

    @Column(name = "country_name", nullable = false)
    private String countryName;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    public Country toEntity() {
        return Country.builder()
                .id(this.id)
                .countryCode(this.countryCode)
                .countryName(this.countryName)
                .createdAt(this.createdAt)
                .createdBy(this.createdBy)
                .updatedAt(this.updatedAt)
                .updatedBy(this.updatedBy)
                .build();
    }

    public CountryPsql(Country country) {
        this.id = country.getId();
        this.countryCode = country.getCountryCode();
        this.countryName = country.getCountryName();
        this.createdBy = Objects.isNull(country.getCreatedBy()) ? RequestContext.getUsername() : country.getCreatedBy();
        this.updatedBy = Objects.isNull(country.getUpdatedBy()) ? RequestContext.getUsername() : country.getUpdatedBy();
        this.createdAt = DateUtils.getTimeStamp(country.getCreatedAt());
        this.updatedAt = DateUtils.getTimeStamp(country.getUpdatedAt());
    }
}
