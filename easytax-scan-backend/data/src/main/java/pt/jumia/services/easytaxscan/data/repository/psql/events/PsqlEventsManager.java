package pt.jumia.services.easytaxscan.data.repository.psql.events;


import java.sql.SQLException;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import pt.jumia.services.easytaxscan.data.repository.psql.events.channels.PsqlChannelFactory;
import pt.jumia.services.easytaxscan.data.repository.psql.events.channels.PsqlSettingsChangeChannel;
import pt.jumia.services.easytaxscan.domain.DataEventsNotificator;
import pt.jumia.services.easytaxscan.domain.properties.DataProperties;

/**
 * Psql events manager implementation that is responsible for:
 * 1 - Register and unregister a postgresql channel.
 * 2 - Execute a "listenToMe" command for channels to receive notifications.
 * 3 - Trigger notification events for the available channels.
 * <p>
 */
@Repository
public class PsqlEventsManager implements DataEventsNotificator {

    private final PsqlSettingsChangeChannel psqlSettingsChangeChannel;

    private final DataProperties dataProperties;

    @Autowired
    public PsqlEventsManager(
            PsqlChannelFactory channelFactory,
            DataProperties dataProperties)
            throws SQLException {
        this.psqlSettingsChangeChannel = channelFactory.createSettingsChangeChannel();
        this.dataProperties = dataProperties;
    }

    @PostConstruct
    public void start() {
        psqlSettingsChangeChannel.start();
    }

    @PreDestroy
    public void stop() {
        psqlSettingsChangeChannel.stop();
    }

    @Override
    public void notifySettingChanges() {
        psqlSettingsChangeChannel.notifyMe();
    }

}
