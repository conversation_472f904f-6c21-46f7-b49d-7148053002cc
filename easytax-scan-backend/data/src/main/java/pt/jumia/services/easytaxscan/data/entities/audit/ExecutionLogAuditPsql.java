package pt.jumia.services.easytaxscan.data.entities.audit;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RevisionType;
import pt.jumia.services.easytaxscan.data.entities.ScanPsql;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.Mode;

import java.time.LocalDateTime;

@Data
@Entity
@NoArgsConstructor
@Audited
@Table(name = "execution_log_aud", schema = "audit")
public class ExecutionLogAuditPsql {

    @EmbeddedId
    private DefaultAudIdPsql id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fk_scan", nullable = false)
    private ScanPsql scanPsql;

    @Column(name = "total_results", nullable = false)
    private Integer totalResults;

    @Column(name = "exception", nullable = false)
    private String exception;

    @Column(name = "duration_ms", nullable = false)
    private Long durationMs;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "mode", nullable = false)
    private String mode;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false, length = 256)
    private String createdBy;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false, length = 256)
    private String updatedBy;

    @Enumerated(EnumType.ORDINAL)
    @Column(name = "revtype")
    private RevisionType revType;

    public ExecutionLog toEntity() {
        return ExecutionLog
                .builder()
                .id(id.getId())
                .scan(scanPsql != null ? scanPsql.toEntity() : null)
                .totalResults(totalResults)
                .exception(exception)
                .durationMs(durationMs)
                .status(ExecutionLog.Status.valueOf(status))
                .mode(Mode.valueOf(mode))
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}
