package pt.jumia.services.easytaxscan.data.repository.psql;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.data.entities.DataSourcePsql;
import pt.jumia.services.easytaxscan.data.entities.QDataSourcePsql;
import pt.jumia.services.easytaxscan.data.entities.QQueryPsql;
import pt.jumia.services.easytaxscan.data.entities.QueryPsql;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaDataSourceRepository;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaQueryRepository;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.filter.QueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.QuerySortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.QueryRepository;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsFirst;
import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsLast;

@Slf4j
@Repository
public class PsqlQueryRepository extends PsqlRepository implements QueryRepository {

    private final JpaQueryRepository jpaQueryRepository;
    private final EntityManager entityManager;
    private final JpaDataSourceRepository jpaDataSourceRepository;
    private final QQueryPsql root = QQueryPsql.queryPsql;
    private final QDataSourcePsql dataSourcePsql = QDataSourcePsql.dataSourcePsql;


    public PsqlQueryRepository(TransactionTemplate transactionTemplate, JpaQueryRepository jpaQueryRepository,
                               EntityManager entityManager, JpaDataSourceRepository jpaDataSourceRepository) {
        super(transactionTemplate);
        this.jpaQueryRepository = jpaQueryRepository;
        this.entityManager = entityManager;
        this.jpaDataSourceRepository = jpaDataSourceRepository;
    }


    @Override
    @Transactional
    public Query insert(Query query) {
        DataSourcePsql dataSourcePsql = jpaDataSourceRepository.findById(query.getDataSource().getId())
                .orElseThrow(() -> NotFoundException.build(DataSource.class, "ID", query.getDataSource().getId()));
        query.setDataSource(dataSourcePsql.toEntity());
        return jpaQueryRepository.save(new QueryPsql(query)).toEntity();

    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Query> findById(Long id) {
        return jpaQueryRepository.findById(id).map(QueryPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Query> findByCode(String code) {
        return jpaQueryRepository.findByCode(code).map(QueryPsql::toEntity);
    }

    @Override
    @Transactional
    public Query update(long id, Query updateQuery) {
        QueryPsql queryPsql = jpaQueryRepository.findById(id).orElseThrow(() -> NotFoundException.build(Query.class, id));

        queryPsql.setSql(updateQuery.getSql());
        queryPsql.setDescription(updateQuery.getDescription());
        queryPsql.setPageSize(updateQuery.getPageSize());
        queryPsql.setPaginationField(updateQuery.getPaginationField());
        queryPsql.setSampleResult(updateQuery.getSampleResult());
        queryPsql.setUpdatedBy(RequestContext.getUsername());
        queryPsql.setUpdatedAt(DateUtils.getCurrentTimeStamp());
        return jpaQueryRepository.save(queryPsql).toEntity();
    }

    @Override
    @Transactional(readOnly = true)
    public Long executeCount(QueryFilters filters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager).select(root.id.count()).from(root);

        buildWhereClauses(filters, query);
        return Objects.requireNonNull(query.fetchOne());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Query> findAll(QueryFilters filters, QuerySortFilters sortFilters, PageFilters pageFilters) {
        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<QueryPsql> query = queryFactory.selectFrom(root).distinct();
        buildWhereClauses(filters, query);
        query.orderBy(getOrderSpecifier(sortFilters)).offset((long) (pageFilters.getPage() - 1) * pageFilters.getSize())
                .limit(pageFilters.getSize() + 1);
        return query.fetch().stream().map(QueryPsql::toEntity).toList();
    }

    @Override
    public void deleteById(Long id) {
        jpaQueryRepository.deleteById(id);
    }


    private void buildWhereClauses(QueryFilters filters, JPAQuery<?> query) {

        if (!Objects.isNull(filters)) {
            BooleanBuilder builder = new BooleanBuilder();
            filterText(filters, builder);
            filterByDataSourceId(filters, builder);
            query.where(builder.getValue());
        }

    }

    private void filterByDataSourceId(QueryFilters filters, BooleanBuilder builder) {
        if (filters.getDataSourceId() != null) {
            jpaDataSourceRepository.findById(filters.getDataSourceId())
                    .orElseThrow(() -> NotFoundException.build(DataSource.class, filters.getDataSourceId()));
            builder.and(
                    root.dataSourcePsql.id.eq(filters.getDataSourceId()));
        }
    }


    private void filterText(QueryFilters filter, BooleanBuilder builder) {
        if (filter.getText() != null) {
            builder.andAnyOf(root.description.containsIgnoreCase(filter.getText()),
                    root.code.containsIgnoreCase(filter.getText()),
                    root.dataSourcePsql.code.eq(filter.getText()));
        }
    }

    private OrderSpecifier<?> getOrderSpecifier(QuerySortFilters sortFilters) {
        String field = QueryPsql.ENTITY_FIELDS.get(sortFilters.getField());
        if (OrderDirection.ASC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.ASC, ExpressionUtils.path(QQueryPsql.class, root, field), NullsFirst);
        } else if (OrderDirection.DESC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.DESC, ExpressionUtils.path(QQueryPsql.class, root, field), NullsLast);
        } else {
            throw new IllegalArgumentException("Order must be either asc or desc");
        }
    }

}
