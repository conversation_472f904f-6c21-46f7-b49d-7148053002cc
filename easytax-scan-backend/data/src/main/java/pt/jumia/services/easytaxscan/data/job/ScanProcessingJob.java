package pt.jumia.services.easytaxscan.data.job;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.usecases.kafka.NotifyBillDocumentUseCase;
import pt.jumia.services.easytaxscan.domain.jobs.ScanJobExecutionUseCase;

@Slf4j
@Component
public class ScanProcessingJob extends QuartzJobBean {

    @Override
    protected void executeInternal(@NotNull JobExecutionContext context) throws JobExecutionException {
        String scanCode = null;
        try {
            scanCode = context.getJobDetail().getJobDataMap().getString("jobName");
            log.info("Starting scan job:{}  ", scanCode);

            ApplicationContext applicationContext = (ApplicationContext)
                    context.getScheduler().getContext().get("applicationContext");

            ScanJobExecutionUseCase scanJobExecutionUseCase =
                    applicationContext.getBean(ScanJobExecutionUseCase.class);

            log.info("Executing scan job: {}", scanCode);
            scanJobExecutionUseCase.execute(scanCode);
            log.info("Job {} execution completed.", scanCode);
        } catch (Exception e) {
            log.error("Error while executing scan processing job:{}  message:{}", scanCode, e.getMessage());
        }
    }
}

