package pt.jumia.services.easytaxscan.data.repository.psql;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.data.entities.ExecutionLogPsql;
import pt.jumia.services.easytaxscan.data.entities.QExecutionLogPsql;
import pt.jumia.services.easytaxscan.data.entities.ScanPsql;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaExecutionLogRepository;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaScanRepository;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.ExecutionLogRepository;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsFirst;
import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsLast;

@Slf4j
@Repository
public class PsqlExecutionLogRepository extends PsqlRepository implements ExecutionLogRepository {

    private final JpaExecutionLogRepository jpaExecutionLogRepository;
    private final JpaScanRepository jpaScanRepository;
    private final EntityManager entityManager;
    private final QExecutionLogPsql root = QExecutionLogPsql.executionLogPsql;


    public PsqlExecutionLogRepository(TransactionTemplate transactionTemplate,
                                      JpaScanRepository jpaScanRepository,
                                      JpaExecutionLogRepository jpaExecutionLogRepository,
                                      EntityManager entityManager) {
        super(transactionTemplate);
        this.jpaScanRepository = jpaScanRepository;
        this.jpaExecutionLogRepository = jpaExecutionLogRepository;
        this.entityManager = entityManager;
    }


    @Override
    @Transactional
    public ExecutionLog insert(ExecutionLog executionLog) {

        ScanPsql scanPsql = jpaScanRepository.findById(Objects.requireNonNull(executionLog.getScan().getId()))
                .orElseThrow(() -> NotFoundException.build(Scan.class, "ID", executionLog.getScan().getId()));
        executionLog.setScan(scanPsql.toEntity());
        return jpaExecutionLogRepository.save(new ExecutionLogPsql(executionLog)).toEntity();
    }

    @Override
    @Transactional
    public ExecutionLog update(long id,ExecutionLog executionLog) {
        ExecutionLogPsql executionPsql = jpaExecutionLogRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(Scan.class, "ID", id));

        executionPsql.setUpdatedAt(LocalDateTime.now());
        executionPsql.setUpdatedBy(RequestContext.getUsername());
        executionPsql.setMode(executionLog.getMode().name());
        executionPsql.setStatus(executionLog.getStatus().name());
        executionPsql.setTotalResults(executionLog.getTotalResults());
        executionPsql.setDurationMs(executionLog.getDurationMs());
        executionPsql.setLastRecordId(executionLog.getLastRecordId());
        executionPsql.setException(executionLog.getException());

        return jpaExecutionLogRepository.save(executionPsql).toEntity();

    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ExecutionLog> findById(Long id) {
        return jpaExecutionLogRepository.findById(id).map(ExecutionLogPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ExecutionLog> findAll(ExecutionLogFilters filters, ExecutionLogSortFilters sortFilters, PageFilters pageFilters) {
        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ExecutionLogPsql> query = queryFactory.selectFrom(root).distinct();
        buildWhereClauses(filters, query);
        query.orderBy(getOrderSpecifier(sortFilters)).offset((long) (pageFilters.getPage() - 1) * pageFilters.getSize())
                .limit(pageFilters.getSize() + 1);
        return query.fetch().stream().map(ExecutionLogPsql::toEntity).toList();
    }

    @Override
    @Transactional
    public void deleteById(long id) {
        jpaExecutionLogRepository.deleteById(id);

    }

    @Override
    @Transactional(readOnly = true)
    public Long executeCount(ExecutionLogFilters filters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager).select(root.id.count()).from(root);

        buildWhereClauses(filters, query);
        return Objects.requireNonNull(query.fetchOne());
    }

    private void buildWhereClauses(ExecutionLogFilters filters, JPAQuery<?> query) {

        if (!Objects.isNull(filters)) {
            BooleanBuilder builder = new BooleanBuilder();
            filterStatus(filters, builder);
            filterScanId(filters, builder);
            filtersFromDate(filters, builder);
            filtersToDate(filters, builder);
            query.where(builder.getValue());
        }

    }

    private void filtersFromDate(ExecutionLogFilters filters, BooleanBuilder builder) {
        if (filters.getCreatedFrom() != null) {
            builder.and(root.createdAt.goe(filters.getCreatedFrom()));
        }
    }

    private void filtersToDate(ExecutionLogFilters filters, BooleanBuilder builder) {
        if (filters.getCreatedTo() != null) {
            builder.and(root.createdAt.loe(filters.getCreatedTo()));
        }
    }

    private void filterScanId(ExecutionLogFilters filters, BooleanBuilder builder) {
        if (filters.getScanId() != null) {
            jpaScanRepository.findById(filters.getScanId())
                    .orElseThrow(() -> NotFoundException.build(ExecutionLog.class, filters.getScanId()));
            builder.and(
                    root.scanPsql.id.eq(filters.getScanId()));
        }
    }


    private void filterStatus(ExecutionLogFilters filter, BooleanBuilder builder) {
        if (filter.getStatus() != null) {
            builder.and(
                    root.status.eq(filter.getStatus().toString()));
        }

    }


    private OrderSpecifier<?> getOrderSpecifier(ExecutionLogSortFilters sortFilters) {

        String field = ExecutionLogPsql.ENTITY_FIELDS.get(sortFilters.getField());
        if (OrderDirection.ASC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.ASC, ExpressionUtils.path(QExecutionLogPsql.class, root, field), NullsFirst);
        } else if (OrderDirection.DESC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.DESC, ExpressionUtils.path(QExecutionLogPsql.class, root, field), NullsLast);
        } else {
            throw new IllegalArgumentException("Order must be either asc or desc");
        }
    }


}
