package pt.jumia.services.easytaxscan.data.entities;

import jakarta.persistence.*;
import lombok.Data;
import pt.jumia.services.easytaxscan.domain.entities.Client;

@Data
@Entity
@Table(name = "clients")
public class ClientPsql {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long sid;

    @Column(name = "client_code", unique = true)
    private String clientCode;

    @Column(name = "client_name")
    private String clientName;

    @Column(name = "address")
    private String address;

    @Column(name = "contact_person")
    private String contactPerson;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "email")
    private String email;

    public Client toEntity() {
        return Client
                .builder()
                .sid(sid)
                .clientCode(this.clientCode)
                .clientName(this.clientName)
                .address(this.address)
                .contactPerson(this.contactPerson)
                .phoneNumber(this.phoneNumber)
                .email(this.email)
                .build();
    }
}

