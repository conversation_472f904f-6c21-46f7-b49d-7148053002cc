package pt.jumia.services.easytaxscan.data.repository.psql;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.data.entities.DataSourcePsql;
import pt.jumia.services.easytaxscan.data.entities.QDataSourcePsql;
import pt.jumia.services.easytaxscan.data.repository.psql.jpa.JpaDataSourceRepository;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceRepository;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsFirst;
import static com.querydsl.core.types.OrderSpecifier.NullHandling.NullsLast;

@Slf4j
@Repository
public class PsqlDataSourceRepository extends PsqlRepository implements DataSourceRepository {

    private final JpaDataSourceRepository jpaDataSourceRepository;
    private final EntityManager entityManager;
    private final QDataSourcePsql root = QDataSourcePsql.dataSourcePsql;


    public PsqlDataSourceRepository(TransactionTemplate transactionTemplate,
                                    JpaDataSourceRepository jpaDataSourceRepository, EntityManager entityManager) {
        super(transactionTemplate);
        this.jpaDataSourceRepository = jpaDataSourceRepository;
        this.entityManager = entityManager;
    }

    @Override
    @Transactional
    public DataSource insert(DataSource dataSource) {
        return jpaDataSourceRepository.save(new DataSourcePsql(dataSource)).toEntity();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<DataSource> findById(Long id) {
        return jpaDataSourceRepository.findById(id).map(DataSourcePsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<DataSource> findByCode(String code) {
        return jpaDataSourceRepository.findByCode(code).map(DataSourcePsql::toEntity);
    }


    @Override
    @Transactional(readOnly = true)
    public List<DataSource> findAll(DataSourceFilters filters,
                                    DataSourceSortFilters sortFilters,
                                    PageFilters pageFilters) {
        JPAQueryFactory queryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<DataSourcePsql> query = queryFactory
                .selectFrom(root)
                .distinct();
        buildWhereClauses(filters, query);
        query.orderBy(getOrderSpecifier(sortFilters))
                .offset((long) (pageFilters.getPage() - 1) * pageFilters.getSize())
                .limit(pageFilters.getSize() + 1);
        return query.fetch().stream()
                .map(DataSourcePsql::toEntity).toList();
    }

    private void buildWhereClauses(DataSourceFilters filters, JPAQuery<?> query) {

        if (!Objects.isNull(filters)) {
            BooleanBuilder builder = new BooleanBuilder();
            filterText(filters, builder);
            filterStatus(filters, builder);
            query.where(builder.getValue());
        }

    }

    private void filterText(DataSourceFilters filter, BooleanBuilder builder) {
        if (filter.getText() != null) {
            builder.andAnyOf(
                    root.description.containsIgnoreCase(filter.getText()),
                    root.code.containsIgnoreCase(filter.getText()));
        }
    }

    private void filterStatus(DataSourceFilters filter, BooleanBuilder builder) {
        if (filter.getStatus() != null) {
            builder.and(
                    root.status.eq(filter.getStatus().toString()));
        }
    }

    @Override
    @Transactional
    public DataSource update(long id, DataSource updateDataSource) {
        DataSourcePsql dataSourcePsql = jpaDataSourceRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(DataSource.class, id));

        dataSourcePsql.setStatus(updateDataSource.getStatus().name());
        dataSourcePsql.setDescription(updateDataSource.getDescription());
        dataSourcePsql.setCountrySegregated(updateDataSource.getCountrySegregated());
        dataSourcePsql.setUpdatedBy(RequestContext.getUsername());
        dataSourcePsql.setUpdatedAt(DateUtils.getCurrentTimeStamp());
        return jpaDataSourceRepository.save(dataSourcePsql).toEntity();
    }

    @Override
    @Transactional
    public void deleteById(long id) {
        jpaDataSourceRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Long executeCount(DataSourceFilters filters) {
        JPAQuery<Long> query = new JPAQueryFactory(entityManager)
                .select(root.id.count())
                .from(root);

        buildWhereClauses(filters, query);
        return Objects.requireNonNull(query.fetchOne());
    }

    private OrderSpecifier<?> getOrderSpecifier(DataSourceSortFilters sortFilters) {
        String field = DataSourcePsql.ENTITY_FIELDS.get(sortFilters.getField());
        if (OrderDirection.ASC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.ASC, ExpressionUtils.path(QDataSourcePsql.class, root, field), NullsFirst);
        } else if (OrderDirection.DESC.equals(sortFilters.getDirection())) {
            return new OrderSpecifier(Order.DESC,
                    ExpressionUtils.path(QDataSourcePsql.class, root, field), NullsLast);
        } else {
            throw new IllegalArgumentException("Order must be either asc or desc");
        }
    }

}
