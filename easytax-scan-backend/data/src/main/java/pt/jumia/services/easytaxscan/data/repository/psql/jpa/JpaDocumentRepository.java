package pt.jumia.services.easytaxscan.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import pt.jumia.services.easytaxscan.data.entities.DocumentPsql;

import java.util.Optional;

@Repository
public interface JpaDocumentRepository extends JpaRepository<DocumentPsql, Long> {
    Optional<DocumentPsql> findBySid(String sid);

}
