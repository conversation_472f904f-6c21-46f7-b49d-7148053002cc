package pt.jumia.services.easytaxscan.data.entities;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

@Entity
@Audited
@Table(name = "sub_query")
@NoArgsConstructor
@Data
public class SubQueryPsql {

    public static final Map<pt.jumia.services.easytaxscan.domain.entities.SubQuery.SortingFields, String> ENTITY_FIELDS;

    static {
        ENTITY_FIELDS = Map.ofEntries(
                Map.entry(SubQuery.SortingFields.ID, "id"),
                Map.entry(SubQuery.SortingFields.UPDATED_AT, "updatedAt")
        );
    }

    @Id
    @SequenceGenerator(name = "sub_query_seq", sequenceName = "sub_query_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sub_query_seq")
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "main_query_column", nullable = false)
    private String mainQueryColumn;

    @Column(name = "sub_query_column", nullable = false)
    private String subQueryColumn;

    @Column(name = "status", nullable = false)
    private String status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fk_main_query")
    private QueryPsql query;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fk_sub_query")
    private QueryPsql subQuery;

    @Column(name = "is_list", nullable = false)
    private Boolean isList;

    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    public SubQueryPsql(SubQuery subQuery) {
        this.id = subQuery.getId();
        this.mainQueryColumn = subQuery.getMainQueryColumn();
        this.subQueryColumn = subQuery.getSubQueryColumn();
        this.isList = subQuery.getIsList();
        this.query = new QueryPsql(subQuery.getQuery());
        this.subQuery = new QueryPsql(subQuery.getSubQuery());
        this.status = subQuery.getStatus().name();
        this.createdBy = Objects.isNull(subQuery.getCreatedBy()) ?
                RequestContext.getUsername() : subQuery.getCreatedBy();
        this.updatedBy = Objects.isNull(subQuery.getUpdatedBy()) ?
                RequestContext.getUsername() : subQuery.getUpdatedBy();
        this.createdAt = DateUtils.getTimeStamp(subQuery.getCreatedAt());
        this.updatedAt = DateUtils.getTimeStamp(subQuery.getUpdatedAt());

    }

    public SubQuery toEntity() {
        return SubQuery.builder()
                .id(id)
                .mainQueryColumn(mainQueryColumn)
                .subQueryColumn(subQueryColumn)
                .status(SubQuery.Status.valueOf(status))
                .isList(isList)
                .createdBy(createdBy)
                .createdAt(createdAt)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .query(query.toEntity())
                .subQuery(subQuery.toEntity())
                .build();
    }
}
