package pt.jumia.services.easytaxscan.data.entities;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.Mode;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

@Entity
@Audited
@Table(name = "execution_log")
@NoArgsConstructor
@Data
public class ExecutionLogPsql {

    public static final Map<ExecutionLog.SortingFields, String> ENTITY_FIELDS;

    static {
        ENTITY_FIELDS = Map.ofEntries(
                Map.entry(ExecutionLog.SortingFields.ID, "id"),
                Map.entry(ExecutionLog.SortingFields.UPDATED_AT, "updatedAt")
        );
    }

    @Id
    @SequenceGenerator(name = "execution_log", sequenceName = "execution_log_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "execution_log")
    @Column(name = "id")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fk_scan")
    private ScanPsql scanPsql;

    @Column(name = "total_results", nullable = false)
    private Integer totalResults;

    @Column(name = "exception", nullable = false)
    private String exception;

    @Column(name = "duration_ms", nullable = false)
    private Long durationMs;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "mode", nullable = false)
    private String mode;

    @Column(name = "last_record_id", nullable = false)
    private Long lastRecordId;

    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false)
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    public ExecutionLogPsql(ExecutionLog executionLog) {
        this.id = executionLog.getId();
        this.scanPsql = new ScanPsql(executionLog.getScan());
        this.durationMs = executionLog.getDurationMs();
        this.exception = executionLog.getException();
        this.totalResults = executionLog.getTotalResults();
        this.status = executionLog.getStatus().name();
        this.mode = executionLog.getMode().name();
        this.lastRecordId = executionLog.getLastRecordId();
        this.createdAt = DateUtils.getTimeStamp(executionLog.getCreatedAt());
        this.createdBy = Objects.isNull(executionLog.getCreatedBy()) ? RequestContext.getUsername() : executionLog.getCreatedBy();
        this.updatedAt = DateUtils.getTimeStamp(executionLog.getUpdatedAt());
        this.updatedBy = Objects.isNull(executionLog.getUpdatedBy()) ? RequestContext.getUsername() : executionLog.getUpdatedBy();


    }

    public ExecutionLog toEntity() {
        return ExecutionLog
                .builder()
                .id(id)
                .scan(scanPsql.toEntity())
                .exception(exception)
                .totalResults(totalResults)
                .status(ExecutionLog.Status.valueOf(status))
                .durationMs(durationMs)
                .mode(Mode.valueOf(mode))
                .lastRecordId(lastRecordId)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}
