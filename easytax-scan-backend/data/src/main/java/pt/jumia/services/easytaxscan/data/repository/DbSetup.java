package pt.jumia.services.easytaxscan.data.repository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.data.utils.MigrationService;
import pt.jumia.services.easytaxscan.domain.Profiles;
import pt.jumia.services.easytaxscan.domain.entities.Tag;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeTags;
import pt.jumia.services.easytaxscan.domain.properties.SpringProperties;
import pt.jumia.services.easytaxscan.domain.usecases.tags.CreateTagsUseCase;

import javax.sql.DataSource;

/**
 * Fills up the DB with data on boot and provides utility methods to reset the DB in tests
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Profile({Profiles.DB_SETUP})
public class DbSetup implements ApplicationListener<ContextRefreshedEvent> {

    private final SpringProperties springProperties;
    private final CreateTagsUseCase createTagsUseCase;
    private final MigrationService migrationService;
    private final DataSource dataSource;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        addDummyData();
    }

    public void cleanDataBase() {
        cleanApplicationSchemas();
        migrateApplicationSchemas();
    }

    public void resetDataBase() {
        cleanDataBase();

        addDummyData();
    }

    private void addDummyData() {
        for (Tag tag : FakeTags.ALL) {
            try {
                log.info("Adding tag " + tag.getName());
                createTagsUseCase.execute(tag);
            } catch (Exception e) {
                log.error("Error inserting {} in memory DB: {}", tag.getName(), ExceptionUtils.getStackTrace(e));
            }
        }
    }

    private void cleanApplicationSchemas() {
        for (String schemaDir : springProperties.getFlyway().getSchemas()) {
            try {
                migrationService.cleanDatabaseSchema(dataSource, schemaDir);
            } catch (Exception e) {
                log.warn("Error cleaning database: {}", ExceptionUtils.getStackTrace(e));
            }
        }
    }

    private void migrateApplicationSchemas() {
        for (String schemaDir : springProperties.getFlyway().getSchemas()) {
            try {
                migrationService.migrateDatabaseSchema(dataSource, schemaDir);
            } catch (Exception e) {
                log.warn("Error migrating database: {}", ExceptionUtils.getStackTrace(e));
            }
        }
    }
}
