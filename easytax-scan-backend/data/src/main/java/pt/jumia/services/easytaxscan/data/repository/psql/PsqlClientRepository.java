package pt.jumia.services.easytaxscan.data.repository.psql;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.easytaxscan.data.entities.ClientPsql;
import pt.jumia.services.easytaxscan.domain.entities.Client;
import pt.jumia.services.easytaxscan.domain.properties.DatabaseProperties;
import pt.jumia.services.easytaxscan.domain.repository.ClientRepository;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceFactory;
import pt.jumia.services.easytaxscan.domain.repository.EntityDataSourceRepository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class PsqlClientRepository implements ClientRepository {
    private final DataSourceFactory dataSourceFactory;
    private final DatabaseProperties databaseProperties;

    public PsqlClientRepository(TransactionTemplate transactionTemplate,
                                DataSourceFactory dataSourceFactory,
                                DatabaseProperties databaseProperties) {
        this.dataSourceFactory = dataSourceFactory;
        this.databaseProperties = databaseProperties;
    }

    @Override
    public List<Client> findAll() {
        EntityDataSourceRepository dataSourceAdapter = dataSourceFactory.getDatasourceAdapter(
                databaseProperties.getDataSourceProperties("cash-rec"));

        String query = "SELECT * FROM clients";

        List<Map<String, Object>> results = dataSourceAdapter.query(query, Map.of());

        return results.stream()
                .map(result -> {
                    ClientPsql clientPsql = new ClientPsql();
                    clientPsql.setSid(((Number) result.get("sid")).longValue());
                    clientPsql.setClientCode((String) result.get("client_code"));
                    clientPsql.setClientName((String) result.get("client_name"));
                    clientPsql.setAddress((String) result.get("address"));
                    clientPsql.setContactPerson((String) result.get("contact_person"));
                    clientPsql.setPhoneNumber((String) result.get("phone_number"));
                    clientPsql.setEmail((String) result.get("email"));
                    return clientPsql.toEntity();
                })
                .collect(Collectors.toList());
    }
}

