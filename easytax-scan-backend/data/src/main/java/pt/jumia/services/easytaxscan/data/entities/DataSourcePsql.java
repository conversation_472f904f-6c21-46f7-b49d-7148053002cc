package pt.jumia.services.easytaxscan.data.entities;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.Audited;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

@Entity
@Audited
@Table(name = "datasource")
@NoArgsConstructor
@Data
public class DataSourcePsql {

    public static final Map<DataSource.SortingFields, String> ENTITY_FIELDS;
    static {
        ENTITY_FIELDS = Map.ofEntries(
                Map.entry(DataSource.SortingFields.ID, "id"),
                Map.entry(DataSource.SortingFields.UPDATED_AT, "updatedAt")
        );
    }

    @Id
    @SequenceGenerator(name = "datasource_seq", sequenceName = "datasource_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "datasource_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "code", nullable = false, length = 64)
    private String code;

    @Column(name = "status", nullable = false, length = 64)
    private String status;

    @Column(name = "description", nullable = false, length = 1024)
    private String description;

    @Column(name = "country_segregated", nullable = false)
    private Boolean countrySegregated;

    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = false, length = 256)
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", nullable = false, length = 256)
    private String updatedBy;

    public DataSourcePsql(DataSource datasource) {
        this.id = datasource.getId();
        this.code = datasource.getCode();
        this.description = datasource.getDescription();
        this.status = datasource.getStatus().name();
        this.countrySegregated = datasource.getCountrySegregated();
        this.createdBy = Objects.isNull(datasource.getCreatedBy()) ?
                RequestContext.getUsername() : datasource.getCreatedBy();
        this.updatedBy = Objects.isNull(datasource.getUpdatedBy()) ?
                RequestContext.getUsername() : datasource.getUpdatedBy();
        this.createdAt = DateUtils.getTimeStamp(datasource.getCreatedAt());
        this.updatedAt = DateUtils.getTimeStamp(datasource.getUpdatedAt());
    }

    public DataSource toEntity() {
        return DataSource
                .builder()
                .id(id)
                .code(code)
                .status(DataSource.Status.valueOf(status))
                .description(description)
                .countrySegregated(countrySegregated)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}
