package pt.jumia.services.easytaxscan.data.job;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentSortFilters;
import pt.jumia.services.easytaxscan.domain.usecases.document.ReadDocumentUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.kafka.NotifyBillDocumentUseCase;

import java.util.List;

@Slf4j
public class DocumentRetryJob extends QuartzJobBean {
    @Override
    protected void executeInternal(@NotNull JobExecutionContext context) throws JobExecutionException {
        String jobName = null;
        try {
            jobName = context.getJobDetail().getJobDataMap().getString("jobName");
            log.info("Processing document retry job:{}  ", jobName);
            ApplicationContext applicationContext = (ApplicationContext)
                    context.getScheduler().getContext().get("applicationContext");

            NotifyBillDocumentUseCase notifyBillDocumentUseCase
                    = applicationContext.getBean(NotifyBillDocumentUseCase.class);
            ReadDocumentUseCase readDocumentUseCase = applicationContext.getBean(ReadDocumentUseCase.class);
            DocumentFilters filters = DocumentFilters.builder().build();
            filters.setStatusList(List.of(Document.Status.ERROR.name()
                    , Document.Status.SUBMITTED.name()));
            List<Document> documentList = readDocumentUseCase.execute(filters, DocumentSortFilters.builder().build(),
                    PageFilters.builder().build());
            for (Document document : documentList) {
                log.info("Processing document retry job:{}  documentId:{}", jobName, document.getId());
                notifyBillDocumentUseCase.execute(jobName, document);
            }

        } catch (Exception e) {
            log.error("Error while executing document retry job:{}  message:{}", jobName, e.getMessage());
        }
    }
}
