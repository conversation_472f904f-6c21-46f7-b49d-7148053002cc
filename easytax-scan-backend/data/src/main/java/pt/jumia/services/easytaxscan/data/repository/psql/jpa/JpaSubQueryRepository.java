package pt.jumia.services.easytaxscan.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import pt.jumia.services.easytaxscan.data.entities.SubQueryPsql;

import java.util.List;
import java.util.Optional;

/**
 * JPA repository for Queries
 */
public interface JpaSubQueryRepository extends JpaRepository<SubQueryPsql, Long> {

    void deleteSubQueryByQueryId(Long queryId);

    void deleteByQueryIdAndSubQueryId(Long queryId, Long subQueryId);

    List<SubQueryPsql> findAllByQueryId(Long queryId);

    Optional<SubQueryPsql> findByQueryIdAndSubQueryId(Long queryId, Long subQueryId);
}
