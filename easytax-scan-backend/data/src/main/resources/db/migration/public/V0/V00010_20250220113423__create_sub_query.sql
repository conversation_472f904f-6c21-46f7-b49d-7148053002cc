-- ########################### Table Creation for sub query###########################

CREATE SEQUENCE IF NOT EXISTS sub_query_sequence_id_seq START 1 INCREMENT 1;

CREATE TABLE IF NOT EXISTS sub_query (
    id             BIGINT     DEFAULT NEXTVAL('sub_query_sequence_id_seq')  PRIMARY KEY,
    fk_main_query    INTEGER      NOT NULL,
    fk_sub_query    INTEGER,
    main_query_column           VARCHAR,
    sub_query_column    VARCHAR,
    status             VARCHAR,
    is_list          BOOLEAN NOT NULL DEFAULT FALSE,
    created_by  <PERSON><PERSON><PERSON><PERSON>,
    created_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by <PERSON><PERSON><PERSON><PERSON>,
    updated_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (fk_main_query) REFERENCES query (id),
    FOREI<PERSON><PERSON> KEY  (fk_sub_query) REFERENCES query (id)

);
