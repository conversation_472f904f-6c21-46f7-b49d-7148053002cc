-- ########################### Table Creation for EXECUTION LOG ###########################

CREATE SEQUENCE IF NOT EXISTS execution_log_sequence_id_seq START 1 INCREMENT 1;

CREATE TABLE IF NOT EXISTS execution_log (
    id             BIGINT     DEFAULT NEXTVAL('execution_log_sequence_id_seq')  PRIMARY KEY,
    fk_scan       BIGINT,
    total_results   INTEGER,
    status    VARCHAR(32),
    exception VARCHAR,
    mode        VARCHAR(32),
    duration_ms   BIGINT,
    created_by  <PERSON><PERSON><PERSON><PERSON>,
    created_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VA<PERSON><PERSON><PERSON>,
    updated_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,

   FOREIGN KEY (fk_scan) REFERENCES scan (id)
);
