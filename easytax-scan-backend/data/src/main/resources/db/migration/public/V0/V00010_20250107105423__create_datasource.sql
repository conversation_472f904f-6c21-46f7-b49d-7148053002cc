-- ########################### DATASOURCE ###########################
DO
$$
BEGIN
    CREATE SEQUENCE datasource_sequence_id_seq START 1 INCREMENT 1;
EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'sequence "datasource_sequence_id_seq" already exists. Ignoring...';
END;
$$;



DO
$$
BEGIN
    CREATE TABLE datasource (
      id          BIGINT DEFAULT nextval('datasource_sequence_id_seq') PRIMARY KEY,
      code        VARCHAR(64)  UNIQUE NOT NULL,
      status      VARCHAR(32)  NOT NULL,
      description VARCHAR(1024) NOT NULL,
      country_segregated BOOLEAN ,
      created_by  <PERSON><PERSON><PERSON><PERSON>(255),
      created_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_by <PERSON><PERSON><PERSON><PERSON> (255),
      updated_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'table "datasource" already exists. Ignoring...';
<PERSON><PERSON>;
$$;
