-- ########################### SCAN ###########################
DO
$$
BEGIN
    CREATE SEQUENCE IF NOT EXISTS scan_sequence_id_seq START 1 INCREMENT 1;

END;
$$;



DO
$$
BEGIN
    CREATE TABLE IF NOT EXISTS scan (
      id          BIGINT DEFAULT nextval('scan_sequence_id_seq') PRIMARY KEY,
      fk_query    BIGINT       NOT NULL,
      cron_expression VARCHAR  NOT NULL,
      code        VARCHAR(64)  UNIQUE NOT NULL,
      description VARCHAR      NOT NULL,
      status      VARCHAR(32)  NOT NULL,
      mode        VARCHAR(32)  NOT NULL,
      mapping     VARCHAR      NOT NULL,
      sid_column  VARCHAR      NOT NULL,
      fk_country BIGINT,
      created_by  VA<PERSON>HAR(255),
      created_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_by VARC<PERSON><PERSON> (255),
      updated_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (fk_query) REFERENCES query (id),
      FOREIGN KEY (fk_country) REFERENCES country(id)


    );

END;
$$;
