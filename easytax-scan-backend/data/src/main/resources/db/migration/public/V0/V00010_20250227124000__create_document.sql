CREATE SEQUENCE IF NOT EXISTS document_sequence_id_seq START 1 INCREMENT 1;

CREATE TABLE IF NOT EXISTS document (
    id            BIGINT     DEFAULT NEXTVAL('document_sequence_id_seq')  PRIMARY KEY,
    fk_execution_log       BIGINT,
    sid            VARCHAR(64),
    status  VARCHAR(32),
    mode  <PERSON><PERSON><PERSON><PERSON>(32),
    query_data   VARCHAR,
    request_payload VARCHAR,
    created_by  VARCHAR,
    created_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by <PERSON><PERSON><PERSON><PERSON>,
    updated_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,

   FOREIGN KEY (fk_execution_log) REFERENCES execution_log (id)
);
