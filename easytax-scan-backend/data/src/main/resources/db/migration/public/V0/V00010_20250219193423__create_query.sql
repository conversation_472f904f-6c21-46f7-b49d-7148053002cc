-- ########################### Table Creation for query###########################

CREATE SEQUENCE IF NOT EXISTS query_sequence_id_seq START 1 INCREMENT 1;

CREATE TABLE IF NOT EXISTS query (
    id             BIGINT     DEFAULT NEXTVAL('query_sequence_id_seq')  PRIMARY KEY,
    sql             VARCHAR,
    code           VARCHAR,
    description    VARCHAR,
    page_size         INTEGER      NOT NULL,
    pagination_field VARCHAR,
    fk_datasource         INTEGER      NOT NULL,
    sample_result   VARCHAR,
    created_by  <PERSON><PERSON><PERSON><PERSON>,
    created_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VA<PERSON><PERSON>R,
    updated_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,

   FOREIGN KEY (fk_datasource) REFERENCES datasource (id)
);
