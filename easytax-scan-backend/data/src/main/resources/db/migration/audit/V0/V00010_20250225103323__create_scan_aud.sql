-- ########################### SCAN AUD ###########################

DO
$$
BEGIN
    CREATE TABLE IF NOT EXISTS scan_aud (
      id          BIGINT ,
      fk_query    BIGINT       ,
      cron_expression VARCHAR  ,
      code        VA<PERSON><PERSON><PERSON>(64)  ,
      description VARCHAR      ,
      status      VARCHAR(32)  ,
      mode        VARC<PERSON><PERSON>(32)  ,
      mapping     VARCHAR      ,
      sid_column  VARCHAR      ,
      fk_country BIGINT ,
      created_by  <PERSON><PERSON><PERSON><PERSON>(255),
      created_at  TIMESTAMP   ,
      updated_by VA<PERSON><PERSON><PERSON> (255),
      updated_at  TIMESTAMP   ,
      rev          BIGINT,
      revtype      INTEGER,
      PRIMARY KEY (id, rev)
    );

END;
$$;
