DO
$$
BEGIN
    CREATE TABLE setting_aud
    (
        id           BIGINT,
        property     VARCHAR(255),
        type         VA<PERSON><PERSON><PERSON>(64),
        override_key VARCHAR(255),
        description  VARCHAR,
        value        VARCHAR,
        created_at   TIMESTAMP,
        created_by   <PERSON><PERSON><PERSON><PERSON>(255),
        updated_at   TIMESTAMP,
        updated_by   <PERSON><PERSON><PERSON><PERSON>(255),
        rev          BIGINT,
        revtype      INTEGER,
        PRIMARY KEY (id, rev)
    );
EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'table "setting_aud" already exists. Ignoring...';
END;
$$;

