-- ########################### EXECUTION LOG AUD ###########################


    CREATE TABLE IF NOT EXISTS execution_log_aud (
      id          BIGINT ,
      fk_scan       BIGINT,
      total_results   INTEGER,
      status    VARCHAR(32),
      exception VARCHAR,
      mode        <PERSON><PERSON><PERSON><PERSON>(32),
      duration_ms    BIGINT,
      created_by  VA<PERSON><PERSON><PERSON>,
      created_at  TIMESTAMP,
      updated_by VARCHAR,
      updated_at  TIMESTAMP,
      rev          BIGINT,
      revtype      INTEGER,
      PRIMARY KEY (id, rev)
    );

