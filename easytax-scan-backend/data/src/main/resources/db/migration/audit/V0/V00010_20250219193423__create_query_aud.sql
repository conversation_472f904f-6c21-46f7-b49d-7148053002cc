
  -- Create query_aud table with appropriate fields
  CREATE TABLE IF NOT EXISTS query_aud (
      id               BIGINT,
      sql              VARCHAR,
      code             VA<PERSON>HAR,
      description      VARCHAR,
      page_size         INTEGER     NOT NULL,
      pagination_field  VARCHAR,
      fk_datasource    INTEGER     NOT NULL,
      sample_result     VARCHAR,
      created_by       <PERSON><PERSON><PERSON><PERSON>,
      created_at       TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_by       VARCHAR,
      updated_at       TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,

      rev              BIGINT,
      revtype          INTEGER,

      PRIMARY KEY (id,rev)
  );

