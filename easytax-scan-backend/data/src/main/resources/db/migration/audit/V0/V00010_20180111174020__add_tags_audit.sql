DO
$$
BEGIN
    CREATE TABLE revision_info
    (
      ID        INTEGER,
      <PERSON><PERSON><PERSON><PERSON><PERSON> BIGINT,
      EMAIL     VARCHAR(255),
      time      TIMESTAMP NOT NULL,

      PRIMARY KEY (ID)
    );
EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'table "revision_info" already exists. Ignoring...';
END;
$$;


DO
$$
BEGIN
    CREATE TABLE tags_aud (
                              ID          INTEGER,
                              REV         INTEGER,
                              NAME        VARCHAR(50),
                              DESCRIPTION VARCHAR(255),
                              COLOR       VARCHAR(7),
                              CREATED_AT  TIMESTAMP,
                              REVTYP<PERSON>     INTEGER,
                              PRIMARY KEY (ID, REV)
    );
EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'table "tags_aud" already exists. Ignoring...';
END;
$$;
