
  -- Create sub_query_aud table with appropriate fields
  CREATE TABLE IF NOT EXISTS sub_query_aud (
        id             BIGINT,
        main_query_column   VARCHAR,
        sub_query_column    VARCHAR,
        status             VARCHAR,
        fk_main_query       INTEGER,
        fk_sub_query        INTEGER,
        is_list          BOOLEAN NOT NULL DEFAULT FALSE,
        created_by  VARCHAR,
        created_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_by VARCHA<PERSON>,
        updated_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
        rev              BIGINT,
        revtype          INTEGER,
        PRIMARY KEY (id, rev)
  );
