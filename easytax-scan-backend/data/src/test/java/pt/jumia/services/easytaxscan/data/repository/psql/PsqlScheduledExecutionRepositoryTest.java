package pt.jumia.services.easytaxscan.data.repository.psql;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.quartz.SchedulerException;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.ScheduledExecution;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScheduledExecutionFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScheduledExecutionSortFilters;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;


class PsqlScheduledExecutionRepositoryTest extends BaseRepositoryTest {


    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    void insertAndFind() throws SchedulerException {
        Scan insertedScan = insertFakeScan(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        Optional<Scan> optScan = scanRepository.findByCode(insertedScan.getCode());
        createJobsUseCase.createScanJob(optScan.get());
        Assertions.assertThat(optScan.isPresent()).isTrue();
        List<ScheduledExecution> scheduledJobList = scheduledExecutionsRepository.
                findAll(ScheduledExecutionFilters.builder().build(), ScheduledExecutionSortFilters.builder().build());
        Optional<ScheduledExecution> jobDetails = scheduledJobList.stream().filter(job -> job.getScanCode().equalsIgnoreCase(insertedScan.getCode())).findAny();
        assertThat(jobDetails).isPresent();
    }
}
