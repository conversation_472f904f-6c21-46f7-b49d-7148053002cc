package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import org.junit.jupiter.api.Test;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.data.repository.psql.BaseRepositoryTest;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.QueryAuditFilters;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class QueryAuditRepositoryTest extends BaseRepositoryTest {

    @Test
    public void insertAndFind_query_audits_success() {
        RequestContext.setUser(RequestUser.builder().username(TEST_USER).build());
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        Query insertQuery = insertQuery(queryCreated);
        QueryAuditFilters filters = QueryAuditFilters.builder().id(insertQuery.getId()).build();
        List<AuditedEntity<Query>> queryAuditById = queryAuditRepository.getQueryAuditLogById(filters, insertQuery);
        assertEquals(1, queryAuditById.size());
    }

    @Test
    public void insertAndFind_query_audits_count_success() {
        RequestContext.setUser(RequestUser.builder().username(TEST_USER).build());
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        Query insertQuery = insertQuery(queryCreated);
        QueryAuditFilters filters = QueryAuditFilters.builder().id(insertQuery.getId()).build();
        long auditQueryCount = queryAuditRepository.getAuditLogCountById(filters);
        assertEquals(1, auditQueryCount);
    }
}
