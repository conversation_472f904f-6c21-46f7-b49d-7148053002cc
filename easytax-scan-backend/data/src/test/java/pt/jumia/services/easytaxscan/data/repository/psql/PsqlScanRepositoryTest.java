package pt.jumia.services.easytaxscan.data.repository.psql;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class PsqlScanRepositoryTest extends BaseRepositoryTest {

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");

    }

    @Test
    public void insert() {
        Scan insertedScan = insertFakeScan(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        Optional<Scan> optScan = scanRepository.findByCode(insertedScan.getCode());
        Assertions.assertThat(optScan.isPresent()).isTrue();
        Assertions.assertThat(optScan.get()).isNotNull();
    }

    @Test
    public void update() {
        Scan createdScan = insertFakeScan(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        createdScan.setStatus(Scan.Status.INACTIVE);
        createdScan.setDescription("Updating ....");
        scanRepository.update(createdScan.getId(), createdScan);
        Scan scan = scanRepository.findById(createdScan.getId());
        assertEquals(scan.getDescription(), createdScan.getDescription());
        assertEquals(Scan.Status.INACTIVE, scan.getStatus());
    }

    @Test
    public void findByCode() {
        Scan createdScan = insertFakeScan(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        Optional<Scan> scanByCode = scanRepository.findByCode(createdScan.getCode());
        Assertions.assertThat(scanByCode.isPresent()).isTrue();
        assertEquals(scanByCode.get().getDescription(), createdScan.getDescription());
        assertEquals(scanByCode.get().getCode(), createdScan.getCode());
    }

    @Test
    public void findById() {
        Scan createdScan = insertFakeScan(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        Scan scanById = scanRepository.findById(createdScan.getId());
        assertEquals(scanById.getDescription(), createdScan.getDescription());
        assertEquals(scanById.getCode(), createdScan.getCode());
    }

    @Test
    public void deleteById() {
        Scan createdScan = insertFakeScan(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        scanRepository.deleteById(createdScan.getId());
        assertThrows(NotFoundException.class, () ->
                scanRepository.findById(createdScan.getId())
        );
    }

    @Test
    public void findAll_withOutFilters() {
        populateFindAllFilterData();
        ScanFilters filters = ScanFilters.builder().build();
        ScanSortFilters sortFilters = ScanSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Scan> scanList = scanRepository.findAll(filters, sortFilters, pageFilters);
        long count = scanRepository.executeCount(filters);
        assertThat(scanList).isNotEmpty();
        assertThat(count).isEqualTo(3);
        assertThat(scanList.size()).isEqualTo(3);
        assertThat(scanList.getFirst()).extracting(Scan::getCode)
                .isEqualTo(FakeScan.SCAN_FILTER_DATA2.getCode());
        assertThat(scanList.getLast()).extracting(Scan::getCode)
                .isEqualTo(FakeScan.SCAN_FILTER_DATA4.getCode());
    }

    private void populateFindAllFilterData() {
        insertFakeScan(FakeScan.SCAN_FILTER_DATA2, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        insertFakeScan(FakeScan.SCAN_FILTER_DATA3, FakeQuery.QUERY_FILTER_DATA1, FakeDataSource.DATA_SOURCE_FILTER_DATA3);
        insertFakeScan(FakeScan.SCAN_FILTER_DATA4, FakeQuery.QUERY_FILTER_DATA2, FakeDataSource.DATA_SOURCE_FILTER_DATA4);

    }


    @Test
    public void findAll_withFilters() {
        populateFindAllFilterData();
        ScanFilters filters = ScanFilters.builder()
                .text(FakeScan.SCAN_FILTER_DATA3.getCode())
                .status(Scan.Status.INACTIVE).build();
        ScanSortFilters sortFilters = ScanSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Scan> ScanList = scanRepository.findAll(filters, sortFilters, pageFilters);
        long count = scanRepository.executeCount(filters);
        assertThat(ScanList).isNotEmpty();
        assertThat(count).isEqualTo(1);
        assertThat(ScanList.size()).isEqualTo(1);
        assertThat(ScanList.getFirst()).extracting(Scan::getCode)
                .isEqualTo(FakeScan.SCAN_FILTER_DATA3.getCode());
    }


}
