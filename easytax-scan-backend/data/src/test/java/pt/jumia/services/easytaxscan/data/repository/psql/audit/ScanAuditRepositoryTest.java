package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import org.junit.jupiter.api.Test;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.data.repository.psql.BaseRepositoryTest;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.ScanAuditFilters;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ScanAuditRepositoryTest extends BaseRepositoryTest {

    @Test
    public void insertAndFind_scan_audits_success() {
        RequestContext.setUser(RequestUser.builder().username(TEST_USER).build());
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        Query insertQuery = insertQuery(queryCreated);
        Scan scanCreated = FakeScan.SCAN_CREATE;
        scanCreated.setQuery(insertQuery);
        Scan insertScan = insertScan(scanCreated);
        ScanAuditFilters filters = ScanAuditFilters.builder().id(insertScan.getId()).build();
        List<AuditedEntity<Scan>> scanAuditLogs = scanAuditRepository.getScanAuditLogById(filters, insertScan);

        assertEquals(1, scanAuditLogs.size());
    }

    @Test
    public void insertAndFind_scan_audits_count_success() {
        RequestContext.setUser(RequestUser.builder().username(TEST_USER).build());
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        Query insertQuery = insertQuery(queryCreated);
        Scan scanCreated = FakeScan.SCAN_CREATE;
        scanCreated.setQuery(insertQuery);
        Scan insertScan = insertScan(scanCreated);
        ScanAuditFilters filters = ScanAuditFilters.builder().id(insertScan.getId()).build();
        long scanAuditCount = scanAuditRepository.getAuditLogCountById(filters);

        assertEquals(1, scanAuditCount);
    }
}
