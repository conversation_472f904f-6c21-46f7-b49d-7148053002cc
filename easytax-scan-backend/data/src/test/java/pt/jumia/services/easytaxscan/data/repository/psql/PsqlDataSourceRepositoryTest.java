package pt.jumia.services.easytaxscan.data.repository.psql;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceSortFilters;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PsqlDataSourceRepositoryTest extends BaseRepositoryTest {

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }


    @Test
    public void insert() {
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Optional<DataSource> OptDataSource = dataSourceRepository.findByCode(dataSource.getCode());
        assertThat(OptDataSource.get()).isNotNull();
    }

    @Test
    public void update() {
        DataSource createdDataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        DataSource toUpdate = FakeDataSource.DATA_SOURCE_UPDATE;

        dataSourceRepository.update(createdDataSource.getId(), toUpdate);
        Optional<DataSource> optionalDataSource = dataSourceRepository.findById(createdDataSource.getId());
        assertEquals(optionalDataSource.get().getDescription(), toUpdate.getDescription());
    }

    @Test
    public void findByCode() {
        DataSource createdDataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Optional<DataSource> dataSourceByCode = dataSourceRepository.findByCode(createdDataSource.getCode());
        assertEquals(dataSourceByCode.get().getDescription(), createdDataSource.getDescription());
        assertEquals(dataSourceByCode.get().getCode(), createdDataSource.getCode());
    }

    @Test
    public void findById() {
        DataSource createdDataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Optional<DataSource> dataSourceById = dataSourceRepository.findById(createdDataSource.getId());
        assertEquals(dataSourceById.get().getDescription(), createdDataSource.getDescription());
        assertEquals(dataSourceById.get().getCode(), createdDataSource.getCode());
    }

    @Test
    public void deleteById() {
        DataSource createdDataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        dataSourceRepository.deleteById(createdDataSource.getId());
        Optional<DataSource> dataSourceById = dataSourceRepository.findById(createdDataSource.getId());
        assertTrue(dataSourceById.isEmpty(), "Optional should be empty");
    }

    @Test
    public void findAll_withOutFilters() {
        populateFindAllFilterData();
        DataSourceFilters filters = DataSourceFilters.builder().build();
        DataSourceSortFilters sortFilters = DataSourceSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<DataSource> dataSourceList = dataSourceRepository.findAll(filters, sortFilters, pageFilters);
        long count = dataSourceRepository.executeCount(filters);
        assertThat(dataSourceList).isNotEmpty();
        assertThat(count).isEqualTo(3);
        assertThat(dataSourceList.size()).isEqualTo(3);
        assertThat(dataSourceList.getFirst()).extracting(DataSource::getCode)
                .isEqualTo(FakeDataSource.DATA_SOURCE_FILTER_DATA2.getCode());
        assertThat(dataSourceList.getLast()).extracting(DataSource::getCode)
                .isEqualTo(FakeDataSource.DATA_SOURCE_FILTER_DATA4.getCode());
    }

    private void populateFindAllFilterData() {
        List<DataSource> dataSources = List.of(FakeDataSource.DATA_SOURCE_FILTER_DATA2,
                FakeDataSource.DATA_SOURCE_FILTER_DATA3, FakeDataSource.DATA_SOURCE_FILTER_DATA4
        );
        dataSources.forEach(this::insertDataSource);
    }


    @Test
    public void findAll_withFilters() {
        populateFindAllFilterData();
        DataSourceFilters filters = DataSourceFilters.builder()
                .text(FakeDataSource.DATA_SOURCE_FILTER_DATA3.getCode())
                .status(DataSource.Status.INACTIVE).build();
        DataSourceSortFilters sortFilters = DataSourceSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<DataSource> dataSourceList = dataSourceRepository.findAll(filters, sortFilters, pageFilters);
        long count = dataSourceRepository.executeCount(filters);
        assertThat(dataSourceList).isNotEmpty();
        assertThat(count).isEqualTo(1);
        assertThat(dataSourceList.size()).isEqualTo(1);
        assertThat(dataSourceList.getFirst()).extracting(DataSource::getCode)
                .isEqualTo(FakeDataSource.DATA_SOURCE_FILTER_DATA3.getCode());
    }

}
