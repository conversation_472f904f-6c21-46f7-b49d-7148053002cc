package pt.jumia.services.easytaxscan.data.repository.psql;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.*;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogSortFilters;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PsqlExecutionLogRepositoryTest extends BaseRepositoryTest {

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void insertAndFindById() {
        ExecutionLog executionLogCreate = insertExecutionLogData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE);
        ExecutionLog insertExecutionLog = insertExecutionLog(executionLogCreate);
        Optional<ExecutionLog> executionLog = executionLogRepository.findById(insertExecutionLog.getId());
        assertThat(executionLog).isPresent();
    }

    @Test
    public void deleteById() {
        ExecutionLog executionLogCreate = insertExecutionLogData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE);
        executionLogRepository.deleteById(executionLogCreate.getId());
        Optional<ExecutionLog> executionLog = executionLogRepository.findById(executionLogCreate.getId());
        assertTrue(executionLog.isEmpty(), "execution log should be deleted and Optional should be empty");
    }

    @Test
    public void findAll_withFilters() {
        insertExecutionLogData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE);
        insertExecutionLogData(FakeScan.SCAN_UPDATE, FakeQuery.QUERY_UPDATE, FakeDataSource.DATA_SOURCE_UPDATE,
                FakeExecutionLog.EXECUTION_LOG_UPDATE);
        ExecutionLogFilters filters = ExecutionLogFilters.builder()
                .status(ExecutionLog.Status.ACTIVE)
                .build();
        ExecutionLogSortFilters sortFilters = ExecutionLogSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<ExecutionLog> executionLogList = executionLogRepository.findAll(filters, sortFilters, pageFilters);
        long count = executionLogRepository.executeCount(filters);
        assertThat(executionLogList).isNotEmpty();
        assertEquals(2, executionLogList.size());
        assertEquals(count, executionLogList.size());

    }

    private ExecutionLog insertExecutionLogData(Scan scan, Query query, DataSource dataSource, ExecutionLog executionLog) {
        DataSource createdDataSource = insertDataSource(dataSource);
        query.setDataSource(createdDataSource);
        Query insertQuery = insertQuery(query);
        scan.setQuery(insertQuery);
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> insertCountry(FakeCountry.COUNTRY_EG));
        scan.setCountry(country);
        Scan scanInserted = insertScan(scan);
        executionLog.setScan(scanInserted);
        return insertExecutionLog(executionLog);
    }

}
