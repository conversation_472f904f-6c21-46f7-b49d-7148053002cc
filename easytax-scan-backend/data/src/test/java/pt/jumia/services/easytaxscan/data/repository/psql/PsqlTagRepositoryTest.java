package pt.jumia.services.easytaxscan.data.repository.psql;

import org.junit.jupiter.api.Test;
import pt.jumia.services.easytaxscan.domain.entities.Tag;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlTagRepositoryTest extends BaseRepositoryTest {

    private static final Tag A_TAG = Tag.builder()
            .name("A tag")
            .description("This is just for testing purposes")
            .color("#FFFFFF")
            .build();
    private static final Tag ANOTHER_TAG = Tag.builder()
            .name("Another tag")
            .description("This is another tag just for testing purposes")
            .color("#AFAFAF")
            .build();

    @Test
    public void insertAndFind() {
        Tag aTag = insertTag(A_TAG);
        Tag anotherTag = insertTag(ANOTHER_TAG);

        List<Tag> tags = tagRepository.findAll();

        assertThat(tags).containsExactlyInAnyOrder(aTag, anotherTag);
    }

    @Test
    public void findById() {
        Tag insertedTag = insertTag(A_TAG);

        Optional<Tag> optionalTag = tagRepository.findById(insertedTag.getId());

        assertThat(optionalTag).contains(insertedTag);
    }

    @Test
    public void update() {
        Tag insertedTag = insertTag(A_TAG);

        Tag toUpdate = insertedTag.toBuilder()
                .name("updated name")
                .description("updated description")
                .color("#BBB")
                .build();
        Tag updatedTag = tagRepository.update(toUpdate);
        Optional<Tag> optionalTag = tagRepository.findById(insertedTag.getId());

        assertThat(optionalTag).contains(updatedTag);
        assertEquals(toUpdate.withoutDbFields(), updatedTag.withoutDbFields());
    }

    @Test
    public void delete() {
        Tag insertedTag = tagRepository.insert(A_TAG);
        assertThat(tagRepository.findById(insertedTag.getId())).contains(insertedTag);

        tagRepository.deleteById(insertedTag.getId());
        assertThat(tagRepository.findById(insertedTag.getId())).isEmpty();
    }
}
