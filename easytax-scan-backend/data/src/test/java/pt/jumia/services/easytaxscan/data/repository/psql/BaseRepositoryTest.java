package pt.jumia.services.easytaxscan.data.repository.psql;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.domain.jobs.CreateJobsUseCase;
import pt.jumia.services.easytaxscan.domain.repository.*;
import pt.jumia.services.easytaxscan.domain.repository.audit.*;

import java.util.Stack;

@ExtendWith(SpringExtension.class)
@SpringBootTest
public class BaseRepositoryTest {

    public static final String TEST_USER = "Test_User";

    private final Stack<Long> tagsToDelete = new Stack<>();
    private final Stack<Long> dataSourceToDelete = new Stack<>();
    private final Stack<Long> queryToDelete = new Stack<>();
    private final Stack<Long> subQueryToDelete = new Stack<>();
    private final Stack<Long> scanToDelete = new Stack<>();
    private final Stack<Long> webHooksToDelete = new Stack<>();

    private final Stack<Long> executionToDelete = new Stack<>();
    private final Stack<Long> documentToDelete = new Stack<>();
    private final Stack<Long> countryToDelete = new Stack<>();

    @Autowired
    protected TagRepository tagRepository;
    @Autowired
    protected DataSourceRepository dataSourceRepository;
    @Autowired
    protected QueryRepository queryRepository;
    @Autowired
    protected WebhooksRepository webhooksRepository;
    @Autowired
    protected SubQueryRepository subQueryRepository;
    @Autowired
    protected ScanRepository scanRepository;

    @Autowired
    protected ExecutionLogRepository executionLogRepository;

    @Autowired
    protected DocumentRepository documentRepository;

    @Autowired
    protected CountryRepository countryRepository;

    @Autowired
    protected DataSourceAuditRepository dataSourceAuditRepository;

    @Autowired
    protected QueryAuditRepository queryAuditRepository;

    @Autowired
    protected ScanAuditRepository scanAuditRepository;

    @Autowired
    protected ScheduledExecutionsRepository scheduledExecutionsRepository;

    @Autowired
    protected CreateJobsUseCase createJobsUseCase;

    @Autowired
    protected ExecutionLogAuditRepository executionLogAuditRepository;

    @Autowired
    protected DocumentAuditRepository documentAuditRepository;

    @Autowired
    protected WebhookAuditRepository webhookAuditRepository;

    @AfterEach
    public void baseTearDown() {
        while (!tagsToDelete.empty()) {
            tagRepository.deleteById(tagsToDelete.pop());
        }
        while (!webHooksToDelete.empty()) {
            webhooksRepository.deleteById(webHooksToDelete.pop());
        }
        while (!documentToDelete.empty()) {
            documentRepository.deleteById(documentToDelete.pop());
        }
        while (!executionToDelete.empty()) {
            executionLogRepository.deleteById(executionToDelete.pop());
        }

        while (!scanToDelete.empty()) {
            scanRepository.deleteById(scanToDelete.pop());
        }
        while (!queryToDelete.empty()) {
            Long QueryId = queryToDelete.pop();
            queryRepository.deleteById(QueryId);
            subQueryRepository.deleteSubQueryByQueryId(QueryId);
        }
        while (!dataSourceToDelete.empty()) {
            dataSourceRepository.deleteById(dataSourceToDelete.pop());
        }
        while (!countryToDelete.empty()) {
            countryRepository.deleteById(countryToDelete.pop());
        }
    }

    protected void loginUser(String username) {
        RequestContext.setUser(RequestUser.builder().username(username).build());
    }

    protected Tag insertTag(Tag tag) {
        Tag createdTag = tagRepository.insert(tag);
        tagsToDelete.push(createdTag.getId());
        return createdTag;
    }

    protected DataSource insertDataSource(DataSource dataSource) {
        DataSource createdDataSource = dataSourceRepository.insert(dataSource);
        dataSourceToDelete.push(createdDataSource.getId());
        return createdDataSource;
    }

    protected Scan insertFakeScan(Scan scan, Query query, DataSource dataSource) {
        DataSource createdDataSource = insertDataSource(dataSource);
        query.setDataSource(createdDataSource);
        Query insertQuery = insertQuery(query);
        scan.setQuery(insertQuery);
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> insertCountry(FakeCountry.COUNTRY_EG));
        scan.setCountry(country);
        return insertScan(scan);
    }

    protected Scan insertScan(Scan scan) {
        Scan createdScan = scanRepository.insert(scan);
        scanToDelete.push(createdScan.getId());
        return createdScan;
    }

    protected Query insertQuery(Query query) {
        Query queryInsert = queryRepository.insert(query);
        queryToDelete.push(queryInsert.getId());
        return queryInsert;
    }

    protected WebHooks insertWebhooks(WebHooks webHooks) {
        WebHooks webHooksInsert = webhooksRepository.insert(webHooks);
        webHooksToDelete.push(webHooksInsert.getId());
        return webHooksInsert;
    }

    protected ExecutionLog insertExecutionLog(ExecutionLog executionLog) {
        ExecutionLog executionInserted = executionLogRepository.insert(executionLog);
        executionToDelete.push(executionInserted.getId());
        return executionInserted;
    }

    protected Document insertDocument(Document document) {
        Document createdDocument = documentRepository.insert(document);
        documentToDelete.push((createdDocument.getId()));
        return createdDocument;
    }

    protected Country insertCountry(Country country) {
        Country createdCountry = countryRepository.insert(country);
        countryToDelete.push((createdCountry.getId()));
        return createdCountry;
    }

}
