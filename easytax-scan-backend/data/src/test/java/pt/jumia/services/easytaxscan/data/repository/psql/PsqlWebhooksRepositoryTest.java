package pt.jumia.services.easytaxscan.data.repository.psql;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.*;
import pt.jumia.services.easytaxscan.domain.entities.filter.WebhooksSortFilters;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlWebhooksRepositoryTest extends BaseRepositoryTest {

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void insert_success() {
        Document documentCreate = insertWebhooksData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        WebHooks webHooks = FakeWebhook.WEBHOOK;
        webHooks.setDocument(documentCreate);
        WebHooks webHooks1 = insertWebhooks(webHooks);
        assertThat(webHooks1.getPayload()).isEqualTo(webHooks.getPayload());

    }

    @Test
    public void findById_success() {
        Document documentCreate = insertWebhooksData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        WebHooks webHooks = FakeWebhook.WEBHOOK;
        webHooks.setDocument(documentCreate);
        WebHooks webHooks1 = insertWebhooks(webHooks);
        Optional<WebHooks> insertedWebhooks = webhooksRepository.findById(webHooks1.getId());
        assertThat(insertedWebhooks).isPresent();
    }

    @Test
    public void findById_failure() {
        Optional<WebHooks> insertedWebhooks = webhooksRepository.findById(2L);
        assertThat(insertedWebhooks).isEmpty();
    }

    @Test
    public void findAll_success() {
        Document documentCreate = insertWebhooksData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        WebHooks webHooks = FakeWebhook.WEBHOOK;
        webHooks.setDocument(documentCreate);
        WebHooks webHooks1 = insertWebhooks(webHooks);
        List<WebHooks> insertedWebhooks = webhooksRepository
                .findAll(WebhooksSortFilters.builder().build(), PageFilters.builder().build());
        assertThat(insertedWebhooks).isNotEmpty();
        assertEquals(1, insertedWebhooks.size());
    }

    private Document insertWebhooksData(Scan scan, Query query, DataSource dataSource, ExecutionLog executionLog, Document document) {
        DataSource createdDataSource = insertDataSource(dataSource);
        query.setDataSource(createdDataSource);
        Query insertedQuery = insertQuery(query);
        scan.setQuery(insertedQuery);
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> insertCountry(FakeCountry.COUNTRY_EG));
        scan.setCountry(country);
        Scan scanInserted = insertScan(scan);
        executionLog.setScan(scanInserted);
        ExecutionLog insertedExecutionLog = insertExecutionLog(executionLog);
        document.setExecutionLog(insertedExecutionLog);
        return insertDocument(document);
    }
}
