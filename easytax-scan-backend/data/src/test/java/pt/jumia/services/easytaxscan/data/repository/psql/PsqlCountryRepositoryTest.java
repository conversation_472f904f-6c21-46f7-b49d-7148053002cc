package pt.jumia.services.easytaxscan.data.repository.psql;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.CountryRepository;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

public class PsqlCountryRepositoryTest extends BaseRepositoryTest {

    @Autowired
    private CountryRepository countryRepository;

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void insert() {
        Country createdCountry = insertCountry(FakeCountry.COUNTRY_IND);
        Optional<Country> optCountry = countryRepository.findByCode(createdCountry.getCountryCode());

        assertThat(optCountry).isPresent();
        assertEquals(createdCountry.getCountryCode(), optCountry.get().getCountryCode());
    }

    @Test
    public void findById() {
        Country createdCountry = insertCountry(FakeCountry.COUNTRY_CREATE);
        Optional<Country> countryById = countryRepository.findById(createdCountry.getId());

        assertThat(countryById).isPresent();
        assertEquals(createdCountry.getCountryCode(), countryById.get().getCountryCode());
        assertEquals(createdCountry.getCountryName(), countryById.get().getCountryName());
    }

    @Test
    public void findByCode() {
        Country createdCountry = insertCountry(FakeCountry.COUNTRY_EG);
        Optional<Country> countryByCode = countryRepository.findByCode(createdCountry.getCountryCode());

        assertThat(countryByCode).isPresent();
        assertEquals(createdCountry.getCountryCode(), countryByCode.get().getCountryCode());
        assertEquals(createdCountry.getCountryName(), countryByCode.get().getCountryName());
    }

    @Test
    public void update() {
        Country createdCountry = insertCountry(FakeCountry.COUNTRY_CREATE);
        Country toUpdate = FakeCountry.COUNTRY_UPDATE;

        Country updatedCountry = countryRepository.update(createdCountry.getId(), toUpdate);
        Optional<Country> countryById = countryRepository.findById(createdCountry.getId());

        assertThat(countryById).isPresent();
        assertEquals(toUpdate.getCountryName(), updatedCountry.getCountryName());
    }

    @Test
    public void deleteById() {
        Country createdCountry = insertCountry(FakeCountry.COUNTRY_CREATE);
        countryRepository.deleteById(createdCountry.getId());
        Optional<Country> countryById = countryRepository.findById(createdCountry.getId());

        assertTrue(countryById.isEmpty(), "Country should be deleted");
    }

    @Test
    public void update_nonExistingCountry_shouldThrowException() {
        Country toUpdate = FakeCountry.COUNTRY_UPDATE;
        long nonExistingId = 9999L;

        NotFoundException thrown = assertThrows(NotFoundException.class, () ->
                countryRepository.update(nonExistingId, toUpdate)
        );

        String expectedMessage = String.format("Country with id %d not found", nonExistingId);
        String expectedMessageWithComma = String.format("Country with id %,d not found", nonExistingId);

        assertThat(thrown.getMessage()).isIn(expectedMessage, expectedMessageWithComma);
    }

    @Test
    public void findById_nonExistingCountry_shouldReturnEmpty() {
        Optional<Country> countryById = countryRepository.findById(9999L);
        assertTrue(countryById.isEmpty(), "Optional should be empty for non-existing ID");
    }
}
