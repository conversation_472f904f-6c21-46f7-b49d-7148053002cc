package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import org.junit.jupiter.api.Test;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.data.repository.psql.BaseRepositoryTest;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.DataSourceAuditFilters;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class DataSourceAuditRepositoryTest extends BaseRepositoryTest {

    @Test
    public void insertAndFind_dataSource_audits_success() {
        RequestContext.setUser(RequestUser.builder().username(TEST_USER).build());
        DataSource insertedDataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        DataSourceAuditFilters filters = DataSourceAuditFilters.builder()
                .id(insertedDataSource.getId())
                .build();
        List<AuditedEntity<DataSource>> dataSourceAuditLogs =
                dataSourceAuditRepository.getDataSourceAuditLogById(filters, insertedDataSource);
        assertEquals(1, dataSourceAuditLogs.size());
    }

    @Test
    public void insertAndFind_dataSource_audit_count_success() {
        RequestContext.setUser(RequestUser.builder().username(TEST_USER).build());
        DataSource insertedDataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        DataSourceAuditFilters filters = DataSourceAuditFilters.builder()
                .id(insertedDataSource.getId())
                .build();
        long auditLogCount = dataSourceAuditRepository.getAuditLogCountById(filters);
        assertEquals(1, auditLogCount);
    }
}
