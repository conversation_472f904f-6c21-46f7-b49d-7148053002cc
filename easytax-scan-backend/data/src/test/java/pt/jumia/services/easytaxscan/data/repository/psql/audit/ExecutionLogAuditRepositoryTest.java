package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import org.junit.jupiter.api.Test;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.data.repository.psql.BaseRepositoryTest;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.ExecutionLogAuditFilters;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class ExecutionLogAuditRepositoryTest extends BaseRepositoryTest {

    @Test
    public void insertAndFind_executionLog_audits_success() {
        RequestContext.setUser(RequestUser.builder().username(TEST_USER).build());
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        Query insertQuery = insertQuery(queryCreated);
        Scan scanCreated = FakeScan.SCAN_CREATE;
        scanCreated.setQuery(insertQuery);
        Scan insertScan = insertScan(scanCreated);
        ExecutionLog executionLogCreated = FakeExecutionLog.EXECUTION_LOG_CREATE;
        executionLogCreated.setScan(insertScan);
        ExecutionLog insertExecutionLog = insertExecutionLog(executionLogCreated);
        ExecutionLogAuditFilters filters = ExecutionLogAuditFilters.builder()
                .id(insertExecutionLog.getId())
                .build();
        List<AuditedEntity<ExecutionLog>> executionLogAuditLogs =
                executionLogAuditRepository.getExecutionLogAuditLogById(filters, insertExecutionLog);
        assertEquals(1, executionLogAuditLogs.size());
    }

    @Test
    public void insertAndFind_executionLog_audits_count_success() {
        RequestContext.setUser(RequestUser.builder().username(TEST_USER).build());
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        Query insertQuery = insertQuery(queryCreated);
        Scan scanCreated = FakeScan.SCAN_CREATE;
        scanCreated.setQuery(insertQuery);
        Scan insertScan = insertScan(scanCreated);
        ExecutionLog executionLogCreated = FakeExecutionLog.EXECUTION_LOG_CREATE;
        executionLogCreated.setScan(insertScan);
        ExecutionLog insertExecutionLog = insertExecutionLog(executionLogCreated);
        ExecutionLogAuditFilters filters = ExecutionLogAuditFilters.builder()
                .id(insertExecutionLog.getId())
                .build();
        long executionLogAuditCount = executionLogAuditRepository.getAuditLogCountById(filters);
        assertEquals(1, executionLogAuditCount);
    }
}
