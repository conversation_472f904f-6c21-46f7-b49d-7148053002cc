package pt.jumia.services.easytaxscan.data.repository.psql;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeSubQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.QueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.QuerySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;


public class PsqlQueryRepositoryTest extends BaseRepositoryTest {

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void insert() {
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        Query insertQuery = insertQuery(queryCreated);
        Optional<Query> optQuery = queryRepository.findByCode(insertQuery.getCode());
        assertThat(optQuery).isPresent();
    }

    @Test
    public void update() {
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        Query createdQuery = insertQuery(queryCreated);
        createdQuery.setDescription("newly updated");
        queryRepository.update(createdQuery.getId(), createdQuery);
        Optional<Query> optionalQuery = queryRepository.findById(createdQuery.getId());
        assertEquals(optionalQuery.get().getDescription(), "newly updated");
    }

    @Test
    public void findByCode() {
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        Query createdQuery = insertQuery(queryCreated);
        Optional<Query> queryByCode = queryRepository.findByCode(createdQuery.getCode());
        assertEquals(queryByCode.get().getDescription(), createdQuery.getDescription());
        assertEquals(queryByCode.get().getCode(), createdQuery.getCode());
    }

    @Test
    public void findById() {
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        Query createdQuery = insertQuery(queryCreated);
        Optional<Query> queryById = queryRepository.findById(createdQuery.getId());
        assertEquals(queryById.get().getDescription(), createdQuery.getDescription());
        assertEquals(queryById.get().getCode(), createdQuery.getCode());
    }

    @Test
    public void deleteById() {
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        Query createdQuery = insertQuery(queryCreated);
        queryRepository.deleteById(createdQuery.getId());
        Optional<Query> queryById = queryRepository.findById(createdQuery.getId());
        assertTrue(queryById.isEmpty(), "Query should be deleted and Optional should be empty");
    }

    @Test
    public void findAll_withFilters() {
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        insertQuery(queryCreated);
        QueryFilters filters = QueryFilters.builder()
                .text(FakeQuery.QUERY_CREATE.getDescription())
                .build();
        QuerySortFilters sortFilters = QuerySortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Query> queryList = queryRepository.findAll(filters, sortFilters, pageFilters);
        long count = queryRepository.executeCount(filters);
        assertThat(queryList).isNotEmpty();
        assertThat(count).isEqualTo(1);
        assertThat(queryList.size()).isEqualTo(1);
    }

    @Test
    public void executeSubQueryCount() {
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        SubQuery subQuery = FakeSubQuery.SUB_QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        queryCreated.setSubQueries(List.of(subQuery));
        Query query = insertQuery(queryCreated);
        subQuery.setId(query.getId());
        subQueryRepository.insert(query.getId(), subQuery);
        SubQueryFilters filters = SubQueryFilters.builder().build();
        Long count = subQueryRepository.executeCount(query.getId(), filters);
        subQueryRepository.deleteSubQueryByQueryId(query.getId());
        assertThat(count).isGreaterThan(0);
    }

    @Test
    public void findAllSubQueries() {
        DataSource dataSource = insertDataSource(FakeDataSource.DATA_SOURCE_CREATE);
        Query queryCreated = FakeQuery.QUERY_CREATE;
        SubQuery subQuery = FakeSubQuery.SUB_QUERY_CREATE;
        queryCreated.setDataSource(dataSource);
        queryCreated.setSubQueries(List.of(subQuery));
        Query query = insertQuery(queryCreated);
        subQuery.setId(query.getId());
        subQueryRepository.insert(query.getId(), subQuery);
        SubQueryFilters filters = SubQueryFilters.builder().build();
        SubQuerySortFilters sortFilters = SubQuerySortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<SubQuery> subQueryList = subQueryRepository.findAll(query.getId(), filters, sortFilters, pageFilters);
        subQueryRepository.deleteSubQueryByQueryId(query.getId());
        assertThat(subQueryList).isNotEmpty();
    }


}
