package pt.jumia.services.easytaxscan.data.repository.psql;

import jakarta.transaction.Transactional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.*;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentSortFilters;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class PsqlDocumentRepositoryTest extends BaseRepositoryTest {

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void insertAndCount() {
        Document documentCreate = insertDocumentData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        insertDocument(documentCreate);

        DocumentFilters filters = DocumentFilters.builder()
                .sid(documentCreate.getSid())
                .build();
        long count = documentRepository.executeCount(filters);

        assertEquals(1, count, "Document should be inserted and count should be 1");
    }

    @Test
    public void deleteByIdAndCount() {
        Document documentCreate = insertDocumentData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        insertDocument(documentCreate);

        documentRepository.deleteById(documentCreate.getId());

        DocumentFilters filters = DocumentFilters.builder()
                .sid(documentCreate.getSid())
                .build();
        long count = documentRepository.executeCount(filters);

        assertEquals(0, count, "Document should be deleted and count should be 0");
    }

    @Test
    public void findAll_withFilters() {
        insertDocumentData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE,
                FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        insertDocumentData(FakeScan.SCAN_FILTER_DATA2, FakeQuery.QUERY_UPDATE, FakeDataSource.DATA_SOURCE_UPDATE,
                FakeExecutionLog.EXECUTION_LOG_UPDATE, FakeDocument.DOCUMENT_UPDATE);
        DocumentFilters filters = DocumentFilters.builder()
                .statusList(List.of(Document.Status.CREATED.name()))
                .build();
        DocumentSortFilters sortFilters = DocumentSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<Document> documentList = documentRepository.findAll(filters, sortFilters, pageFilters);
        long count = documentRepository.executeCount(filters);
        assertEquals(2, documentList.size());
        assertEquals(count, documentList.size());
    }

    @Test
    public void executeCount_fails_whenNoDocumentsExist() {
        DocumentFilters filters = DocumentFilters.builder().build();
        long count = documentRepository.executeCount(filters);

        assertEquals(0, count, "Document count should be 0 when no documents exist");
    }

    private Document insertDocumentData(Scan scan, Query query, DataSource dataSource, ExecutionLog executionLog, Document document) {
        DataSource createdDataSource = insertDataSource(dataSource);
        query.setDataSource(createdDataSource);
        Query insertedQuery = insertQuery(query);
        scan.setQuery(insertedQuery);
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> insertCountry(FakeCountry.COUNTRY_EG));
        scan.setCountry(country);
        Scan scanInserted = insertScan(scan);
        executionLog.setScan(scanInserted);
        ExecutionLog insertedExecutionLog = insertExecutionLog(executionLog);
        document.setExecutionLog(insertedExecutionLog);
        return insertDocument(document);
    }
}
