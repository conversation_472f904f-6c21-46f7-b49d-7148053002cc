package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import org.junit.jupiter.api.Test;
import pt.jumia.services.easytaxscan.data.repository.psql.BaseRepositoryTest;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.*;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.WebhookAuditFilters;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class WebhooksAuditRepositoryTest extends BaseRepositoryTest {

    @Test
    public void insertAndFind_webhooks_audits_success() {
        Document documentCreate = insertWebhooksData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        WebHooks webHooks = FakeWebhook.WEBHOOK;
        webHooks.setDocument(documentCreate);
        WebHooks insertWebhooks = insertWebhooks(webHooks);
        WebhookAuditFilters filters = WebhookAuditFilters.builder().id(insertWebhooks.getId()).build();
        List<AuditedEntity<WebHooks>> documentAuditById = webhookAuditRepository.getWebhookAuditLogById(filters, insertWebhooks);
        assertEquals(1, documentAuditById.size());
    }

    @Test
    public void insertAndFind_document_audits_count_success() {
        Document documentCreate = insertWebhooksData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        WebHooks webHooks = FakeWebhook.WEBHOOK;
        webHooks.setDocument(documentCreate);
        WebHooks insertWebhooks = insertWebhooks(webHooks);
        WebhookAuditFilters filters = WebhookAuditFilters.builder().id(insertWebhooks.getId()).build();
        long auditDocumentCount = webhookAuditRepository.getAuditLogCountById(filters);
        assertEquals(1, auditDocumentCount);
    }

    private Document insertWebhooksData(Scan scan, Query query, DataSource dataSource, ExecutionLog executionLog, Document document) {
        DataSource createdDataSource = insertDataSource(dataSource);
        query.setDataSource(createdDataSource);
        Query insertedQuery = insertQuery(query);
        scan.setQuery(insertedQuery);
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> insertCountry(FakeCountry.COUNTRY_EG));
        scan.setCountry(country);
        Scan scanInserted = insertScan(scan);
        executionLog.setScan(scanInserted);
        ExecutionLog insertedExecutionLog = insertExecutionLog(executionLog);
        document.setExecutionLog(insertedExecutionLog);
        return insertDocument(document);
    }
}
