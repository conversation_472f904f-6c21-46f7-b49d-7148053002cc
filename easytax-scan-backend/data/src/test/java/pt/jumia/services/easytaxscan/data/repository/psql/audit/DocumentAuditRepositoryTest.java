package pt.jumia.services.easytaxscan.data.repository.psql.audit;

import org.junit.jupiter.api.Test;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.data.repository.psql.BaseRepositoryTest;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.*;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.DocumentAuditFilters;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class DocumentAuditRepositoryTest extends BaseRepositoryTest {

    @Test
    public void insertAndFind_document_audits_success() {
        RequestContext.setUser(RequestUser.builder().username(TEST_USER).build());
        Document documentCreate = insertDocumentData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        Document insertDocument = insertDocument(documentCreate);
        DocumentAuditFilters filters = DocumentAuditFilters.builder().id(insertDocument.getId()).build();
        List<AuditedEntity<Document>> documentAuditById = documentAuditRepository.getDocumentAuditLogById(filters, insertDocument);
        assertEquals(2, documentAuditById.size());
    }

    @Test
    public void insertAndFind_document_audits_count_success() {
        RequestContext.setUser(RequestUser.builder().username(TEST_USER).build());
        RequestContext.setUser(RequestUser.builder().username(TEST_USER).build());
        Document documentCreate = insertDocumentData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        Document insertDocument = insertDocument(documentCreate);
        DocumentAuditFilters filters = DocumentAuditFilters.builder().id(insertDocument.getId()).build();
        long auditDocumentCount = documentAuditRepository.getAuditLogCountById(filters);
        assertEquals(2, auditDocumentCount);
    }

    private Document insertDocumentData(Scan scan, Query query, DataSource dataSource, ExecutionLog executionLog, Document document) {
        DataSource createdDataSource = insertDataSource(dataSource);
        query.setDataSource(createdDataSource);
        Query insertedQuery = insertQuery(query);
        scan.setQuery(insertedQuery);
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> insertCountry(FakeCountry.COUNTRY_EG));
        scan.setCountry(country);
        Scan scanInserted = insertScan(scan);
        executionLog.setScan(scanInserted);
        ExecutionLog insertedExecutionLog = insertExecutionLog(executionLog);
        document.setExecutionLog(insertedExecutionLog);
        return insertDocument(document);
    }
}
