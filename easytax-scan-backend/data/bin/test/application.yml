info.build.version: 0.1.0
server.port: ${EASYTAXSCAN_SERVER_PORT:8080}

spring:
  application.name: ${EASYTAXSCAN_SPRING_APPLICATION_NAME:EasyTaxScan}
  datasource.url: ***************************************************************************************
  flyway:
    enabled: ${EASYTAXSCAN_SPRING_FLYWAY_ENABLED:false}
    locations: ${EASYTAXSCAN_SPRING_FLYWAY_LOCATIONS:db/migration}
    schemas: ${EASYTAXSCAN_SPRING_FLYWAY_SCHEMAS:audit,public}

endpoints.default.web.enabled: false

#Spring actuator management configs
management.endpoints.web:
  base-path: / # Force base path to be root for instead of default "/actuator"
  exposure.include: health, prometheus   # Expose only health endpoint

#data
data:
  db:
    driver: ${EASYTAXSCAN_DATA_DB_DRIVER:org.postgresql.Driver}
    url: ***************************************************************************************
    username: ${EASYTAXSCAN_DATA_DB_USERNAME:postgres}
    password: ${EASYTAXSCAN_DATA_DB_PASSWORD:postgres}
    application-schema: ${EASYTAXSCAN_DATA_DB_APPLICATION_SCHEMA:public}
    audit-schema: ${EASYTAXSCAN_DATA_DB_AUDIT_SCHEMA:audit}
    quartz-schema: ${EASYTAXSCAN_DATA_DB_QUARTZ_SCHEMA:public}
    max-pool-size: ${EASYTAXSCAN_DATA_DB_MAX_POOL_SIZE:15}
    flyway:
      repair: ${EASYTAXSCAN_DATA_DB_MAX_POOL_SIZE:false}
  events:
    check-connection-timeout: 15s

#API
api:
  swagger-enabled: ${EASYTAXSCAN_API_SWAGGER_ENABLED:true}
  self-host: ${EASYTAXSCAN_API_SELF_HOST:http://localhost:8080/}
  allowed-domains: ${EASYTAXSCAN_API_ALLOWED_DOMAINS:http://localhost:9000,http://localhost:8080,http://localhost:3000,http://localhost:4200}

#Network
network:
  jokes.url: http://api.icndb.com

#ACL
acl:
  skip: ${EASYTAXSCAN_ACL_SKIP:false}
  url: ${EASYTAXSCAN_ACL_HOST:http://internal-api-acl-staging.jumia.services}
  app-name: ${EASYTAXSCAN_ACL_APP_NAME:EASYTAXSCAN}
  cache:
    strategy: ${EASYTAXSCAN_ACL_CACHE_STRATEGY:in-memory}
    in-memory:
      expiration-duration: ${EASYTAXSCAN_ACL_CACHE_IN_MEMORY_EXPIRATION_DURATION:5m}
    redis:
      host: ${EASYTAXSCAN_ACL_REDIS_HOST:dev-communications.2smgfr.0001.euw1.cache.amazonaws.com}
      port: ${EASYTAXSCAN_ACL_REDIS_PORT:6379}
      username-key-prefix: ${EASYTAXSCAN_ACL_REDIS_USERNAME_KEY_PREFIX:easytaxscan}
      password: ${EASYTAXSCAN_ACL_REDIS_PASSWORD:dummy}
      expiration-duration: ${EASYTAXSCAN_ACL_REDIS_EXPIRATION_DURATION:5m}
      timeout: ${EASYTAXSCAN_ACL_REDIS_TIMEOUT:0s}
  migrator-user:
    username: ${EASYTAXSCAN_ACL_MIGRATOR_USER_USERNAME:dummy}
    password: ${EASYTAXSCAN_ACL_MIGRATOR_USER_PASSWORD:dummy}
