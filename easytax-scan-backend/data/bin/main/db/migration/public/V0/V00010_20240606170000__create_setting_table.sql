
-- Sequence used to generate revision ids for audit records
DO
$$
BEGIN
    CREATE SEQUENCE hibernate_sequence INCREMENT 1 MINVALUE 1
    MAXVALUE 9223372036854775807
    START 2
    CACHE 1;
EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'Sequence "hibernate_sequence" already exists. Ignoring...';
END;
$$;


DO
$$
BEGIN
    CREATE SEQUENCE setting_id_seq START 1 INCREMENT 1;
EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'Sequence "setting_id_seq" already exists. Ignoring...';
END;
$$;

DO
$$
BEGIN
    CREATE TABLE setting
    (
        id          BIGINT       NOT NULL DEFAULT nextval('setting_id_seq') PRIMARY KEY,
        property     VARCHAR(255) NOT NULL,
        type         VARCHAR(64)  NOT NULL,
        override_key VARCHAR(255) UNIQUE,
        description  VARCHAR,
        value        VARCHAR      NOT NULL,
        created_by  <PERSON><PERSON><PERSON><PERSON>(255),
        created_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_by VA<PERSON>HAR (255),
        updated_at  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'table "setting" already exists. Ignoring...';
END;
$$;
