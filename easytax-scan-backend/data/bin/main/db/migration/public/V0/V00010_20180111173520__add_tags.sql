-- ########################### TAGS ###########################
DO
$$
BEGIN
    CREATE SEQUENCE tags_sequence_id_seq START 1 INCREMENT 1;

EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'Sequence "tags_sequence_id_seq" already exists. Ignoring...';
END;
$$;

DO
$$
BEGIN
    CREATE TABLE TAGS (
      ID          INTEGER DEFAULT nextval('tags_sequence_id_seq') PRIMARY KEY,
      NAME        VARCHAR(50)  UNIQUE NOT NULL,-- name unique because it doesnt make sense to have replicated tags.
      DESCRIPTION VARCHAR(255) NOT NULL,
      COLOR       VARCHAR(7)   NULL, -- Store as a varchar with 7 chars like #FFFFFF
      CREATED_AT  TIMESTAMP    NOT NULL
    );

EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'table "TAGS" already exists. Ignoring...';
END;
$$;
