-- ########################### DATASOURCE AUD ###########################

DO
$$
BEGIN
    CREATE TABLE datasource_aud (
      id          BIGINT ,
      code        VA<PERSON><PERSON><PERSON>(64)  ,
      status      VARCHAR(32)  ,
      description VARCHAR(1024) ,
      created_by  <PERSON><PERSON><PERSON><PERSON>(255),
      created_at  TIMESTAMP    ,
      updated_by <PERSON><PERSON><PERSON><PERSON> (255),
      updated_at  TIMESTAMP   ,
      rev          BIGINT,
      revtype      INTEGER,
      PRIMARY KEY (id, rev)
    );
EXCEPTION
        WHEN DUPLICATE_TABLE THEN
            RAISE NOTICE 'table "datasource_aud" already exists. Ignoring...';
END;
$$;
