package pt.jumia.services.easytaxscan.data.entities.audit;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QDefaultAudIdPsql is a Querydsl query type for DefaultAudIdPsql
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QDefaultAudIdPsql extends BeanPath<DefaultAudIdPsql> {

    private static final long serialVersionUID = -1310606498L;

    public static final QDefaultAudIdPsql defaultAudIdPsql = new QDefaultAudIdPsql("defaultAudIdPsql");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> rev = createNumber("rev", Integer.class);

    public QDefaultAudIdPsql(String variable) {
        super(DefaultAudIdPsql.class, forVariable(variable));
    }

    public QDefaultAudIdPsql(Path<? extends DefaultAudIdPsql> path) {
        super(path.getType(), path.getMetadata());
    }

    public QDefaultAudIdPsql(PathMetadata metadata) {
        super(DefaultAudIdPsql.class, metadata);
    }

}

