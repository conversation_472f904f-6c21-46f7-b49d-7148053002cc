package pt.jumia.services.easytaxscan.data.entities;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QDataSourcePsql is a Querydsl query type for DataSourcePsql
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QDataSourcePsql extends EntityPathBase<DataSourcePsql> {

    private static final long serialVersionUID = 784986470L;

    public static final QDataSourcePsql dataSourcePsql = new QDataSourcePsql("dataSourcePsql");

    public final StringPath code = createString("code");

    public final DateTimePath<java.time.LocalDateTime> createdAt = createDateTime("createdAt", java.time.LocalDateTime.class);

    public final StringPath createdBy = createString("createdBy");

    public final StringPath description = createString("description");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath status = createString("status");

    public final DateTimePath<java.time.LocalDateTime> updatedAt = createDateTime("updatedAt", java.time.LocalDateTime.class);

    public final StringPath updatedBy = createString("updatedBy");

    public QDataSourcePsql(String variable) {
        super(DataSourcePsql.class, forVariable(variable));
    }

    public QDataSourcePsql(Path<? extends DataSourcePsql> path) {
        super(path.getType(), path.getMetadata());
    }

    public QDataSourcePsql(PathMetadata metadata) {
        super(DataSourcePsql.class, metadata);
    }

}

