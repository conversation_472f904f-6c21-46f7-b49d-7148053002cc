package pt.jumia.services.easytaxscan.data.entities;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QSettingPsql is a Querydsl query type for SettingPsql
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QSettingPsql extends EntityPathBase<SettingPsql> {

    private static final long serialVersionUID = -94862165L;

    public static final QSettingPsql settingPsql = new QSettingPsql("settingPsql");

    public final DateTimePath<java.time.LocalDateTime> createdAt = createDateTime("createdAt", java.time.LocalDateTime.class);

    public final StringPath createdBy = createString("createdBy");

    public final StringPath description = createString("description");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath overrideKey = createString("overrideKey");

    public final StringPath property = createString("property");

    public final StringPath type = createString("type");

    public final DateTimePath<java.time.LocalDateTime> updatedAt = createDateTime("updatedAt", java.time.LocalDateTime.class);

    public final StringPath updatedBy = createString("updatedBy");

    public final StringPath value = createString("value");

    public QSettingPsql(String variable) {
        super(SettingPsql.class, forVariable(variable));
    }

    public QSettingPsql(Path<? extends SettingPsql> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSettingPsql(PathMetadata metadata) {
        super(SettingPsql.class, metadata);
    }

}

