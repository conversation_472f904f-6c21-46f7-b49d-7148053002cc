package pt.jumia.services.easytaxscan.data.entities;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QRevisionPsql is a Querydsl query type for RevisionPsql
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QRevisionPsql extends EntityPathBase<RevisionPsql> {

    private static final long serialVersionUID = -399325540L;

    public static final QRevisionPsql revisionPsql = new QRevisionPsql("revisionPsql");

    public final org.hibernate.envers.QDefaultRevisionEntity _super = new org.hibernate.envers.QDefaultRevisionEntity(this);

    public final StringPath email = createString("email");

    //inherited
    public final NumberPath<Integer> id = _super.id;

    public final DateTimePath<java.time.LocalDateTime> time = createDateTime("time", java.time.LocalDateTime.class);

    //inherited
    public final NumberPath<Long> timestamp = _super.timestamp;

    public QRevisionPsql(String variable) {
        super(RevisionPsql.class, forVariable(variable));
    }

    public QRevisionPsql(Path<? extends RevisionPsql> path) {
        super(path.getType(), path.getMetadata());
    }

    public QRevisionPsql(PathMetadata metadata) {
        super(RevisionPsql.class, metadata);
    }

}

