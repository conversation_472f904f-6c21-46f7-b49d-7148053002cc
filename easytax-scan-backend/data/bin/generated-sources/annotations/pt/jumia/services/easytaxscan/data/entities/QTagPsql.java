package pt.jumia.services.easytaxscan.data.entities;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QTagPsql is a Querydsl query type for TagPsql
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QTagPsql extends EntityPathBase<TagPsql> {

    private static final long serialVersionUID = -11307147L;

    public static final QTagPsql tagPsql = new QTagPsql("tagPsql");

    public final StringPath color = createString("color");

    public final DateTimePath<java.time.LocalDateTime> createdAt = createDateTime("createdAt", java.time.LocalDateTime.class);

    public final StringPath description = createString("description");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath name = createString("name");

    public QTagPsql(String variable) {
        super(TagPsql.class, forVariable(variable));
    }

    public QTagPsql(Path<? extends TagPsql> path) {
        super(path.getType(), path.getMetadata());
    }

    public QTagPsql(PathMetadata metadata) {
        super(TagPsql.class, metadata);
    }

}

