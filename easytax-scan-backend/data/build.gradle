apply plugin: 'idea'
apply from: '../config/quality/quality.gradle'

dependencies {

    implementation project(':domain')
    testImplementation project(':network')

    // Spring context
    implementation "org.springframework:spring-context-support:${springVersion}"
    implementation group: 'org.postgresql', name: 'postgresql', version: '42.7.5'

    // hibernate
    implementation "org.springframework:spring-orm:${springVersion}"
    implementation group: 'org.hibernate', name: 'hibernate-core', version: '6.4.1.Final'
    implementation group: 'org.hibernate', name: 'hibernate-core-jakarta', version: '5.6.15.Final'
    implementation group: 'org.hibernate', name: 'hibernate-entitymanager', version: '5.6.15.Final'
    implementation group: 'org.hibernate', name: 'hibernate-envers-jakarta', version: '5.6.15.Final'
    implementation group: 'com.zaxxer', name: 'HikariCP', version: '5.1.0'

    //JPA
    implementation group: 'org.springframework.data', name: 'spring-data-jpa', version: "${springBootVersion}"

    //QueryDSL
    implementation "com.querydsl:querydsl-jpa:${queryDslVersion}:jakarta"
    annotationProcessor "com.querydsl:querydsl-apt:${queryDslVersion}:jakarta"

    //quartz
    implementation "org.quartz-scheduler:quartz:${quartzVersion}"
    implementation 'com.mchange:c3p0:0.9.5.5'

    // Advanced jdbc driver implementation for postgresql event listening
    implementation group: 'com.impossibl.pgjdbc-ng', name: 'pgjdbc-ng', version: '0.8.9'

    // flyway db migrations
    implementation "org.flywaydb:flyway-core:11.3.2"
    runtimeOnly 'org.flywaydb:flyway-database-postgresql:11.3.2'
    implementation "org.springframework.boot:spring-boot-autoconfigure:${springBootVersion}"

    //ehcache
    implementation ("org.springframework.boot:spring-boot-starter-cache:${springBootVersion}"){
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    implementation 'javax.cache:cache-api:1.1.1'
    implementation 'org.ehcache:ehcache:3.10.8:jakarta'
    // Add JAXB runtime dependency
    implementation 'org.glassfish.jaxb:jaxb-runtime:2.3.3'

    testImplementation ("org.springframework.boot:spring-boot-starter-test:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    annotationProcessor("org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}")
    implementation "javax.annotation:javax.annotation-api:1.3.2"

}

// configure the folder for the generated sources (Query DSL entities)
compileJava {
    options.compilerArgs << "-s"
    options.compilerArgs << "$buildDir/generated/sources/annotationProcessor/java/main"

    doFirst {
        // make sure that directory exists
        file(file("$buildDir/generated/sources/annotationProcessor/java/main")).mkdirs()
    }
}

dockerCompose.isRequiredBy(test)
dockerCompose {
    useComposeFiles = ['./../dockers/docker-compose.yml']
    def includeOptionalDatabases = project.hasProperty('includeOptionalDatabases') && project.includeOptionalDatabases.toBoolean()
    if (project.gradle.startParameter.taskNames.contains('test')) {
        startedServices = ['easytax-scan-database']
    } else if (includeOptionalDatabases) {
        startedServices = ['easytax-scan-database', 'cashrec-database', 'nav-database']
    } else {
        startedServices = ['easytax-scan-database']
    }
}