package pt.jumia.services.easytaxscan.robots.audit;

import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.api.payloads.response.audit.AuditApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.robots.RestApiRobot;

import java.util.List;
import java.util.stream.Collectors;

public class QueryAuditRobot extends RestApiRobot {

    public static final String QUERY_AUDIT_PATH = "/api/audit/query";

    public static final String CONTENT_TYPE = "application/json";


    public QueryAuditRobot(int port) {
        super(port);
    }

    public List<AuditedEntity> fetchQueryAudits(Long queryId) {
        RequestSpecification request = givenAuth();
        return request
                .when().get(QUERY_AUDIT_PATH + "/" + queryId)
                .then().statusCode(HttpStatus.OK.value())
                .extract().jsonPath()
                .getList("results", AuditApiResponsePayload.class)
                .stream()
                .map(AuditApiResponsePayload::toEntity)
                .collect(Collectors.toList());
    }

    public ValidatableResponse fetchAuditFails(Long queryId) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(QUERY_AUDIT_PATH + "/" + queryId)
                .then();
    }


}
