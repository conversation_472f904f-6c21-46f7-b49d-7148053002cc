package pt.jumia.services.easytaxscan.endpoints;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.*;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogSortFilters;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * End-to-End test of the ExecutionLog API, ensuring all operations work correctly (create, read, update, delete).
 */
public class ExecutionLogEndpointTest extends BaseEndpointTest {


    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void insert_executionLog() {
        ExecutionLog executionLog = insertExecutionLog(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE);
        assertThat(executionLog.getId()).isNotNull();
        assertThat(FakeExecutionLog.EXECUTION_LOG_CREATE.getException()).isEqualTo(executionLog.getException());
    }

    @Test
    public void readOne_executionLog() {
        ExecutionLog executionLog = insertExecutionLog(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE);
        ExecutionLog executionLogInserted = executionLogRobot.readOneQuery(executionLog.getId());
        assertThat(executionLog.getId()).isEqualTo(executionLogInserted.getId());
        assertThat(executionLog.getException()).isEqualTo(executionLogInserted.getException());
    }


    @Test
    public void fetchAll_executionLogs() {
        insertExecutionLog(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE,
                FakeExecutionLog.EXECUTION_LOG_CREATE);
        insertExecutionLog(FakeScan.SCAN_UPDATE, FakeQuery.QUERY_UPDATE, FakeDataSource.DATA_SOURCE_UPDATE,
                FakeExecutionLog.EXECUTION_LOG_UPDATE);
        var executionLogList = executionLogRobot.fetchAllExecutionLogs(null, null, null);
        assertNotNull(executionLogList);
        assertThat(executionLogList.size()).isGreaterThanOrEqualTo(2);
    }

    @Test
    public void findAll_ExecutionLogs_success_withFilters() {

        insertExecutionLog(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE,
                FakeExecutionLog.EXECUTION_LOG_CREATE);
        insertExecutionLog(FakeScan.SCAN_UPDATE, FakeQuery.QUERY_UPDATE, FakeDataSource.DATA_SOURCE_UPDATE,
                FakeExecutionLog.EXECUTION_LOG_UPDATE);

        // Setup filters
        ExecutionLogFilters filters = ExecutionLogFilters.builder()
                .status(ExecutionLog.Status.ACTIVE)
                .build();
        ExecutionLogSortFilters sortFilters = ExecutionLogSortFilters.builder()
                .direction(OrderDirection.ASC)
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        // Fetch filtered ExecutionLogs
        List<ExecutionLog> executionLogs = executionLogRobot.fetchAllExecutionLogs(filters, sortFilters, pageFilters);
        assertNotNull(executionLogs);
        assertEquals(2, executionLogs.size());
    }
    private ExecutionLog insertExecutionLog(Scan scan, Query query, DataSource dataSource, ExecutionLog executionLog) {
        DataSource createdDataSource = createDataSource(dataSource);
        query.setDataSource(createdDataSource);
        Query insertQuery = createQuery(query);
        scan.setQuery(insertQuery);
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> createCountry(FakeCountry.COUNTRY_EG));
        scan.setCountry(country);
        Scan scanCreated = createScan(scan);
        executionLog.setScan(scanCreated);
        return createExecutionLog(executionLog);
    }

    @Test
    public void all_executionLogs_shouldBeUnAuthorized() {
       ExecutionLog executionLog= insertExecutionLog(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE,
                FakeExecutionLog.EXECUTION_LOG_CREATE);
        anonymousUser();
        executionLogRobot.readAllFails()
                .statusCode(HttpStatus.UNAUTHORIZED.value());
        executionLogRobot.readOneQueryFails(executionLog.getId())
                .statusCode(HttpStatus.UNAUTHORIZED.value());
        executionLogRobot.createFails(executionLog)
                .statusCode(HttpStatus.UNAUTHORIZED.value());
    }
}
