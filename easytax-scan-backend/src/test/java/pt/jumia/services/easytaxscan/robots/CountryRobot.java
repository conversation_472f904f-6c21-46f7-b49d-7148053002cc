package pt.jumia.services.easytaxscan.robots;

import io.restassured.response.ValidatableResponse;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.api.payloads.request.CountryApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.CountryApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.Country;

public class CountryRobot extends RestApiRobot {

    public static final String COUNTRY_BASE_PATH = "/api/countries";
    public static final String CONTENT_TYPE = "application/json";

    public CountryRobot(int port) {
        super(port);
    }

    public Country create(Country country) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new CountryApiRequestPayload(country))
                .when().post(COUNTRY_BASE_PATH)
                .then().statusCode(HttpStatus.CREATED.value())
                .extract().as(CountryApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse createFails(Country country) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new CountryApiRequestPayload(country))
                .when().post(COUNTRY_BASE_PATH)
                .then();
    }

    public Country readOneCountry(Long id) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(COUNTRY_BASE_PATH + "/" + id)
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(CountryApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse readOneCountryFails(Long id) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(COUNTRY_BASE_PATH + "/" + id)
                .then();
    }

    public Country update(Long id, Country country) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new CountryApiRequestPayload(country))
                .when().put(COUNTRY_BASE_PATH + "/" + id)
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(CountryApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse updateFails(Long id, Country country) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new CountryApiRequestPayload(country))
                .when().put(COUNTRY_BASE_PATH + "/" + id)
                .then();
    }

}
