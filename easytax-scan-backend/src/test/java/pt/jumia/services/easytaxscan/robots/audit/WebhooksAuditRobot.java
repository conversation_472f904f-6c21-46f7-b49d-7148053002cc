package pt.jumia.services.easytaxscan.robots.audit;

import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.api.payloads.response.audit.AuditApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.robots.RestApiRobot;

import java.util.List;
import java.util.stream.Collectors;

public class WebhooksAuditRobot extends RestApiRobot {

    public static final String WEBHOOKS_AUDIT_PATH = "/api/audit/webhook";

    public static final String CONTENT_TYPE = "application/json";


    public WebhooksAuditRobot(int port) {
        super(port);
    }

    public List<AuditedEntity> fetchWebhooks(Long webHooksId) {
        RequestSpecification request = givenAuth();
        return request
                .when().get(WEBHOOKS_AUDIT_PATH + "/" + webHooksId)
                .then().statusCode(HttpStatus.OK.value())
                .extract().jsonPath()
                .getList("results", AuditApiResponsePayload.class)
                .stream()
                .map(AuditApiResponsePayload::toEntity)
                .collect(Collectors.toList());
    }

    public ValidatableResponse fetchAuditFails(Long webHooksId) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(WEBHOOKS_AUDIT_PATH + "/" + webHooksId)
                .then();
    }


}
