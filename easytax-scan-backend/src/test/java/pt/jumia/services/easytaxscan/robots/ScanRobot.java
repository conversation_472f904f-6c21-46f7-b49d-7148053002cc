package pt.jumia.services.easytaxscan.robots;

import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.api.payloads.request.ScanApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.ScanApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanSortFilters;

import java.util.List;
import java.util.stream.Collectors;

public class ScanRobot extends RestApiRobot {

    public static final String SCAN_BASE_PATH = "/api/scans";
    public static final String CONTENT_TYPE = "application/json";

    public ScanRobot(int port) {
        super(port);
    }

    public Scan create(Scan scan) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new ScanApiRequestPayload(scan))
                .when().post(SCAN_BASE_PATH)
                .then().statusCode(HttpStatus.CREATED.value())
                .extract().as(ScanApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse createFails(Scan scan) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new ScanApiRequestPayload((scan)))
                .when().post(SCAN_BASE_PATH)
                .then();
    }

    public Scan readOneScan(Long id) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(SCAN_BASE_PATH + "/" + id)
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(ScanApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse readOneScanFails(Long id) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(SCAN_BASE_PATH + "/" + id)
                .then();
    }

    public Scan update(Long id, Scan scan) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new ScanApiRequestPayload((scan)))
                .when().put(SCAN_BASE_PATH + "/" + id)
                .then().statusCode(200)
                .extract().as(ScanApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse updateFails(Long id, Scan scan) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new ScanApiRequestPayload(scan))
                .when().put(SCAN_BASE_PATH + "/" + id)
                .then();
    }


    public List<Scan> fetchAllTransactions(
            ScanFilters scanFilters,
            ScanSortFilters sortingFilters,
            PageFilters pageFilters
    ) {
        RequestSpecification request = givenAuth();
        if (scanFilters != null) {
            buildQueryParams(scanFilters, request);
        }
        if (sortingFilters != null) {
            request.queryParams(sortingFilters.getAsMap());
        }
        if (pageFilters != null) {
            request.queryParams(pageFilters.getAsMap());
        }
        return request
                .when().get(SCAN_BASE_PATH)
                .then().statusCode(HttpStatus.OK.value())
                .extract().jsonPath()
                .getList("results", ScanApiResponsePayload.class)
                .stream()
                .map(ScanApiResponsePayload::toEntity)
                .collect(Collectors.toList());
    }

    private void buildQueryParams(ScanFilters scanFilters, RequestSpecification request) {
        if (scanFilters.getText() != null) {
            request.param("text", scanFilters.getText());
        }
        if (scanFilters.getStatus() != null) {
            request.param("status", scanFilters.getStatus());
        }
    }

    public ValidatableResponse readAllFails() {
        return givenAuth()
                .when().get(SCAN_BASE_PATH)
                .then();
    }

    public void executeManualScan(Long id) {
        givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().patch(SCAN_BASE_PATH + "/" + id + "/execute")
                .then();
    }

    public void delete(Long id) {
        givenAuth()
                .when().delete(SCAN_BASE_PATH + "/" + id)
                .then().statusCode(200);
    }
}
