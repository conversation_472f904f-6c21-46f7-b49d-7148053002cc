package pt.jumia.services.easytaxscan.robots;


import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.not;

import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.endpoints.administrator.SpringActuatorEndpointsTest;

/**
 * SpringActuator robot used in {@link SpringActuatorEndpointsTest}
 */
public class SpringActuatorRobot extends RestApiRobot {


    public SpringActuatorRobot(int port) {
        super(port);
    }

    public void fetchHealth() {
        givenAuth()
            .get("/health")
            .then()
            .body(containsString("status"))
            .statusCode(HttpStatus.OK.value());
    }

    public void fetchHealthJson() {
        givenAuth()
            .get("/health.json")
            .then()
            .body(containsString("status"))
            .statusCode(not(HttpStatus.UNAUTHORIZED.value()));
    }

}
