package pt.jumia.services.easytaxscan.endpoints;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.Scan.Status;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentSortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanSortFilters;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;


/**
 * End-to-End test of the Scan API, performing all CRUD operations and making sure that data is correctly updated
 */
public class ScanEndpointTest extends BaseEndpointTest {

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void insert_Scan_shouldBeSuccess() {
        Scan scanCreated = insertQueryDataSourceDetails(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        assertThat(scanCreated.getId()).isNotNull();
        assertThat(FakeScan.SCAN_CREATE.getCode()).isEqualTo(scanCreated.getCode());
    }

    private Scan insertQueryDataSourceDetails(Scan scan, Query query, DataSource dataSource) {
        DataSource createdDataSource = createDataSource(dataSource);
        query.setDataSource(createdDataSource);
        Query insertQuery = createQuery(query);
        scan.setQuery(insertQuery);
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> createCountry(FakeCountry.COUNTRY_EG));

        scan.setCountry(country);
        return createScan(scan);
    }

    @Test
    public void insertAndFind_Scan_shouldBeSuccess() {
        Scan scanCreated = insertQueryDataSourceDetails(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        Scan ScanFetched = scanRobot.readOneScan(scanCreated.getId());
        assertThat(ScanFetched.getId()).isNotNull();
        assertThat(FakeScan.SCAN_CREATE.getDescription()).isEqualTo(ScanFetched.getDescription());
    }

    @Test
    public void insertAndFind_Scan_Failure() {
        scanRobot.readOneScanFails(99999L)
                .statusCode(HttpStatus.NOT_FOUND.value());
    }

    @Test
    public void insert_ScanDuplicate_shouldFail() {
        Scan scanCreated = insertQueryDataSourceDetails(FakeScan.SCAN_FAIL, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        scanRobot.createFails(scanCreated)
                .statusCode(HttpStatus.CONFLICT.value());

    }

    @Test
    public void update_Scan_shouldBeSuccess() {
        Scan scanCreated = insertQueryDataSourceDetails(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        scanCreated.setDescription("Scan has been modified");
        scanCreated.setStatus(Status.INACTIVE);
        Scan updatedScan = scanRobot.update(scanCreated.getId(), scanCreated);
        assertThat(Status.INACTIVE).isEqualTo(updatedScan.getStatus());
        assertThat("Scan has been modified").isEqualTo(updatedScan.getDescription());
    }

    @Test
    public void update_Scan_failure() {
        Scan scanCreated = insertQueryDataSourceDetails(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        scanCreated.setDescription("Scan has been modified");
        scanCreated.setStatus(null);
        scanRobot.updateFails(scanCreated.getId(), scanCreated)
                .statusCode(HttpStatus.BAD_REQUEST.value());
    }

    @Test
    public void findAll_Scan_success_withFilters() {
        populateFindAllFilterData();
        ScanFilters filters = ScanFilters.builder().
                status(Scan.Status.ACTIVE)
                .mode(Scan.Mode.DRYRUN)
                .text("Scan")
                .build();
        ScanSortFilters sortFilters = ScanSortFilters.builder()
                .direction(OrderDirection.ASC).build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<Scan> ScansList = scanRobot.fetchAllTransactions(filters, sortFilters, pageFilters);
        assertNotNull(ScansList);
        assertEquals(2, ScansList.size());
        assertEquals(Scan.Status.ACTIVE, ScansList.stream().findAny().get().getStatus());
        assertThat(ScansList).isNotEmpty()
                .anyMatch(Scan -> Scan.getDescription().contains("Scan"));
    }

    @Test
    public void findAll_Scan_noResult_withInvalidFilters() {
        populateFindAllFilterData();
        ScanFilters filters = ScanFilters.builder().
                status(Scan.Status.INACTIVE)
                .text("InvalidData").build();
        ScanSortFilters sortFilters = ScanSortFilters.builder()
                .direction(OrderDirection.ASC).build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<Scan> ScansList = scanRobot.fetchAllTransactions(filters, sortFilters, pageFilters);
        assertEquals(0, ScansList.size());
    }

    @Test
    public void executeManualScan_shouldBeSuccess() {
        Scan scanCreated = insertQueryDataSourceDetails(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_TEST);
        scanRobot.executeManualScan(scanCreated.getId());
        List<Document> documentList = documentRepository.findAll(DocumentFilters.builder().build(), DocumentSortFilters.builder().build(),
                PageFilters.builder().build());
        documentRepository.deleteById(documentList.getFirst().getId());
        executionLogRepository.deleteById(documentList.getFirst().getExecutionLog().getId());
        assertNotNull(scanCreated);
    }

    private void populateFindAllFilterData() {
        insertQueryDataSourceDetails(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE);
        insertQueryDataSourceDetails(FakeScan.SCAN_FILTER_DATA3, FakeQuery.QUERY_FILTER_DATA1, FakeDataSource.DATA_SOURCE_FILTER_DATA4);
        insertQueryDataSourceDetails(FakeScan.SCAN_FILTER_DATA2, FakeQuery.QUERY_FILTER_DATA2, FakeDataSource.DATA_SOURCE_FILTER_DATA3);

    }


}

