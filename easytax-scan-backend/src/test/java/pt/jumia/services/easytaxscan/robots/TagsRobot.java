package pt.jumia.services.easytaxscan.robots;

import io.restassured.response.ValidatableResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.api.payloads.request.TagApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.TagApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.Tag;

public class TagsRobot extends RestApiRobot {

    public TagsRobot(int port) {
        super(port);
    }

    public Tag create(Tag tag) {
        return givenAuth()
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .body(new TagApiRequestPayload((tag)))
            .when().post("/api/tags")
            .then().statusCode(HttpStatus.CREATED.value())
            .extract().as(TagApiResponsePayload.class).toEntity();
    }


    public List<Tag> readAll() {
        return Arrays.stream(givenAuth()
            .when().get("/api/tags")
            .then().statusCode(HttpStatus.OK.value())
            .extract().as(TagApiResponsePayload[].class))
            .map(TagApiResponsePayload::toEntity)
            .collect(Collectors.toList());
    }

    public Tag readOne(Long id) {
        return givenAuth()
            .when().get("/api/tags/" + id)
            .then().statusCode(HttpStatus.OK.value())
            .extract().as(TagApiResponsePayload.class)
            .toEntity();
    }

    public void update(Long id, Tag tag) {
        givenAuth()
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .body(new TagApiRequestPayload(tag))
            .when().put("/api/tags/" + id)
            .then().statusCode(200);
    }

    public void delete(Long id) {
        givenAuth()
            .when().delete("/api/tags/" + id)
            .then().statusCode(200);
    }

    public ValidatableResponse createFails(Tag tag) {
        return givenAuth()
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .body(new TagApiRequestPayload((tag)))
            .when().post("/api/tags")
            .then();
    }


    public ValidatableResponse updateFails(Long id, Tag tag) {
        return givenAuth()
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .body(new TagApiRequestPayload(tag))
            .when().put("/api/tags/" + id)
            .then();
    }


    public ValidatableResponse deleteFails(Long id) {
        return givenAuth()
            .when().delete("/api/tags/" + id)
            .then();
    }

    public ValidatableResponse readOneFails(Long id) {
        return givenAuth()
            .when().get("/api/tags/" + id)
            .then();
    }

    public ValidatableResponse readAllFails() {
        return givenAuth()
            .when().get("/api/tags")
            .then();
    }
}
