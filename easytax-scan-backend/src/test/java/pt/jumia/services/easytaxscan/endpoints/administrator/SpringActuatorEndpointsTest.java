package pt.jumia.services.easytaxscan.endpoints.administrator;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import pt.jumia.services.easytaxscan.domain.Profiles;
import pt.jumia.services.easytaxscan.robots.SpringActuatorRobot;


/**
 * SpringActuator integration tests
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles({Profiles.FAKE_CLIENTS, Profiles.TEST})
public class SpringActuatorEndpointsTest {

    @LocalServerPort
    protected int port;
    private SpringActuatorRobot springActuatorRobot;

    @BeforeEach
    public void baseSetUp() {
        springActuatorRobot = new SpringActuatorRobot(port);
    }

    @Test
    public void healthTest() {
        springActuatorRobot.fetchHealth();
        springActuatorRobot.fetchHealthJson();
    }
}
