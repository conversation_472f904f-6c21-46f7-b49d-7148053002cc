package pt.jumia.services.easytaxscan.endpoints;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeSubQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.QueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.QuerySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;
import pt.jumia.services.easytaxscan.domain.utils.ResourceLoader;

import java.io.IOException;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * End-to-End test of the Query API, ensuring all operations work correctly (create, read, update, delete).
 */
public class QueryEndpointTest extends BaseEndpointTest {


    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void insert_Query() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        Query query = FakeQuery.QUERY_CREATE;
        query.setDataSource(dataSourceCreated);
        Query insertedQuery = createQuery(query);
        assertThat(insertedQuery.getId()).isNotNull();
        assertThat(FakeQuery.QUERY_CREATE.getCode()).isEqualTo(insertedQuery.getCode());
    }

    @Test
    public void readOne_Query() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        Query query = FakeQuery.QUERY_CREATE;
        query.setDataSource(dataSourceCreated);
        Query insertedQuery = createQuery(query);
        Query fetchedQuery = queryRobot.readOneQuery(insertedQuery.getId());
        assertThat(fetchedQuery.getId()).isEqualTo(insertedQuery.getId());
        assertThat(fetchedQuery.getCode()).isEqualTo(insertedQuery.getCode());
    }

    @Test
    public void update_Query() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        Query query = FakeQuery.QUERY_CREATE;
        query.setDataSource(dataSourceCreated);
        Query insertedQuery = createQuery(query);
        insertedQuery.setDescription("Updated description");
        Query updatedQuery = queryRobot.update(insertedQuery.getId(), insertedQuery);
        assertThat(updatedQuery.getDescription()).isEqualTo("Updated description");
    }

    @Test
    public void update_Query_Failure() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        Query query = FakeQuery.QUERY_CREATE;
        query.setDataSource(dataSourceCreated);
        Query insertedQuery = createQuery(query);
        insertedQuery.setDescription(null);
        queryRobot.updateFails(insertedQuery.getId(), insertedQuery)
                .statusCode(400);
    }

    @Test
    public void fetchAll_Queries() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        Query query1 = FakeQuery.QUERY_CREATE;
        query1.setDataSource(dataSourceCreated);
        createQuery(query1);
        Query query2 = FakeQuery.QUERY_UPDATE;
        query2.setDataSource(dataSourceCreated);
        createQuery(query2);
        var queryList = queryRobot.fetchAllQueries(null, null, null);
        assertThat(queryList.size()).isGreaterThanOrEqualTo(2);
    }

    @Test
    public void findAll_Query_success_withFilters() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        Query query1 = FakeQuery.QUERY_CREATE;
        query1.setDataSource(dataSourceCreated);
        createQuery(query1);
        Query query2 = FakeQuery.QUERY_UPDATE;
        query2.setDataSource(dataSourceCreated);
        createQuery(query2);
        QueryFilters filters = QueryFilters.builder()
                .text("Query Create Test")
                .build();
        QuerySortFilters sortFilters = QuerySortFilters.builder()
                .direction(OrderDirection.ASC)
                .build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<Query> queriesList = queryRobot.fetchAllQueries(filters, sortFilters, pageFilters);
        assertNotNull(queriesList);
        assertEquals(2, queriesList.size());
        assertThat(queriesList).isNotEmpty()
                .anyMatch(query -> query.getDescription().contains("Query Create Test"));
    }

    @Test
    public void fetchSubQuery_byQueryId_withFilters() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        SubQuery subQuery = FakeSubQuery.SUB_QUERY_CREATE;
        Query query = FakeQuery.QUERY_CREATE;
        query.setDataSource(dataSourceCreated);
        Query insertedQuery = createQuery(query);
        subQuery.setId(insertedQuery.getId());
        subQueryRepository.insert(insertedQuery.getId(), subQuery);
        SubQueryFilters subQueryFilters = SubQueryFilters.builder()
                .text(String.valueOf(SubQuery.Status.ACTIVE))
                .build();
        SubQuerySortFilters subQuerySortFilters = SubQuerySortFilters.builder()
                .direction(OrderDirection.ASC)
                .build();
        List<SubQuery> subQueries = queryRobot.fetchAllSubQueryById(
                insertedQuery.getId(),
                subQueryFilters,
                subQuerySortFilters,
                PageFilters.builder().build()
        );
        subQueryRepository.deleteSubQueryByQueryId(insertedQuery.getId());
        assertThat(subQueries).isNotEmpty();
    }

    @Test
    public void fetchFields_fromQuery_success() throws IOException {
        String sampleJson = ResourceLoader.getStringFromFile("query/sampleresult.json");

        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);

        Query query = FakeQuery.QUERY_CREATE;
        query.setDataSource(dataSourceCreated);
        query.setSampleResult(sampleJson);

        Query insertedQuery = createQuery(query);

        List<String> fields = queryRobot.fetchFieldsFromQuery(insertedQuery.getId());

        assertThat(fields).isNotEmpty();
        assertThat(fields).contains(
                "context.id",
                "context.items",
                "context.items[0]",
                "context.items[0].id",
                "context.items[0].item_name",
                "context.items[0].item_details",
                "context.items[0].item_details[0].sku",
                "context.items[0].item_details[0].item_price"
        );

        assertThat(fields.size()).isGreaterThan(50);
    }

    @Test
    public void fetchFields_queryNotFound_returns404() {
        queryRobot.fetchFieldsFromQueryFails(999999L)
                .statusCode(404);
    }

    @Test
    public void fetchFields_emptySampleResult_returns400() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);

        Query query = FakeQuery.QUERY_CREATE;
        query.setDataSource(dataSourceCreated);
        query.setSampleResult("");

        Query insertedQuery = createQuery(query);

        queryRobot.fetchFieldsFromQueryFails(insertedQuery.getId())
                .statusCode(400);
    }


}
