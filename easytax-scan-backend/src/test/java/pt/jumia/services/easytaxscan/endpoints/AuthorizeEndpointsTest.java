package pt.jumia.services.easytaxscan.endpoints;

import static io.restassured.RestAssured.enableLoggingOfRequestAndResponseIfValidationFails;
import static io.restassured.RestAssured.given;
import static io.restassured.RestAssured.urlEncodingEnabled;

import io.restassured.RestAssured;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.robots.RestApiRobot;

/**
 * Tests the authorization capacities of the application
 */
public class AuthorizeEndpointsTest extends BaseEndpointTest {

    @BeforeEach
    public void setUp() {
        RestAssured.port = port;
        enableLoggingOfRequestAndResponseIfValidationFails();
        urlEncodingEnabled = true;
        loginUser("<EMAIL>");
    }

    @Test
    public void testBearerAuth() {
        given()
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .header(HttpHeaders.AUTHORIZATION, "Bearer " + RestApiRobot.TOKEN)
            .when().get("/api/tags")
            .then().statusCode(HttpStatus.OK.value());
    }

    @Test
    public void testBasicAuth() {
        //sending request with Basic auth (username: <EMAIL>, pswd: password)
        given()
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .header(HttpHeaders.AUTHORIZATION, "Basic ****************************************")
            .when().get("/api/tags")
            .then().statusCode(HttpStatus.OK.value());
    }

    @Test
    public void testInvalidAuth() {
        //sending invalid authorization header
        given()
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .header(HttpHeaders.AUTHORIZATION, "random stuff")
            .when().get("/api/tags")
            .then().statusCode(HttpStatus.BAD_REQUEST.value());
    }

}
