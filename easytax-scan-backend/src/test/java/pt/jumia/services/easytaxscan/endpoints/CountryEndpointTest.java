package pt.jumia.services.easytaxscan.endpoints;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.robots.CountryRobot;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;


public class CountryEndpointTest extends BaseEndpointTest {

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
        countryRobot = new CountryRobot(port);
    }

    @Test
    public void insert_Country_shouldBeSuccess() {
        Country country = FakeCountry.COUNTRY_CREATE;
        Country createdCountry = countryRobot.create(country);

        assertNotNull(createdCountry.getId());
        assertEquals(FakeCountry.COUNTRY_CREATE.getCountryCode(), createdCountry.getCountryCode());
    }

    @Test
    public void insertAndFind_Country_shouldBeSuccess() {
        Country country = FakeCountry.COUNTRY_CREATE.toBuilder()
                .countryCode("MOCK-" + System.currentTimeMillis())
                .build();
        Country createdCountry = createCountry(country);
        Country fetchedCountry = countryRobot.readOneCountry(createdCountry.getId());
        assertNotNull(fetchedCountry.getId());
        assertEquals(country.getCountryName(), fetchedCountry.getCountryName());
    }


    @Test
    public void insertAndFind_Country_Failure() {
        countryRobot.readOneCountryFails(99999L)
                .statusCode(HttpStatus.NOT_FOUND.value());
    }

    @Test
    public void insert_CountryDuplicate_shouldFail() {
        Country country = FakeCountry.COUNTRY_CREATE;
        createCountry(country);
        countryRobot.createFails(country)
                .statusCode(HttpStatus.CONFLICT.value());
    }

    @Test
    public void update_Country_shouldBeSuccess() {
        Country country = FakeCountry.COUNTRY_CREATE;
        Country createdCountry = createCountry(country);
        createdCountry.setCountryName("Country Modified");
        Country updatedCountry = countryRobot.update(createdCountry.getId(), createdCountry);
        assertEquals("Country Modified", updatedCountry.getCountryName());
    }

    @Test
    public void update_Country_Failure() {
        Country country = FakeCountry.COUNTRY_CREATE;
        Country createdCountry = createCountry(country);
        createdCountry.setCountryName(null);
        countryRobot.updateFails(createdCountry.getId(), createdCountry)
                .statusCode(HttpStatus.BAD_REQUEST.value());
        Country nonExistentCountry = FakeCountry.COUNTRY_CREATE.toBuilder().build();
        countryRobot.updateFails(99999L, nonExistentCountry)
                .statusCode(HttpStatus.NOT_FOUND.value());
    }

}
