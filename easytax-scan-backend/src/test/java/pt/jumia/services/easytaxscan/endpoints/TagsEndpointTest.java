package pt.jumia.services.easytaxscan.endpoints;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.domain.entities.Tag;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * End to End test of the Tag API, performing all CRUD operations and making sure that data is correctly updated
 */
public class TagsEndpointTest extends BaseEndpointTest {

    private static final Tag A_TAG = Tag.builder()
            .name("A tag")
            .description("This is just for testing purposes")
            .color("#FFFFFF")
            .build();
    private static final Tag ANOTHER_TAG = Tag.builder()
            .name("Another tag")
            .description("This is another tag just for testing purposes")
            .color("#AFAFAF")
            .build();

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void createAndGetTags() {
        Tag aTag = createTag(A_TAG);
        Tag anotherTag = createTag(ANOTHER_TAG);

        List<Tag> allTags = tagsRobot.readAll();

        assertThat(allTags).containsExactlyInAnyOrder(aTag, anotherTag);
    }

    @Test
    public void createTagDuplicate() {
        createTag(A_TAG);

        tagsRobot.createFails(A_TAG)
                .statusCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
    }

    @Test
    public void updateTag() {
        Tag tagCreated = createTag(A_TAG);

        Tag tagToUpdate = tagCreated.toBuilder()
                .name("Tag updated")
                .description("This tag was updated")
                .build();
        tagsRobot.update(tagCreated.getId(), tagToUpdate);

        Tag tagUpdated = tagsRobot.readOne(tagCreated.getId());
        assertEquals(tagToUpdate, tagUpdated);
    }

    @Test
    public void updateTagNotFound() {
        tagsRobot.updateFails(999999L, A_TAG)
                .statusCode(HttpStatus.NOT_FOUND.value());
    }

    @Test
    public void deleteTag() {
        Tag tagToDelete = tagsRobot.create(A_TAG);

        tagsRobot.delete(tagToDelete.getId());

        tagsRobot.readOneFails(tagToDelete.getId())
                .statusCode(HttpStatus.NOT_FOUND.value());
    }

    @Test
    public void deleteNotFound() throws Exception {
        tagsRobot.deleteFails(9999999L)
                .statusCode(HttpStatus.NOT_FOUND.value());
    }

    @Test
    public void testUnauthorized() {
        Tag aTag = createTag(A_TAG);

        anonymousUser();
        tagsRobot.readAllFails()
                .statusCode(HttpStatus.UNAUTHORIZED.value());
        tagsRobot.readOneFails(aTag.getId())
                .statusCode(HttpStatus.UNAUTHORIZED.value());
        tagsRobot.createFails(A_TAG)
                .statusCode(HttpStatus.UNAUTHORIZED.value());
        tagsRobot.updateFails(aTag.getId(), A_TAG)
                .statusCode(HttpStatus.UNAUTHORIZED.value());
        tagsRobot.deleteFails(aTag.getId())
                .statusCode(HttpStatus.UNAUTHORIZED.value());
    }
}
