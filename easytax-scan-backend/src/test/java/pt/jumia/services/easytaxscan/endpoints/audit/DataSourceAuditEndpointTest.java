package pt.jumia.services.easytaxscan.endpoints.audit;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.endpoints.BaseEndpointTest;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static pt.jumia.services.easytaxscan.domain.entities.AuditedEntities.DATA_SOURCE;
import static pt.jumia.services.easytaxscan.domain.entities.AuditedEntity.OperationType.CREATE;

public class DataSourceAuditEndpointTest extends BaseEndpointTest {

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void fetch_dataSourceAudits_shouldBeSuccess() throws JsonProcessingException {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);

        assertThat(dataSourceCreated.getId()).isNotNull();

        List<AuditedEntity> response =
                dataSourceAuditRobot.fetchTransactionAudits(dataSourceCreated.getId());

        assertThat(response.size()).isEqualTo(1);
        assertThat(response.getFirst().getOperationType()).isEqualTo(CREATE);
        assertThat(response.getFirst().getAuditedEntity()).isEqualTo(DATA_SOURCE);
    }

    @Test
    public void fetch_dataSourceAudit_shouldBeUnAuthorized() {
        anonymousUser();
        dataSourceAuditRobot.fetchAuditFails(1234L)
                .statusCode(HttpStatus.UNAUTHORIZED.value());
    }
}
