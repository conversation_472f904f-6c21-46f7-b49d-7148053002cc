package pt.jumia.services.easytaxscan.endpoints.audit;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.endpoints.BaseEndpointTest;
import pt.jumia.services.easytaxscan.robots.audit.ExecutionLogAuditRobot;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static pt.jumia.services.easytaxscan.domain.entities.AuditedEntities.EXECUTION_LOG;
import static pt.jumia.services.easytaxscan.domain.entities.AuditedEntity.OperationType.CREATE;

/**
 * End-to-End test of the ExecutionLogAudit API
 */
public class ExecutionLogAuditEndpointTest extends BaseEndpointTest {

    private ExecutionLogAuditRobot executionLogAuditRobot;

    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
        executionLogAuditRobot = new ExecutionLogAuditRobot(port);
    }

    @Test
    public void fetch_executionLogAudits_shouldBeSuccess() throws JsonProcessingException {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        Query query = FakeQuery.QUERY_CREATE;
        query.setDataSource(dataSourceCreated);
        Query insertedQuery = createQuery(query);
        assertThat(insertedQuery.getId()).isNotNull();
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> createCountry(FakeCountry.COUNTRY_EG));
        Scan scan = FakeScan.SCAN_CREATE;
        scan.setQuery(insertedQuery);
        scan.setCountry(country);
        Scan insertedScan = createScan(scan);
        assertThat(insertedScan.getId()).isNotNull();
        ExecutionLog executionLog = FakeExecutionLog.EXECUTION_LOG_CREATE;
        executionLog.setScan(insertedScan);
        ExecutionLog insertedExecutionLog = createExecutionLog(executionLog);
        assertThat(insertedExecutionLog.getId()).isNotNull();
        List<AuditedEntity> response = executionLogAuditRobot.fetchExecutionLogAudits(insertedExecutionLog.getId());
        assertThat(response.size()).isEqualTo(1);
        assertThat(response.getFirst().getOperationType()).isEqualTo(CREATE);
        assertThat(response.getFirst().getAuditedEntity()).isEqualTo(EXECUTION_LOG);
    }

    @Test
    public void fetch_executionLogAudit_shouldBeUnAuthorized() {
        anonymousUser();
        executionLogAuditRobot.fetchAuditFails(1234L)
                .statusCode(HttpStatus.UNAUTHORIZED.value());
    }
}
