package pt.jumia.services.easytaxscan.endpoints;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.Permissions;
import pt.jumia.services.easytaxscan.domain.Profiles;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.repository.*;
import pt.jumia.services.easytaxscan.network.acl.AclNetworkRequester;
import pt.jumia.services.easytaxscan.robots.*;
import pt.jumia.services.easytaxscan.robots.audit.*;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles({Profiles.FAKE_CLIENTS, Profiles.TEST})
public abstract class BaseEndpointTest {

    private final Stack<Long> tagsToDelete = new Stack<>();
    private final Stack<Long> dataSourceToDelete = new Stack<>();
    private final Stack<Long> queryToDelete = new Stack<>();
    private final Stack<Long> subQueryToDelete = new Stack<>();
    private final Stack<Long> scanToDelete = new Stack<>();
    private final Stack<Long> executionLogToDelete = new Stack<>();
    private final Stack<Long> webhooksToDelete = new Stack<>();
    private final Stack<Long> documentToDelete = new Stack<>();
    private final Stack<Long> countryToDelete = new Stack<>();


    @LocalServerPort
    protected int port;
    protected SpringActuatorRobot springActuatorRobot;
    protected TagsRobot tagsRobot;
    protected DataSourceRobot dataSourceRobot;
    protected QueryRobot queryRobot;
    protected ScanRobot scanRobot;
    protected ExecutionLogRobot executionLogRobot;
    protected WebhooksRobot webhooksRobot;
    protected DocumentRobot documentRobot;
    protected CountryRobot countryRobot;
    protected DataSourceAuditRobot dataSourceAuditRobot;
    protected QueryAuditRobot queryAuditRobot;
    protected ScanAuditRobot scanAuditRobot;
    protected ExecutionLogAuditRobot executionLogAuditRobot;
    protected DocumentAuditRobot documentAuditRobot;
    protected WebhooksAuditRobot webhooksAuditRobot;


    @Autowired
    protected QueryRepository queryRepository;

    @Autowired
    protected SubQueryRepository subQueryRepository;

    @Autowired
    protected ExecutionLogRepository executionLogRepository;

    @Autowired
    protected WebhooksRepository webhooksRepository;

    @Autowired
    protected DocumentRepository documentRepository;

    @Autowired
    protected DataSourceRepository dataSourceRepository;

    @Autowired
    protected CountryRepository countryRepository;

    @MockitoBean
    private AclNetworkRequester aclNetworkRequester;

    @BeforeEach
    public void baseSetUp() {
        loginUser("<EMAIL>");
        springActuatorRobot = new SpringActuatorRobot(port);
        tagsRobot = new TagsRobot(port);
        countryRobot = new CountryRobot(port);
        dataSourceRobot = new DataSourceRobot(port);
        queryRobot = new QueryRobot(port);
        scanRobot = new ScanRobot(port);
        executionLogRobot = new ExecutionLogRobot(port);
        documentRobot = new DocumentRobot(port);
        webhooksRobot = new WebhooksRobot(port);
        queryAuditRobot = new QueryAuditRobot(port);
        dataSourceAuditRobot = new DataSourceAuditRobot(port);
        scanAuditRobot = new ScanAuditRobot(port);
        executionLogAuditRobot = new ExecutionLogAuditRobot(port);
        documentAuditRobot = new DocumentAuditRobot(port);
        webhooksAuditRobot=new WebhooksAuditRobot(port);
    }

    @AfterEach
    public void baseTearDown() {
        loginUser("<EMAIL>");
        while (!tagsToDelete.empty()) {
            tagsRobot.delete(tagsToDelete.pop());
        }

        while (!webhooksToDelete.empty()) {
            webhooksRepository.deleteById(webhooksToDelete.pop());

        }
        while (!documentToDelete.empty()) {
            documentRepository.deleteById(documentToDelete.pop());
        }

        while (!executionLogToDelete.empty()) {
            executionLogRepository.deleteById(executionLogToDelete.pop());
        }

        while (!scanToDelete.empty()) {
            scanRobot.delete(scanToDelete.pop());
        }
        while (!countryToDelete.empty()) {
            countryRepository.deleteById(countryToDelete.pop());
        }


        while (!subQueryToDelete.empty()) {
            queryRepository.deleteById(queryToDelete.pop());
        }
        while (!queryToDelete.empty()) {
            queryRepository.deleteById(queryToDelete.pop());
        }
        while (!dataSourceToDelete.empty()) {
            dataSourceRobot.delete(dataSourceToDelete.pop());
        }


    }

    protected Tag createTag(Tag tag) {
        Tag createdTag = tagsRobot.create(tag);
        tagsToDelete.push(createdTag.getId());
        return createdTag;
    }

    protected DataSource createDataSource(DataSource dataSource) {
        DataSource createdDataSource = dataSourceRobot.create(dataSource);
        dataSourceToDelete.push(createdDataSource.getId());
        return createdDataSource;
    }

    protected Scan createScan(Scan scan) {
        Scan createdScan = scanRobot.create(scan);
        scanToDelete.push(createdScan.getId());
        return createdScan;
    }

    protected Query createQuery(Query query) {
        Query queryCreate = queryRobot.create(query);
        queryToDelete.push(queryCreate.getId());
        return queryCreate;
    }

    protected ExecutionLog createExecutionLog(ExecutionLog executionLog) {
        ExecutionLog executionLog1 = executionLogRobot.create(executionLog);
        executionLogToDelete.push(executionLog1.getId());
        return executionLog1;
    }

    protected WebHooks createWebhooks(WebHooks webHooks) {
        WebHooks webHooks1 = webhooksRobot.create(webHooks);
        webhooksToDelete.push(webHooks1.getId());
        return webHooks1;
    }

    protected Document createDocument(Document document) {
        Document createdDocument = documentRobot.create(document);
        documentToDelete.push(createdDocument.getId());
        return createdDocument;
    }

    protected Country createCountry(Country country) {
        Country createdCountry = countryRobot.create(country);
        countryToDelete.push(createdCountry.getId());
        return createdCountry;
    }

    protected void loginUser(String username) {
        mockJWT(username);
    }

    protected void anonymousUser() {
        mockJWT(null);
    }

    private void mockJWT(String username) {
        if (username == null) {
            when(aclNetworkRequester.decodeToken(eq(RestApiRobot.TOKEN))).thenThrow(AclErrorException.build(401));
            return;
        }

        Date expirationTime = new Date(new Date().getTime() + TimeUnit.HOURS.toMillis(12));
        RequestUser requestUser = RequestUser.builder()
                .exp(expirationTime.getTime())
                .username(username)
                .build();

        when(aclNetworkRequester.hasPermission(any(), any())).thenReturn(true);

        // Now after the login we need to verify if the user has at least one permission
        Map<String, Map<String, List<String>>> map = new HashMap<>();
        Map<String, List<String>> value = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add(Permissions.DATASOURCE_MANAGE);
        list.add(Permissions.DATASOURCE_ACCESS);
        list.add(Permissions.QUERY_MANAGE);
        list.add(Permissions.QUERY_ACCESS);
        list.add(Permissions.SCAN_MANAGE);
        list.add(Permissions.SCAN_ACCESS);
        list.add(Permissions.EXECUTION_LOG_MANAGE);
        list.add(Permissions.EXECUTION_LOG_ACCESS);
        list.add(Permissions.CAN_MANAGE_WEBHOOKS);
        list.add(Permissions.CAN_ACCESS_WEBHOOKS);
        list.add(Permissions.DOCUMENT_ACCESS);
        list.add(Permissions.COUNTRY_MANAGE);
        list.add(Permissions.COUNTRY_ACCESS);


        value.put("EasyTax-Scan", list);
        map.put("APPLICATION", value);

        when(aclNetworkRequester.getPermissions(any())).thenReturn(map);
        when(aclNetworkRequester.decodeToken(eq(RestApiRobot.TOKEN))).thenAnswer(invocation -> requestUser);
        when(aclNetworkRequester.authorize(username, "password")).thenAnswer(invocation -> requestUser);

        //TODO mock the permissions you want
    }
}
