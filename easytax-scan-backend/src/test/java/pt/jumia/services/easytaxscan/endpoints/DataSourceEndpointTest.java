package pt.jumia.services.easytaxscan.endpoints;

import io.restassured.response.ValidatableResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.HealthCheckParams;
import pt.jumia.services.easytaxscan.domain.entities.HealthStatus;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceSortFilters;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;


/**
 * End-to-End test of the DataSource API, performing all CRUD operations and making sure that data is correctly updated
 */
public class DataSourceEndpointTest extends BaseEndpointTest {


    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void insert_DataSource_shouldBeSuccess() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = dataSourceRobot.create(dataSource);
        assertThat(dataSourceCreated.getId()).isNotNull();
        assertThat(FakeDataSource.DATA_SOURCE_CREATE.getCode()).isEqualTo(dataSourceCreated.getCode());
        dataSourceRobot.delete(dataSourceCreated.getId());
    }

    @Test
    public void insertAndFind_dataSource_shouldBeSuccess() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        DataSource dataSourceFetched = dataSourceRobot.readOneDataSource(dataSourceCreated.getId());
        assertThat(dataSourceFetched.getId()).isNotNull();
        assertThat(FakeDataSource.DATA_SOURCE_CREATE.getDescription()).isEqualTo(dataSourceFetched.getDescription());
    }

    @Test
    public void insertAndFind_dataSource_Failure() {
        dataSourceRobot.readOneDataSourceFails(99999L)
                .statusCode(HttpStatus.NOT_FOUND.value());
    }

    @Test
    public void insert_dataSourceDuplicate_shouldFail() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_FAIL;
        createDataSource(dataSource);
        dataSourceRobot.createFails(dataSource)
                .statusCode(HttpStatus.CONFLICT.value());

    }

    @Test
    public void update_dataSource_shouldBeSuccess() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        dataSourceCreated.setDescription("DataSource has been modified");
        dataSourceCreated.setStatus(DataSource.Status.INACTIVE);
        DataSource updatedDataSource = dataSourceRobot.update(dataSourceCreated.getId(), dataSourceCreated);
        assertThat(DataSource.Status.INACTIVE).isEqualTo(updatedDataSource.getStatus());
        assertThat("DataSource has been modified").isEqualTo(updatedDataSource.getDescription());
    }

    @Test
    public void update_dataSource_failure() {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        dataSourceCreated.setDescription("DataSource has been modified");
        dataSourceCreated.setStatus(null);
        dataSourceRobot.updateFails(dataSourceCreated.getId(), dataSourceCreated)
                .statusCode(HttpStatus.BAD_REQUEST.value());
    }

    @Test
    public void findAll_DataSource_success_withFilters() {
        populateFindAllFilterData();
        DataSourceFilters filters = DataSourceFilters.builder().
                status(DataSource.Status.ACTIVE)
                .text("DataSource").build();
        DataSourceSortFilters sortFilters = DataSourceSortFilters.builder()
                .direction(OrderDirection.ASC).build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<DataSource> dataSourcesList = dataSourceRobot.fetchAllTransactions(filters, sortFilters, pageFilters);
        assertNotNull(dataSourcesList);
        assertEquals(3, dataSourcesList.size());
        assertEquals(DataSource.Status.ACTIVE, dataSourcesList.stream().findAny().get().getStatus());
        assertThat(dataSourcesList).isNotEmpty()
                .anyMatch(dataSource -> dataSource.getDescription().contains("DataSource"));
    }

    @Test
    public void findAll_DataSource_noResult_withInvalidFilters() {
        populateFindAllFilterData();
        DataSourceFilters filters = DataSourceFilters.builder().
                status(DataSource.Status.INACTIVE)
                .text("InvalidData").build();
        DataSourceSortFilters sortFilters = DataSourceSortFilters.builder()
                .direction(OrderDirection.ASC).build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<DataSource> dataSourcesList = dataSourceRobot.fetchAllTransactions(filters, sortFilters, pageFilters);
        assertEquals(0, dataSourcesList.size());
    }

    private void populateFindAllFilterData() {
        List<DataSource> dataSources = List.of(
                FakeDataSource.DATA_SOURCE_FILTER_DATA2, FakeDataSource.DATA_SOURCE_FILTER_DATA3,
                FakeDataSource.DATA_SOURCE_FILTER_DATA4, FakeDataSource.DATA_SOURCE_FILTER_DATA6,
                FakeDataSource.DATA_SOURCE_FILTER_DATA7
        );
        dataSources.forEach(this::createDataSource);
    }

    @Test
    public void dataSource_healthCheck_success() {
        HealthCheckParams healthCheckParams =
                HealthCheckParams.builder()
                        .countryCode("EG")
                        .build();
        DataSource dataSource = createDataSource(FakeDataSource.DATA_SOURCE_HEALTHCHECK);
        createCountry(FakeCountry.COUNTRY_EG);
        HealthStatus healthStatus = dataSourceRobot.healthCheck(healthCheckParams,
                dataSource.getCode());
        assertTrue(healthStatus.isSuccess());
    }

    @Test
    public void dataSource_healthCheck_failure() {
        HealthCheckParams healthCheckParams =
                HealthCheckParams.builder()
                        .countryCode("EG")
                        .build();
        ValidatableResponse nav = dataSourceRobot.healthCheckFail(healthCheckParams,
                "NAV");
        nav.assertThat().statusCode(HttpStatus.NOT_FOUND.value());

    }


}

