package pt.jumia.services.easytaxscan.robots;

import io.restassured.response.Response;
import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.api.payloads.request.WebhookApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.WebhookApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.filter.WebhooksSortFilters;

import java.util.List;
import java.util.stream.Collectors;

public class WebhooksRobot extends RestApiRobot {

    public static final String CREATE_WEBHOOKS_PATH = "/api/documents/webhooks";
    public static final String BASE_WEBHOOKS_PATH = "/api/documents/";
    public static final String WEBHOOKS = "/webhooks";
    public static final String CONTENT_TYPE = "application/json";

    public WebhooksRobot(int port) {
        super(port);
    }

    public WebHooks create(WebHooks webhooks) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new WebhookApiRequestPayload(webhooks))
                .when().post(CREATE_WEBHOOKS_PATH)
                .then().statusCode(HttpStatus.CREATED.value())
                .extract().as(WebhookApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse createFails(WebHooks webHooks) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new WebhookApiRequestPayload(webHooks))
                .when().post(CREATE_WEBHOOKS_PATH)
                .then();
    }

    public WebHooks readOneQuery(Long id) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(BASE_WEBHOOKS_PATH + id + WEBHOOKS)
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(WebhookApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse readOneQueryFails(Long id) {
        return givenAuth()
                .when().get(BASE_WEBHOOKS_PATH + id + WEBHOOKS).then();
    }

    public Response readByIdFails(Long id) {
        return givenAuth()
                .when().get(BASE_WEBHOOKS_PATH + id + WEBHOOKS);
    }

    public ValidatableResponse readAllFails() {
        return givenAuth()
                .when().get(BASE_WEBHOOKS_PATH + "webhooks")
                .then();
    }

    public List<WebHooks> fetchAll(
            WebhooksSortFilters sortingFilters,
            PageFilters pageFilters
    ) {
        RequestSpecification request = givenAuth();
        if (sortingFilters != null) {
            request.queryParams(sortingFilters.getAsMap());
        }
        if (pageFilters != null) {
            request.queryParams(pageFilters.getAsMap());
        }
        return request
                .when().get(BASE_WEBHOOKS_PATH + "webhooks")
                .then().statusCode(HttpStatus.OK.value())
                .extract().jsonPath()
                .getList("results", WebhookApiResponsePayload.class)
                .stream()
                .map(WebhookApiResponsePayload::toEntity)
                .collect(Collectors.toList());
    }


}
