package pt.jumia.services.easytaxscan.robots;

import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.api.payloads.request.QueryApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.QueryApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.SubQueryApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.QueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.QuerySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class QueryRobot extends RestApiRobot {

    public static final String QUERY_BASE_PATH = "/api/queries";
    public static final String CONTENT_TYPE = "application/json";

    public QueryRobot(int port) {
        super(port);
    }

    public Query create(Query query) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new QueryApiRequestPayload(query))
                .when().post(QUERY_BASE_PATH)
                .then().statusCode(HttpStatus.CREATED.value())
                .extract().as(QueryApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse createFails(Query query) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new QueryApiRequestPayload(query))
                .when().post(QUERY_BASE_PATH)
                .then();
    }

    public Query readOneQuery(Long id) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(QUERY_BASE_PATH + "/" + id)
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(QueryApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse readOneQueryFails(Long id) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(QUERY_BASE_PATH + "/" + id)
                .then();
    }

    public Query update(Long id, Query query) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new QueryApiRequestPayload(query))
                .when().put(QUERY_BASE_PATH + "/" + id)
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(QueryApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse updateFails(Long id, Query query) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new QueryApiRequestPayload(query))
                .when().put(QUERY_BASE_PATH + "/" + id)
                .then();
    }

    public List<Query> fetchAllQueries(
            QueryFilters queryFilters,
            QuerySortFilters sortingFilters,
            PageFilters pageFilters
    ) {
        RequestSpecification request = givenAuth();
        if (queryFilters != null) {
            buildQueryParams(queryFilters, request);
        }
        if (sortingFilters != null) {
            request.queryParams(sortingFilters.getAsMap());
        }
        if (pageFilters != null) {
            request.queryParams(pageFilters.getAsMap());
        }
        return request
                .when().get(QUERY_BASE_PATH)
                .then().statusCode(HttpStatus.OK.value())
                .extract().jsonPath()
                .getList("results", QueryApiResponsePayload.class)
                .stream()
                .map(QueryApiResponsePayload::toEntity)
                .collect(Collectors.toList());
    }

    private void buildQueryParams(SubQueryFilters queryFilters, RequestSpecification request) {
        if (queryFilters.getText() != null) {
            request.param("text", queryFilters.getText());
        }

    }

    public List<SubQuery> fetchAllSubQueryById(
            Long queryId, SubQueryFilters filters,
            SubQuerySortFilters sortingFilters,
            PageFilters pageFilters
    ) {
        RequestSpecification request = givenAuth();
        if (filters != null) {
            buildQueryParams(filters, request);
        }
        if (sortingFilters != null) {
            request.queryParams(sortingFilters.getAsMap());
        }
        if (pageFilters != null) {
            request.queryParams(pageFilters.getAsMap());
        }
        return request
                .when().get(QUERY_BASE_PATH + "/" + queryId + "/sub-queries")
                .then().statusCode(HttpStatus.OK.value())
                .extract().jsonPath()
                .getList("results", SubQueryApiResponsePayload.class)
                .stream()
                .map(SubQueryApiResponsePayload::toEntity)
                .collect(Collectors.toList());
    }

    private void buildQueryParams(QueryFilters queryFilters, RequestSpecification request) {
        if (queryFilters.getText() != null) {
            request.param("ACTIVE", queryFilters.getText());
        }

    }

    public List fetchFieldsFromQuery(Long queryId) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(QUERY_BASE_PATH + "/" + queryId+"/placeholders")
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(ArrayList.class);
    }

    public ValidatableResponse fetchFieldsFromQueryFails(Long queryId) {
        return givenAuth()
                .when()
                .get(QUERY_BASE_PATH + "/" + queryId+"/placeholders")
                .then();
    }

}
