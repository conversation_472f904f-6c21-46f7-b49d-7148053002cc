package pt.jumia.services.easytaxscan.robots;

import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.api.payloads.request.DocumentApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.DocumentApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentSortFilters;

import java.util.List;
import java.util.stream.Collectors;

public class DocumentRobot extends RestApiRobot {

    public static final String DOCUMENT_PATH = "/api/documents";
    public static final String CONTENT_TYPE = "application/json";

    public DocumentRobot(int port) {
        super(port);
    }

    public Document create(Document document) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new DocumentApiRequestPayload(document))
                .when().post(DOCUMENT_PATH)
                .then().statusCode(HttpStatus.CREATED.value())
                .extract().as(DocumentApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse createFails(Document document) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new DocumentApiRequestPayload(document))
                .when().post(DOCUMENT_PATH)
                .then();
    }


    public List<Document> fetchAllDocuments(
            DocumentFilters filters,
            DocumentSortFilters sortingFilters,
            PageFilters pageFilters
    ) {
        RequestSpecification request = givenAuth();
        if (filters != null) {
            buildQueryParams(filters, request);
        }
        if (sortingFilters != null) {
            request.queryParams(sortingFilters.getAsMap());
        }
        if (pageFilters != null) {
            request.queryParams(pageFilters.getAsMap());
        }
        return request
                .when().get(DOCUMENT_PATH)
                .then().statusCode(HttpStatus.OK.value())
                .extract().jsonPath()
                .getList("results", DocumentApiResponsePayload.class)
                .stream()
                .map(DocumentApiResponsePayload::toEntity)
                .collect(Collectors.toList());
    }

    private void buildQueryParams(DocumentFilters documentFilters, RequestSpecification request) {
        if (documentFilters.getStatusList() != null) {
            request.param(Document.Status.ACTIVE.name(), documentFilters.getStatusList());
        }

    }


}
