package pt.jumia.services.easytaxscan.endpoints;

import io.restassured.response.Response;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.*;
import pt.jumia.services.easytaxscan.domain.entities.filter.WebhooksSortFilters;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * End-to-End test of the Query API, ensuring all operations work correctly (create, read, update, delete).
 */
public class WebhooksEndpointTest extends BaseEndpointTest {


    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void insert_success() {
        Document documentCreate = insertWebhooksData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        WebHooks webHooks = FakeWebhook.WEBHOOK;
        webHooks.setDocument(documentCreate);
        WebHooks webHooks1 = createWebhooks(webHooks);
        assertThat(webHooks.getId()).isNotNull();
        assertThat(FakeWebhook.WEBHOOK.getPayload()).isEqualTo(webHooks.getPayload());
    }
    @Test
    public void findById_success() {
        Document documentCreate = insertWebhooksData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        WebHooks webHooks = FakeWebhook.WEBHOOK;
        webHooks.setDocument(documentCreate);
        WebHooks webHooks1 = createWebhooks(webHooks);
        WebHooks webHooks2 = webhooksRobot.readOneQuery(webHooks1.getId());
        assertThat(webHooks2.getId()).isEqualTo(webHooks1.getId());
        assertThat(webHooks2.getPayload()).isEqualTo(webHooks1.getPayload());
    }
    @Test
    public void findAll_success() {
        Document documentCreate = insertWebhooksData(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        WebHooks webHooks = FakeWebhook.WEBHOOK;
        webHooks.setDocument(documentCreate);
        WebHooks webHooks1 = createWebhooks(webHooks);
       List<WebHooks> webHooksList = webhooksRobot.fetchAll(WebhooksSortFilters.builder().build(), PageFilters.builder().build());
        assertThat(webHooksList.get(0).getId()).isEqualTo(webHooks1.getId());
        assertThat(webHooksList.get(0).getPayload()).isEqualTo(webHooks1.getPayload());
    }
    @Test
    public void findById_notFound() {
        Response validatableResponse = webhooksRobot.readByIdFails(1L);
        assertEquals(404, validatableResponse.getStatusCode());
        String responseBody = validatableResponse.getBody().asString();
        assertTrue(responseBody.contains("ENTITY_NOT_FOUND"));
    }
    @Test
    public void testUnauthorized() {
       WebHooks webHooks = FakeWebhook.WEBHOOK;
        anonymousUser();
        webhooksRobot.readAllFails()
                .statusCode(HttpStatus.UNAUTHORIZED.value());
        webhooksRobot.readOneQueryFails(webHooks.getId())
                .statusCode(HttpStatus.UNAUTHORIZED.value());
        webhooksRobot.createFails(webHooks)
                .statusCode(HttpStatus.UNAUTHORIZED.value());
    }


    private Document insertWebhooksData(Scan scan, Query query, DataSource dataSource, ExecutionLog executionLog, Document document) {
        DataSource createdDataSource = createDataSource(dataSource);
        query.setDataSource(createdDataSource);
        Query insertQuery = createQuery(query);
        scan.setQuery(insertQuery);
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> createCountry(FakeCountry.COUNTRY_EG));
        scan.setCountry(country);
        Scan scanCreated = createScan(scan);
        executionLog.setScan(scanCreated);
        ExecutionLog executionLog1 = createExecutionLog(executionLog);
        document.setExecutionLog(executionLog1);
        return createDocument(document);
    }
}
