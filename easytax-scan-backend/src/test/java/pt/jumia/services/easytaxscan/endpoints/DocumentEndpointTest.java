package pt.jumia.services.easytaxscan.endpoints;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.*;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentSortFilters;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;


public class DocumentEndpointTest extends BaseEndpointTest {


    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }


    @Test
    public void insert_document() {
        Document document = insertDocument(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        assertThat(document.getId()).isNotNull();
        assertThat(FakeDocument.DOCUMENT_CREATE.getQueryData()).isEqualTo(document.getQueryData());
    }


    @Test
    public void fetchAll_document() {
        insertDocument(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE,
                FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        insertDocument(FakeScan.SCAN_UPDATE, FakeQuery.QUERY_UPDATE, FakeDataSource.DATA_SOURCE_UPDATE,
                FakeExecutionLog.EXECUTION_LOG_UPDATE, FakeDocument.DOCUMENT_UPDATE);
        var documentList = documentRobot.fetchAllDocuments(null, null, null);
        assertNotNull(documentList);
        assertThat(documentList.size()).isGreaterThanOrEqualTo(2);
    }


    @Test
    public void findAll_Document_success_withFilters() {

        insertDocument(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE, FakeDataSource.DATA_SOURCE_CREATE,
                FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        insertDocument(FakeScan.SCAN_UPDATE, FakeQuery.QUERY_UPDATE, FakeDataSource.DATA_SOURCE_UPDATE,
                FakeExecutionLog.EXECUTION_LOG_UPDATE, FakeDocument.DOCUMENT_UPDATE);

        DocumentFilters filters = DocumentFilters.builder()
                .statusList(List.of(Document.Status.ACTIVE.name()))
                .build();
        DocumentSortFilters sortFilters = DocumentSortFilters.builder()
                .direction(OrderDirection.ASC)
                .build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<Document> documents = documentRobot.fetchAllDocuments(filters, sortFilters, pageFilters);
        assertNotNull(documents);
        assertEquals(2, documents.size());
    }

    @Test
    public void fetchAll_document_fails_whenNoDocumentsExist() {
        var documentList = documentRobot.fetchAllDocuments(null, null, null);

        assertNotNull(documentList);
        assertThat(documentList).isEmpty();
    }


    private Document insertDocument(Scan scan, Query query, DataSource dataSource, ExecutionLog executionLog, Document document) {
        DataSource createdDataSource = createDataSource(dataSource);
        query.setDataSource(createdDataSource);
        Query insertQuery = createQuery(query);
        scan.setQuery(insertQuery);
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> createCountry(FakeCountry.COUNTRY_EG));
        scan.setCountry(country);
        Scan scanCreated = createScan(scan);
        executionLog.setScan(scanCreated);
        ExecutionLog executionLog1 = createExecutionLog(executionLog);
        document.setExecutionLog(executionLog1);
        return createDocument(document);
    }
}
