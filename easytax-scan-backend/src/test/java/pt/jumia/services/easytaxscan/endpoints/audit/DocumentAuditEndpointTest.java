package pt.jumia.services.easytaxscan.endpoints.audit;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.*;
import pt.jumia.services.easytaxscan.endpoints.BaseEndpointTest;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static pt.jumia.services.easytaxscan.domain.entities.AuditedEntities.DOCUMENT;
import static pt.jumia.services.easytaxscan.domain.entities.AuditedEntities.QUERY;
import static pt.jumia.services.easytaxscan.domain.entities.AuditedEntity.OperationType.CREATE;

/**
 * End-to-End test of the DocumentAudit API
 */
public class DocumentAuditEndpointTest extends BaseEndpointTest {


    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void fetch_documentAudits_shouldBeSuccess() throws JsonProcessingException {
        Document document = insertDocument(FakeScan.SCAN_CREATE, FakeQuery.QUERY_CREATE,
                FakeDataSource.DATA_SOURCE_CREATE, FakeExecutionLog.EXECUTION_LOG_CREATE, FakeDocument.DOCUMENT_CREATE);
        assertThat(document.getId()).isNotNull();
        List<AuditedEntity> response =
                documentAuditRobot.fetchDocumentAudits(document.getId());
        assertThat(response.size()).isNotNull();
    }


    @Test
    public void fetch_documentAudit_shouldBeUnAuthorized() {
        anonymousUser();
        documentAuditRobot.fetchAuditFails(1234L)
                .statusCode(HttpStatus.UNAUTHORIZED.value());
    }
    private Document insertDocument(Scan scan, Query query, DataSource dataSource, ExecutionLog executionLog, Document document) {
        DataSource createdDataSource = createDataSource(dataSource);
        query.setDataSource(createdDataSource);
        Query insertQuery = createQuery(query);
        scan.setQuery(insertQuery);
        Country country = countryRepository.findByCode(FakeCountry.COUNTRY_EG.getCountryCode())
                .orElseGet(() -> createCountry(FakeCountry.COUNTRY_EG));
        scan.setCountry(country);
        Scan scanCreated = createScan(scan);
        executionLog.setScan(scanCreated);
        ExecutionLog executionLog1 = createExecutionLog(executionLog);
        document.setExecutionLog(executionLog1);
        return createDocument(document);
    }
}
