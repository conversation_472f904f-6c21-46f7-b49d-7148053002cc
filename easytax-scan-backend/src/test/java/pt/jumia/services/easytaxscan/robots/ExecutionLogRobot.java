package pt.jumia.services.easytaxscan.robots;

import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.api.payloads.request.ExecutionLogApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.ExecutionLogApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogSortFilters;

import java.util.List;
import java.util.stream.Collectors;

public class ExecutionLogRobot extends RestApiRobot {

    public static final String EXECUTION_LOG_BASE_PATH = "/api/execution-logs";
    public static final String CONTENT_TYPE = "application/json";

    public ExecutionLogRobot(int port) {
        super(port);
    }

    public ExecutionLog create(ExecutionLog executionLog) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new ExecutionLogApiRequestPayload(executionLog))
                .when().post(EXECUTION_LOG_BASE_PATH)
                .then().statusCode(HttpStatus.CREATED.value())
                .extract().as(ExecutionLogApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse createFails(ExecutionLog executionLog) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new ExecutionLogApiRequestPayload(executionLog))
                .when().post(EXECUTION_LOG_BASE_PATH)
                .then();
    }

    public ExecutionLog readOneQuery(Long id) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(EXECUTION_LOG_BASE_PATH + "/" + id)
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(ExecutionLogApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse readOneQueryFails(Long id) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(EXECUTION_LOG_BASE_PATH + "/" + id)
                .then();
    }
    public ValidatableResponse readAllFails() {
        return givenAuth()
                .when().get(EXECUTION_LOG_BASE_PATH)
                .then();
    }

    public List<ExecutionLog> fetchAllExecutionLogs(
            ExecutionLogFilters filters,
            ExecutionLogSortFilters sortingFilters,
            PageFilters pageFilters
    ) {
        RequestSpecification request = givenAuth();
        if (filters != null) {
            buildQueryParams(filters, request);
        }
        if (sortingFilters != null) {
            request.queryParams(sortingFilters.getAsMap());
        }
        if (pageFilters != null) {
            request.queryParams(pageFilters.getAsMap());
        }
        return request
                .when().get(EXECUTION_LOG_BASE_PATH)
                .then().statusCode(HttpStatus.OK.value())
                .extract().jsonPath()
                .getList("results", ExecutionLogApiResponsePayload.class)
                .stream()
                .map(ExecutionLogApiResponsePayload::toEntity)
                .collect(Collectors.toList());
    }

    private void buildQueryParams(ExecutionLogFilters executionLogFilters, RequestSpecification request) {
        if (executionLogFilters.getStatus() != null) {
            request.param("ACTIVE", executionLogFilters.getStatus());
        }

    }


}
