package pt.jumia.services.easytaxscan.robots;

import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.api.payloads.request.DataSourceApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.HealthCheckParamsApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.DataSourceApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.HealthCheckParams;
import pt.jumia.services.easytaxscan.domain.entities.HealthStatus;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceSortFilters;

import java.util.List;
import java.util.stream.Collectors;

public class DataSourceRobot extends RestApiRobot {

    public static final String DATASOURCE_BASE_PATH = "/api/datasources";
    public static final String CONTENT_TYPE = "application/json";

    public DataSourceRobot(int port) {
        super(port);
    }

    public DataSource create(DataSource dataSource) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new DataSourceApiRequestPayload(dataSource))
                .when().post(DATASOURCE_BASE_PATH)
                .then().statusCode(HttpStatus.CREATED.value())
                .extract().as(DataSourceApiResponsePayload.class).toEntity();
    }

    public HealthStatus healthCheck(HealthCheckParams healthCheckParams, String code) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new HealthCheckParamsApiRequestPayload(healthCheckParams))
                .when()
                .post(DATASOURCE_BASE_PATH + "/" + code + "/health-check")
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(HealthStatus.class);
    }
    public ValidatableResponse healthCheckFail(HealthCheckParams healthCheckParams, String code) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new HealthCheckParamsApiRequestPayload(healthCheckParams))
                .when()
                .post(DATASOURCE_BASE_PATH + "/" + code + "/health-check")
                .then();
    }

    public ValidatableResponse createFails(DataSource dataSource) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new DataSourceApiRequestPayload((dataSource)))
                .when().post(DATASOURCE_BASE_PATH)
                .then();
    }

    public DataSource readOneDataSource(Long id) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(DATASOURCE_BASE_PATH + "/" + id)
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(DataSourceApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse readOneDataSourceFails(Long id) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(DATASOURCE_BASE_PATH + "/" + id)
                .then();
    }

    public DataSource update(Long id, DataSource dataSource) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new DataSourceApiRequestPayload((dataSource)))
                .when().put(DATASOURCE_BASE_PATH + "/" + id)
                .then().statusCode(200)
                .extract().as(DataSourceApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse updateFails(Long id, DataSource dataSource) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .body(new DataSourceApiRequestPayload(dataSource))
                .when().put(DATASOURCE_BASE_PATH + "/" + id)
                .then();
    }


    public List<DataSource> fetchAllTransactions(
            DataSourceFilters dataSourceFilters,
            DataSourceSortFilters sortingFilters,
            PageFilters pageFilters
    ) {
        RequestSpecification request = givenAuth();
        if (dataSourceFilters != null) {
            buildQueryParams(dataSourceFilters, request);
        }
        if (sortingFilters != null) {
            request.queryParams(sortingFilters.getAsMap());
        }
        if (pageFilters != null) {
            request.queryParams(pageFilters.getAsMap());
        }
        return request
                .when().get(DATASOURCE_BASE_PATH)
                .then().statusCode(HttpStatus.OK.value())
                .extract().jsonPath()
                .getList("results", DataSourceApiResponsePayload.class)
                .stream()
                .map(DataSourceApiResponsePayload::toEntity)
                .collect(Collectors.toList());
    }

    private void buildQueryParams(DataSourceFilters dataSourceFilters, RequestSpecification request) {
        if (dataSourceFilters.getText() != null) {
            request.param("text", dataSourceFilters.getText());
        }
        if (dataSourceFilters.getStatus() != null) {
            request.param("status", dataSourceFilters.getStatus());
        }
    }

    public ValidatableResponse readAllFails() {
        return givenAuth()
                .when().get(DATASOURCE_BASE_PATH)
                .then();
    }

    public void delete(Long id) {
        givenAuth()
                .when().delete(DATASOURCE_BASE_PATH + "/" + id)
                .then().statusCode(200);
    }

}
