package pt.jumia.services.easytaxscan.robots.audit;

import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.api.payloads.response.audit.AuditApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.robots.RestApiRobot;

import java.util.List;
import java.util.stream.Collectors;

public class ExecutionLogAuditRobot extends RestApiRobot {

    public static final String EXECUTION_LOG_AUDIT_PATH = "/api/audit/execution-logs";
    public static final String CONTENT_TYPE = "application/json";

    public ExecutionLogAuditRobot(int port) {
        super(port);
    }

    public List<AuditedEntity> fetchExecutionLogAudits(Long executionLogId) {
        RequestSpecification request = givenAuth();
        return request
                .when().get(EXECUTION_LOG_AUDIT_PATH + "/" + executionLogId)
                .then().statusCode(HttpStatus.OK.value())
                .extract().jsonPath()
                .getList("results", AuditApiResponsePayload.class)
                .stream()
                .map(AuditApiResponsePayload::toEntity)
                .collect(Collectors.toList());
    }

    public ValidatableResponse fetchAuditFails(Long executionLogId) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE)
                .when().get(EXECUTION_LOG_AUDIT_PATH + "/" + executionLogId)
                .then();
    }
}
