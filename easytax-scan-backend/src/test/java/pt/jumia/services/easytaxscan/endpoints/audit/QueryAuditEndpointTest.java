package pt.jumia.services.easytaxscan.endpoints.audit;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.endpoints.BaseEndpointTest;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static pt.jumia.services.easytaxscan.domain.entities.AuditedEntities.QUERY;
import static pt.jumia.services.easytaxscan.domain.entities.AuditedEntity.OperationType.CREATE;

/**
 * End-to-End test of the QueryAudit API
 */
public class QueryAuditEndpointTest extends BaseEndpointTest {


    @BeforeEach
    public void setUp() {
        loginUser("<EMAIL>");
    }

    @Test
    public void fetch_queryAudits_shouldBeSuccess() throws JsonProcessingException {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        DataSource dataSourceCreated = createDataSource(dataSource);
        Query query = FakeQuery.QUERY_CREATE;
        query.setDataSource(dataSourceCreated);
        Query insertedQuery = createQuery(query);
        assertThat(insertedQuery.getId()).isNotNull();
        List<AuditedEntity> response =
                queryAuditRobot.fetchQueryAudits(insertedQuery.getId());
        assertThat(response.size()).isEqualTo(1);
        assertThat(response.getFirst().getOperationType()).isEqualTo(CREATE);
        assertThat(response.getFirst().getAuditedEntity()).isEqualTo(QUERY);
    }


    @Test
    public void fetch_queryAudit_shouldBeUnAuthorized() {
        anonymousUser();
        queryAuditRobot.fetchAuditFails(1234L)
                .statusCode(HttpStatus.UNAUTHORIZED.value());
    }

}
