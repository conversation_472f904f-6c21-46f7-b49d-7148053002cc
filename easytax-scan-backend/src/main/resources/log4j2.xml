<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" name="easytaxscan" packages="pt.jumia.services" shutdownHook="enable">
  <Properties>
    <Property name="log-path">/var/log/easytaxscan</Property>
  </Properties>

  <Appenders>
    <Console name="Console" target="SYSTEM_OUT">
      <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%logger{36}] - %msg%n"/>
    </Console>
    <!-- Default -->
    <RollingFile name="Default" fileName="${log-path}/server.log"
      filePattern="${log-path}/server.log-%d{yyyy-MM-dd}.gz">
      <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%logger{36}] - %msg%n"/>
      <Policies>
        <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
      </Policies>
    </RollingFile>
  </Appenders>

  <Loggers>
    <!-- Base Loggers -->
    <Logger name="root" level="INFO" additivity="true">
      <appender-ref ref="Default" level="INFO"/>
      <appender-ref ref="Console" level="INFO"/>
    </Logger>
    <!-- Default -->
    <Logger name="pt.jumia.services.easytaxscan" level="INFO" additivity="false">
      <appender-ref ref="Default" level="INFO"/>
      <appender-ref ref="Console" level="INFO"/>
    </Logger>

    <Logger name="org.flywaydb.core.internal.dbsupport.DbSupportFactory" level="OFF" additivity="false">
      <!-- Remove fly way log that would show the DB username and password -->
      <!-- Can be removed once this is fixed: https://github.com/flyway/flyway/issues/1620 -->
    </Logger>
  </Loggers>

</Configuration>
