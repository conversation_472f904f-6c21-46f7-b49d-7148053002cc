# Uncomment to run the application with the fake implementations (repository, network, etc.)
#spring.profiles.active=fake

info.build.version: 0.1.0
server.port: 8080

spring:
  application.name: EasyTax-Scan
  flyway:
    enabled: false
    locations: db/migration
    schemas: audit, public, quartz

endpoints.default.web.enabled: false

springdoc:
  api-docs:
    version: OPENAPI_3_0


#Spring actuator management configs
management.endpoints.web:
  base-path: / # Force base path to be root for instead of default "/actuator"
  exposure.include: health, prometheus   # Expose only health endpoint

#data
data:
  db:
    driver: org.postgresql.Driver
    url: *******************************************************
    application-schema: public
    audit-schema: audit
    quartz-schema: quartz
    max-pool-size: 15
    flyway:
      repair: false
  events:
    check-connection-timeout: 15s
  data-sources:
    cash-rec:
      connection:
        datasource: postgresql
        driver: org.postgresql.Driver
        url: localhost
        port: 15433
        database: cashrec_database
        username: postgres
        password: postgres
        thread-pool-size: 1
    nav:
      connection:
        datasource: sqlserver
        driver: com.microsoft.sqlserver.jdbc.SQLServerDriver
        url: localhost
        port: 1433
        database: nav_database
        username: sa
        password: nav_local_Password_1!
        thread-pool-size: 1
    oms:
      countries:
        EG:
          connection:
            datasource: postgresql
            driver: org.postgresql.Driver
            url: localhost
            port: 15433
            database: cashrec_database
            username: postgres
            password: postgres
            thread-pool-size: 1
        MA:
          connection:
            datasource: sqlserver
            driver: com.microsoft.sqlserver.jdbc.SQLServerDriver
            url: localhost
            port: 1433
            database: nav_database
            username: sa
            password: nav_local_Password_1!
            thread-pool-size: 1



#API
api:
  swagger-enabled: true
  self-host: http://localhost:8080/
  allowed-domains: http://localhost:9000,http://localhost:8080,http://localhost:3000,http://localhost:4200

#Network
network:
  jokes.url: http://api.icndb.com
  kafka:
    clusters:
      cluster-uvr:
        bootstrap-servers: localhost:9092
        #        properties:
        #          security.protocol: SASL_PLAINTEXT
        #          sasl:
        #            mechanism: PLAIN
        #            jaas:
        #              config: org.apache.kafka.common.security.plain.PlainLoginModule required username="username" password="password";
        producer:
          value-serializer: org.apache.kafka.common.serialization.StringSerializer

    streams:
      scan-documents:
        create: true
        auto-start: true
        name: "easytax-scan-document-events"
        group-id: "easytax-scan-document-events"
        partitions: 1

  bill:
    url: https://api-bill-dev-services.jumia.com
#ACL
acl:
  skip: false
  url: http://internal-api-acl-staging.jumia.services
  app-name: EasyTax-Scan
  cache:
    strategy: in-memory
    in-memory:
      expiration-duration: 5m
    redis:
      host: dev-communications.2smgfr.0001.euw1.cache.amazonaws.com
      port: 6379
      expiration-duration: 5m
      timeout: 0s
  migrator-user:
    username: dummy
    password: dummy
