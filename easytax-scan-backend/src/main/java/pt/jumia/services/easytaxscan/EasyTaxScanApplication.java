package pt.jumia.services.easytaxscan;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import pt.jumia.services.easytaxscan.domain.properties.*;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.MessageFormat;

@SpringBootApplication
@Slf4j
@SuppressWarnings("PMD.CloseResource")
public class EasyTaxScanApplication {

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext appContext = SpringApplication.run(EasyTaxScanApplication.class, args);

        SpringProperties springProperties = appContext.getBean(SpringProperties.class);
        ServerProperties serverProperties = appContext.getBean(ServerProperties.class);
        InfoProperties infoProperties = appContext.getBean(InfoProperties.class);
        ApiProperties apiProperties = appContext.getBean(ApiProperties.class);
        SpringdocProperties springdocProperties = appContext.getBean(SpringdocProperties.class);
        String protocol = "http";

        StringBuilder startupInfoBuilder = new StringBuilder();
        startupInfoBuilder.append("\n--------------------------------------------------------------------\n");

        startupInfoBuilder.append(MessageFormat.format(
                "\tApplication {0} v{1} is running! Access URLs and Info:\n",
                springProperties.getApplication().getName(),
                infoProperties.getBuild().getVersion()));

        startupInfoBuilder.append(MessageFormat.format(
                "\tLocal: \t\t\t{0}://localhost:{1}\n",
                protocol, String.valueOf(serverProperties.getPort())));

        startupInfoBuilder.append(MessageFormat.format(
                "\tExternal: \t{0}://{1}:{2}\n",
                protocol,
                InetAddress.getLocalHost().getHostAddress(),
                String.valueOf(serverProperties.getPort())));

        if (springdocProperties.getSwaggerUi().isEnabled()) {
            startupInfoBuilder.append(MessageFormat.format(
                    "\tSwagger: \t\t{0}{1}\n",
                    apiProperties.getSelfHost(),
                    springdocProperties.getSwaggerUi().getPath()));
        }

        String activeProfiles = String.join(", ", appContext.getEnvironment().getActiveProfiles());
        startupInfoBuilder.append(MessageFormat.format(
                "\tProfile(s): {0}\n",
                activeProfiles));

        startupInfoBuilder.append("--------------------------------------------------------------------");
        log.info(startupInfoBuilder.toString());
    }
}
