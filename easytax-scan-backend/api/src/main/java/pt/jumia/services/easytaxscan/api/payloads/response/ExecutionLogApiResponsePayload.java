package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.Mode;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ExecutionLogApiResponsePayload {

    private Long id;

    private ScanApiResponsePayload scan;

    private Integer totalResults;

    private String status;

    private String exception;

    private Long durationMs;

    private String mode;

    private String createdBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;

    private String updatedBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;


    public ExecutionLogApiResponsePayload(ExecutionLog executionLog) {
        this.id = executionLog.getId();
        this.scan = new ScanApiResponsePayload(executionLog.getScan());
        this.totalResults = executionLog.getTotalResults();
        this.exception = executionLog.getException();
        this.status = executionLog.getStatus().name();
        this.durationMs = executionLog.getDurationMs();
        this.mode = executionLog.getMode().name();
        this.createdBy = executionLog.getCreatedBy();
        this.createdAt = executionLog.getCreatedAt();
        this.updatedBy = executionLog.getUpdatedBy();
        this.updatedAt = executionLog.getUpdatedAt();
    }

    public ExecutionLog toEntity() {
        return ExecutionLog.builder()
                .id(id)
                .durationMs(durationMs)
                .totalResults(totalResults)
                .scan(scan.toEntity())
                .status(ExecutionLog.Status.valueOf(status))
                .exception(exception)
                .mode(Mode.valueOf(mode))
                .createdBy(createdBy)
                .createdAt(createdAt)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .build();

    }
}
