package pt.jumia.services.easytaxscan.api.payloads.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.Mode;

@Data
@NoArgsConstructor
public class DocumentApiRequestPayload {

    @NotNull
    private Long executionLogId;

    @NotNull
    @ValidEnumValue(required = true, enumClass = Document.Status.class)
    private String status;

    @ValidEnumValue(required = true, enumClass = Mode.class)
    private String mode;

    @NotEmpty
    private String sid;

    @NotEmpty
    private String queryData;

    @NotEmpty
    private String requestPayload;


    public DocumentApiRequestPayload(Document document) {
        this.executionLogId = document.getExecutionLog().getId();
        this.sid = document.getSid();
        this.status = document.getStatus().name();
        this.mode = document.getMode().name();
        this.requestPayload = document.getRequestPayload();
        this.queryData = document.getQueryData();
    }

    public Document toEntity() {
        return Document.builder()
                .executionLog(
                        executionLogId != null
                                ? ExecutionLog.builder().id(executionLogId).build()
                                : null
                )
                .sid(sid)
                .status(Document.Status.valueOf(status))
                .mode(Mode.valueOf(mode))
                .queryData(queryData)
                .requestPayload(requestPayload)
                .build();

    }
}
