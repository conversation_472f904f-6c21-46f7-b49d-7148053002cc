package pt.jumia.services.easytaxscan.api.controllers;


import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.easytaxscan.api.payloads.request.SettingApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.SettingFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.SettingSortFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.SettingApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.entities.filter.SettingFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SettingSortFilters;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.setting.CreateSettingUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.setting.DeleteSettingUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.setting.ReadSettingUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.setting.UpdateSettingUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

@RestController
@RequestMapping(value = "/api/settings")
@Slf4j
@RequiredArgsConstructor
public class SettingController {

    private final DeleteSettingUseCase deleteSettingUseCase;
    private final UpdateSettingUseCase updateSettingUseCase;
    private final CreateSettingUseCase createSettingUseCase;
    private final ReadSettingUseCase readSettingUseCase;
    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final JsonUtils jsonUtils;

    @GetMapping
    public List<SettingApiResponsePayload> fetch(@Valid SettingFiltersApiRequestPayload settingFiltersApiRequestPayload,
                                                 @Valid SettingSortFiltersApiRequestPayload sortFiltersApiRequestPayload) {
        validateUserAccessUseCase.checkCanAccessSetting(RequestContext.getUser());

        log.info("Fetching all settings for user with identifier {}",
                RequestContext.getUsername());

        SettingFilters settingFilters = settingFiltersApiRequestPayload.toEntity();
        SettingSortFilters settingSortFilters = sortFiltersApiRequestPayload.toEntity();

        return readSettingUseCase.execute(settingFilters, settingSortFilters)
                .stream()
                .map(SettingApiResponsePayload::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/{id}")
    public SettingApiResponsePayload fetchById(@PathVariable("id") long id) {
        validateUserAccessUseCase.checkCanAccessSetting(RequestContext.getUser());

        log.info("Fetching setting {} for user with identifier {}",
                id, RequestContext.getUsername());

        Setting setting = readSettingUseCase.findById(id);
        return new SettingApiResponsePayload(setting);
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public SettingApiResponsePayload create(@RequestBody @Valid SettingApiRequestPayload payload) {
        validateUserAccessUseCase.checkCanManageSetting(RequestContext.getUser());

        log.info("Creating new setting: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());

        Setting setting = createSettingUseCase.execute(payload.toEntity());
        return new SettingApiResponsePayload(setting);
    }

    @PutMapping("/{id}")
    public SettingApiResponsePayload update(@PathVariable("id") long id,
                                            @Valid @RequestBody SettingApiRequestPayload payload) {
        validateUserAccessUseCase.checkCanManageSetting(RequestContext.getUser());

        log.info("Updating setting with id {} : {} for user with identifier {}",
                id, jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());

        Setting setting = updateSettingUseCase.execute(id, payload.toEntity());
        return new SettingApiResponsePayload(setting);
    }

    @DeleteMapping("/{id}")
    public SettingApiResponsePayload delete(@PathVariable("id") long id) {
        validateUserAccessUseCase.checkCanManageSetting(RequestContext.getUser());

        log.info("Delete setting with id {} for user with identifier {}",
                id, RequestContext.getUsername());

        Setting setting = deleteSettingUseCase.execute(id);
        return new SettingApiResponsePayload(setting);
    }
}
