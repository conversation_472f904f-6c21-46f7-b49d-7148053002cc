package pt.jumia.services.easytaxscan.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.Tag;

/**
 * Tag api response payload pojo representation.
 */
@Data
@NoArgsConstructor
public class TagApiResponsePayload {

    private Long id;
    private String name;
    private String description;
    private String color;

    public TagApiResponsePayload(Tag tag) {
        this.id = tag.getId();
        this.name = tag.getName();
        this.description = tag.getDescription();
        this.color = tag.getColor();
    }

    public Tag toEntity() {
        return Tag
            .builder()
            .id(id)
            .name(name)
            .description(description)
            .color(color)
            .build();
    }
}
