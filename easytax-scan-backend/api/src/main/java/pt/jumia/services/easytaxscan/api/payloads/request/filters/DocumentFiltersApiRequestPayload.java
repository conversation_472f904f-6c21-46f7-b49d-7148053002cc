package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentFilters;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class DocumentFiltersApiRequestPayload {

    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdFrom;
    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdTo;
    private String sid;
    private Long executionLogId;
    private List<String> statusList;
    private Long scanId;
    private String mode;


    public DocumentFilters toEntity() {
        return new DocumentFilters().toBuilder()
                .sid(sid)
                .executionLogId(executionLogId)
                .createdFrom(createdFrom)
                .createdTo(createdTo)
                .statusList(this.statusList == null ? null : this.statusList)
                .mode(mode)
                .scanId(scanId)
                .build();
    }
}
