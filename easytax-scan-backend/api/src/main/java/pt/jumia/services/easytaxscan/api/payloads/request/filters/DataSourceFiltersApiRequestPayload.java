package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceFilters;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class DataSourceFiltersApiRequestPayload {

    @Size(max = 1024)
    private String text;

    @ValidEnumValue(required = false, enumClass = DataSource.Status.class)
    private String status;

    public DataSourceFilters toEntity() {
        return new DataSourceFilters().toBuilder()
                .text(text)
                .status(this.status == null ? null : DataSource.Status.valueOf((this.status)))
                .build();
    }
}
