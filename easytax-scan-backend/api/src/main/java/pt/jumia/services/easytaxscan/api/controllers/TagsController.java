package pt.jumia.services.easytaxscan.api.controllers;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.easytaxscan.api.payloads.request.TagApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.TagApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Tag;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.usecases.tags.CreateTagsUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.tags.DeleteTagsUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.tags.ReadTagsUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.tags.UpdateTagsUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

/**
 * Controller responsible for handling CRUD operations for tags.
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/tags")
public class TagsController {

    private final CreateTagsUseCase createTagsUseCase;
    private final ReadTagsUseCase readTagsUseCase;
    private final UpdateTagsUseCase updateTagsUseCase;
    private final DeleteTagsUseCase deleteTagsUseCase;
    private final JsonUtils jsonUtils;

    @GetMapping
    public List<TagApiResponsePayload> fetch() {
        log.info("Fetching all tags for user with identifier {}",
            RequestContext.getUsername());

        List<Tag> tags = readTagsUseCase.execute();

        return tags
            .stream()
            .map(TagApiResponsePayload::new)
            .collect(Collectors.toList());
    }

    @GetMapping(value = "/{id}")
    public TagApiResponsePayload fetchById(@PathVariable(value = "id") Integer id)
        throws NotFoundException {
        log.info("Fetching tag {} for user with identifier {}",
            id, RequestContext.getUsername());

        Tag tag = readTagsUseCase.execute(id);
        return new TagApiResponsePayload(tag);
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public TagApiResponsePayload create(@RequestBody @Valid TagApiRequestPayload payload) {
        log.info("Creating new tag: {} for user with identifier {}",
            jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());

        Tag persistedTag = createTagsUseCase.execute(payload.toEntity());

        return new TagApiResponsePayload(persistedTag);
    }

    @PutMapping(value = "/{id}")
    public void update(@PathVariable(value = "id") Long id, @RequestBody @Valid TagApiRequestPayload payload) throws NotFoundException {
        log.info("Updating tag with id {} : {} for user with identifier {}",
            id, jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());

        updateTagsUseCase.execute(id, payload.toEntity());
    }

    @DeleteMapping(value = "/{id}")
    public void delete(@PathVariable(value = "id") Integer id) throws NotFoundException {
        log.info("Deleting tag with id {} for user with identifier {}",
            id, RequestContext.getUsername());

        deleteTagsUseCase.execute(id);
    }
}
