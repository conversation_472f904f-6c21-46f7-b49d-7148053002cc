package pt.jumia.services.easytaxscan.api.payloads.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.Mode;
import pt.jumia.services.easytaxscan.domain.entities.Scan;

@Data
@NoArgsConstructor
public class ExecutionLogApiRequestPayload {

    private Long scanId;

    private Integer totalResults;

    @ValidEnumValue(required = true, enumClass = ExecutionLog.Status.class)
    private String status;

    private String exception;

    private Long durationMs;

    @ValidEnumValue(required = true, enumClass = Scan.Mode.class)
    private String mode;


    public ExecutionLogApiRequestPayload(ExecutionLog executionLog) {
        this.scanId = executionLog.getScan().getId();
        this.totalResults = executionLog.getTotalResults();
        this.exception = executionLog.getException();
        this.status = executionLog.getStatus().name();
        this.durationMs = executionLog.getDurationMs();
        this.mode = executionLog.getMode().name();
    }

    public ExecutionLog toEntity() {
        return ExecutionLog.builder()
                .scan(Scan.builder().id(scanId).build())
                .durationMs(durationMs)
                .totalResults(totalResults)
                .status(ExecutionLog.Status.valueOf(status))
                .exception(exception)
                .mode(Mode.valueOf(mode))
                .build();

    }
}
