package pt.jumia.services.easytaxscan.api.controllers;


import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.easytaxscan.api.groups.CreateGroup;
import pt.jumia.services.easytaxscan.api.groups.UpdateGroup;
import pt.jumia.services.easytaxscan.api.payloads.request.CountryApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.CountryApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.country.CreateCountryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.country.ReadCountryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.country.UpdateCountryUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

@RestController
@RequestMapping(value = "/api/countries")
@Slf4j
@RequiredArgsConstructor
public class CountryController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final JsonUtils jsonUtils;
    private final CreateCountryUseCase createCountryUseCase;
    private final ReadCountryUseCase readCountryUseCase;
    private final UpdateCountryUseCase updateCountryUseCase;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public CountryApiResponsePayload create
            (@RequestBody @Validated(CreateGroup.class) @Valid CountryApiRequestPayload payload)
            throws UserForbiddenException, NotFoundException {
        validateUserAccessUseCase.checkCanManageCountry(RequestContext.getUser());
        log.info("Creating new country: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        return new CountryApiResponsePayload(createCountryUseCase.execute(payload.toEntity()));
    }


    @GetMapping(value = "/{id}")
    public CountryApiResponsePayload fetchById(@PathVariable(value = "id") Long id)
            throws UserForbiddenException, NotFoundException {
        validateUserAccessUseCase.checkCanAccessCountry(RequestContext.getUser());
        log.info("Fetching Country {} for user with identifier {}",
                id, RequestContext.getUsername());
        return new CountryApiResponsePayload(readCountryUseCase.execute(id));
    }

    @PutMapping(value = "/{id}")
    public CountryApiResponsePayload update(@PathVariable(value = "id") Long id,
                                            @RequestBody @Validated(UpdateGroup.class) CountryApiRequestPayload payload)
            throws UserForbiddenException, NotFoundException {
        validateUserAccessUseCase.checkCanManageCountry(RequestContext.getUser());
        log.info("Updating Country: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        return new CountryApiResponsePayload(updateCountryUseCase.execute(id, payload.toEntity()));
    }


}
