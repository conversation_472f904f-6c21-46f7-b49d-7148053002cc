package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * The request payload for the paginated endpoints.
 */
@Data
@NoArgsConstructor
public class PageFiltersRequestPayload {
    @Min(1)
    @Max(10000)
    private Integer page = 1;
    @Min(1)
    @Max(1000)
    private Integer size = 10;

    public PageFiltersRequestPayload(PageFilters pageFilters) {
        this.page = pageFilters.getPage();
        this.size = pageFilters.getSize();
    }

    public PageFilters toEntity() {
        return PageFilters
                .builder()
                .page(page)
                .size(size)
                .build();

    }
}
