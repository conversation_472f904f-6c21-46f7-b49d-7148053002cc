package pt.jumia.services.easytaxscan.api.controllers;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.easytaxscan.api.payloads.request.ExecutionLogApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.ExecutionLogFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.ExecutionLogSortFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.PageFiltersRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.ExecutionLogApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.CreateExecutionLogUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.ReadExecutionLogUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/execution-logs")
@Slf4j
@RequiredArgsConstructor
public class ExecutionLogController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final JsonUtils jsonUtils;
    private final CreateExecutionLogUseCase createExecutionLogUseCase;
    private final ReadExecutionLogUseCase readExecutionLogsUseCase;
    private final PaginationService paginationService;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ExecutionLogApiResponsePayload create(@RequestBody ExecutionLogApiRequestPayload payload)
            throws UserForbiddenException {
        log.info("Creating new Execution Log: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        validateUserAccessUseCase.checkCanManageExecutionLog(RequestContext.getUser());
        return new ExecutionLogApiResponsePayload(createExecutionLogUseCase.execute(payload.toEntity()));
    }


    @GetMapping(value = "/{id}")
    public ExecutionLogApiResponsePayload fetchById(@PathVariable(value = "id") Long id)
            throws UserForbiddenException {
        log.info("Fetching Execution Log {} for user with identifier {}",
                id, RequestContext.getUsername());
        validateUserAccessUseCase.checkCanAccessExecutionLog(RequestContext.getUser());
        return new ExecutionLogApiResponsePayload(readExecutionLogsUseCase.execute(id));
    }

    @GetMapping
    public PageResponsePayload<List<ExecutionLogApiResponsePayload>> fetch(HttpServletRequest request,
                                                                           @Valid ExecutionLogFiltersApiRequestPayload apiPayload,
                                                                           @Valid ExecutionLogSortFiltersApiRequestPayload sortFilter,
                                                                           PageFiltersRequestPayload pageFilter) throws UserForbiddenException {

        log.info("Fetching all Execution Logs for user with identifier {}", RequestContext.getUsername());
        validateUserAccessUseCase.checkCanAccessExecutionLog(RequestContext.getUser());
        ExecutionLogFilters filters = apiPayload.toEntity();
        ExecutionLogSortFilters sortFilters = sortFilter.toEntity();
        PageFilters pageFilters = pageFilter.toEntity();
        Long count = readExecutionLogsUseCase.executeCount(filters);
        List<ExecutionLogApiResponsePayload> results = readExecutionLogsUseCase.execute(filters, sortFilters, pageFilters)
                .stream().map(ExecutionLogApiResponsePayload::new).collect(Collectors.toList());
        return paginationService.buildPageResponsePayload(request, pageFilters, results, count);
    }

}
