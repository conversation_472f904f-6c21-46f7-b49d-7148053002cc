package pt.jumia.services.easytaxscan.api.payloads.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.MappingPreviewRequest;
import pt.jumia.services.easytaxscan.domain.entities.QueryParse;

import java.util.Map;

@Data
@NoArgsConstructor
public class MappingParseApiRequestPayload {

    @NotEmpty
    private String mapping;
    @NotNull
    private Map<String, Object> context;


    public MappingPreviewRequest toEntity() {
        return MappingPreviewRequest.builder()
                .context(context)
                .mapping(mapping)
                .build();
    }
}
