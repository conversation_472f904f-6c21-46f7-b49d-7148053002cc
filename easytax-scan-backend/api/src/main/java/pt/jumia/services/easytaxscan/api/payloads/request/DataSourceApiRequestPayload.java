package pt.jumia.services.easytaxscan.api.payloads.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.api.groups.CreateGroup;
import pt.jumia.services.easytaxscan.api.groups.UpdateGroup;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;

@Data
@NoArgsConstructor
public class DataSourceApiRequestPayload {

    @NotEmpty(groups = CreateGroup.class)
    @Size(max = 64)
    private String code;

    @NotNull(groups = {CreateGroup.class, UpdateGroup.class})
    @ValidEnumValue(required = true, enumClass = DataSource.Status.class)
    @Size(max = 32)
    private String status;

    @NotEmpty(groups = {CreateGroup.class, UpdateGroup.class})
    @Size(max = 1024)
    private String description;

    @NotNull(groups = {CreateGroup.class, UpdateGroup.class})
    private Boolean countrySegregated;


    public DataSourceApiRequestPayload(DataSource dataSource) {
        this.code = dataSource.getCode();
        this.status = dataSource.getStatus() == null ? null : dataSource.getStatus().name();
        this.description = dataSource.getDescription();
        this.countrySegregated = dataSource.getCountrySegregated();

    }

    public DataSource toEntity() {
        return DataSource.builder()
                .code(code)
                .status(status != null ? DataSource.Status.valueOf(status) : null)
                .description(description)
                .countrySegregated(countrySegregated)
                .build();

    }
}
