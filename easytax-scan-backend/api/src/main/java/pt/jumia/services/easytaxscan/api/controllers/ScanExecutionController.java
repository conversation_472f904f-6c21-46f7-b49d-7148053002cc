package pt.jumia.services.easytaxscan.api.controllers;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.ScheduledExecutionFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.ScheduledExecutionSortFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.ScheduledExecutionApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.executions.ReadScheduledExecutionsUseCase;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/api/scan-executions")
public class ScanExecutionController {

    private final ReadScheduledExecutionsUseCase readScheduledExecutionsUseCase;
    private final ValidateUserAccessUseCase validateUserAccessUseCase;

    @GetMapping(value = "/scheduled")
    public List<ScheduledExecutionApiResponsePayload> fetchAllScheduled(
            @Valid ScheduledExecutionFiltersApiRequestPayload scheduledExecutionFiltersApiRequestPayload,
            @Valid ScheduledExecutionSortFiltersApiRequestPayload scheduledExecutionSortFiltersApiRequestPayload)
            throws UserForbiddenException {
        log.info("Get scheduled jobs request received for user with identifier: {}",
                RequestContext.getUsername());
        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());

        return readScheduledExecutionsUseCase.execute(
                        scheduledExecutionFiltersApiRequestPayload.toEntity(),
                        scheduledExecutionSortFiltersApiRequestPayload.toEntity()
                )
                .stream()
                .map(ScheduledExecutionApiResponsePayload::new)
                .collect(Collectors.toList());
    }
}
