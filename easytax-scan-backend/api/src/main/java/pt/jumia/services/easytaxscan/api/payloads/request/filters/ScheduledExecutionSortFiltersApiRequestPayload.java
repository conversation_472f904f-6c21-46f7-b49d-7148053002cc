package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.ScheduledExecution;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScheduledExecutionSortFilters;

@Data
@NoArgsConstructor
public class ScheduledExecutionSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = ScheduledExecution.SortingFields.class)
    private String orderField = ScheduledExecution.SortingFields.NEXT_FIRE_TIME.name();
    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public ScheduledExecutionSortFilters toEntity() {
        return ScheduledExecutionSortFilters.builder()
                .field(orderField == null ? null : ScheduledExecution.SortingFields.valueOf(orderField))
                .direction(orderDirection == null ? null : OrderDirection.valueOf(orderDirection))
                .build();
    }
}
