package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class SubQueryFiltersApiRequestPayload {

    private String text;

    public SubQueryFilters toEntity() {
        return new SubQueryFilters().toBuilder()
                .text(text)
                .build();
    }
}
