package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class SubQueryApiResponsePayload {

    private Long id;

    private String mainQueryColumn;

    private String subQueryColumn;

    private String status;

    private Boolean isList;

    private QueryApiResponsePayload query;

    private QueryApiResponsePayload subQuery;


    private String createdBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;

    private String updatedBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;

    public SubQueryApiResponsePayload(SubQuery subQuery) {
        this.id = subQuery.getId();
        this.status = subQuery.getStatus().name();
        this.mainQueryColumn = subQuery.getMainQueryColumn();
        this.subQueryColumn = subQuery.getSubQueryColumn();
        this.isList = subQuery.getIsList();
        this.query = new QueryApiResponsePayload(subQuery.getQuery());
        this.subQuery = new QueryApiResponsePayload(subQuery.getSubQuery());
        this.createdBy = subQuery.getCreatedBy();
        this.createdAt = subQuery.getCreatedAt();
        this.updatedBy = subQuery.getUpdatedBy();
        this.updatedAt = subQuery.getUpdatedAt();
    }

    public SubQuery toEntity() {
        return SubQuery.builder()
                .id(id)
                .status(SubQuery.Status.valueOf(status))
                .mainQueryColumn(mainQueryColumn)
                .subQueryColumn(mainQueryColumn)
                .isList(isList)
                .createdBy(createdBy)
                .createdAt(createdAt)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .build();
    }
}
