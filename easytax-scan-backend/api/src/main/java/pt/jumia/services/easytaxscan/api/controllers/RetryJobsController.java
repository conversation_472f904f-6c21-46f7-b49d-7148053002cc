package pt.jumia.services.easytaxscan.api.controllers;


import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.easytaxscan.api.payloads.request.RetryJobApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.JobApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Job;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.jobs.CreateJobsUseCase;
import pt.jumia.services.easytaxscan.domain.jobs.DeleteJobsUseCase;
import pt.jumia.services.easytaxscan.domain.jobs.UpdateJobsUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

@RestController
@RequestMapping(value = "/api/jobs")
@Slf4j
@RequiredArgsConstructor
public class RetryJobsController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final JsonUtils jsonUtils;
    private final CreateJobsUseCase createJobsUseCase;
    private final UpdateJobsUseCase updateJobsUseCase;
    private final DeleteJobsUseCase deleteJobsUseCase;


    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public JobApiResponsePayload create(@RequestBody @Valid RetryJobApiRequestPayload payload)
            throws UserForbiddenException, SchedulerException {
        log.info("Creating retry job request received: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        validateUserAccessUseCase.checkCanManageJobs(RequestContext.getUser());
        Job job = createJobsUseCase.createRetryJob(payload.toEntity());
        return new JobApiResponsePayload(job);
    }

    @PutMapping(value = "/{jobCode}")
    @ResponseStatus(HttpStatus.OK)
    public JobApiResponsePayload update(@PathVariable(value = "jobCode") String jobName,
                                        @RequestBody @Valid RetryJobApiRequestPayload payload)
            throws UserForbiddenException, SchedulerException {
        payload.setJob(jobName);
        log.info("update retry job request received: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        validateUserAccessUseCase.checkCanManageJobs(RequestContext.getUser());
        Job job = updateJobsUseCase.updateRetryJob(payload.toEntity());
        return new JobApiResponsePayload(job);
    }

    @DeleteMapping(value = "/{jobCode}")
    @ResponseStatus(HttpStatus.OK)
    public void delete(@PathVariable(value = "jobCode") String jobName)
            throws UserForbiddenException, SchedulerException {
        log.info("delete retry job request received: {} for user with identifier {}",
                jobName, RequestContext.getUsername());
        validateUserAccessUseCase.checkCanManageJobs(RequestContext.getUser());
        deleteJobsUseCase.deleteRetryJob(jobName);
    }

}
