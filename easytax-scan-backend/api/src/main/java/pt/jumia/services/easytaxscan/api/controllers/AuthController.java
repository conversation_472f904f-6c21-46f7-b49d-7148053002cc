package pt.jumia.services.easytaxscan.api.controllers;

import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.easytaxscan.api.payloads.response.JwtResponsePayload;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.usecases.ReadUserAccessUseCase;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "")
public class AuthController {

    private final ReadUserAccessUseCase readUserAccessUseCase;

    @PostMapping(value = "/auth/swap-token")
    public JwtResponsePayload swapTemporaryToken(@RequestBody @NotBlank String code) {
        return new JwtResponsePayload(readUserAccessUseCase.findRealToken(code));
    }

    @GetMapping(value = "/api/auth/user/permissions")
    public Map<String, Map<String, List<String>>> fetchAllPermissions() {
        return this.readUserAccessUseCase.execute(RequestContext.getUser());
    }

    @GetMapping(value = "/api/auth/logout")
    public void logout() {
        this.readUserAccessUseCase.logout(RequestContext.getUser());
    }
}
