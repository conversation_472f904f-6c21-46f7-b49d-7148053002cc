package pt.jumia.services.easytaxscan.api.payloads.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.QueryPreview;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;

import java.util.List;

@Data
@NoArgsConstructor
public class QueryPreviewRequestPayload {


    private String sql;

    private Long dataSourceId;

    private Long queryId;

    private List<QueryPreview.SubQueryPreview> subQueries;

    public QueryPreviewRequestPayload(QueryPreview queryPreview) {
        this.sql = queryPreview.getSql();
        this.dataSourceId = queryPreview.getDataSource().getId();
        this.subQueries = queryPreview.getSubQueryList();
        this.queryId = queryPreview.getQueryId();
    }

    public QueryPreview toEntity() {
        return QueryPreview.builder()
                .sql(sql)
                .dataSource(DataSource.builder().id(dataSourceId).build())
                .subQueryList(subQueries)
                .queryId(queryId)
                .build();
    }
}
