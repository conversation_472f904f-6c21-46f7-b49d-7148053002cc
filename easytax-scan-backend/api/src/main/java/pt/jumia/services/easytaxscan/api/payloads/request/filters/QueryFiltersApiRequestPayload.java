package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.filter.QueryFilters;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class QueryFiltersApiRequestPayload {


    private String text;
    private Long dataSourceId;


    public QueryFilters toEntity() {
        return new QueryFilters().toBuilder()
                .text(text)
                .dataSourceId(dataSourceId)
                .build();
    }
}
