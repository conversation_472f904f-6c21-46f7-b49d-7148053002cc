package pt.jumia.services.easytaxscan.api.config;


import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.Collections;

/**
 * Swagger configuration for API documentation
 */
@Configuration
public class SwaggerConfig {

  private static final String API_ENDPOINT = "/api/**";
  private static final String API_PUBLIC_ENDPOINT = "/public/**";

  @Bean
  public GroupedOpenApi api() {
    return GroupedOpenApi.builder()
            .group("EASYTAXSCAN API")
            .pathsToMatch(API_ENDPOINT, API_PUBLIC_ENDPOINT)
            .build();
  }

  @Bean
  public OpenAPI customOpenAPI() {
    return new OpenAPI()
            .openapi("3.0.1")
            .components(
                    new Components()
                            .addSecuritySchemes("bearer-key",
                                    new SecurityScheme()
                                            .type(SecurityScheme.Type.HTTP)
                                            .scheme("bearer")
                                            .bearerFormat("JWT")
                            )
            )
            .info(
                    new Info()
                            .title("EASYTAXSCAN API")
                            .version("v1")
                            .termsOfService("http://swagger.io/terms/")
                            .license(
                                    new License()
                                            .name("Apache 2.0")
                                            .url("http://springdoc.org")
                            )
            )
            .addSecurityItem(
                    new SecurityRequirement()
                            .addList("bearer-jwt", Arrays.asList("read", "write"))
                            .addList("bearer-key", Collections.emptyList())
            );
  }
}
