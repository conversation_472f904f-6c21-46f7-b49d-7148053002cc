package pt.jumia.services.easytaxscan.api.payloads.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.api.groups.CreateGroup;
import pt.jumia.services.easytaxscan.api.groups.UpdateGroup;
import pt.jumia.services.easytaxscan.domain.entities.Country;

@Data
@NoArgsConstructor
public class CountryApiRequestPayload {

    @NotEmpty(groups = CreateGroup.class)
    @Size(max = 64)
    private String countryCode;

    @NotEmpty(groups = {CreateGroup.class, UpdateGroup.class})
    @Size(max = 128)
    private String countryName;

    public CountryApiRequestPayload(Country country) {
        this.countryCode = country.getCountryCode();
        this.countryName = country.getCountryName();
    }

    public Country toEntity() {
        return Country.builder()
                .countryCode(countryCode)
                .countryName(countryName)
                .build();
    }
}
