package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class DataSourceApiResponsePayload {

    private Long id;

    private String code;

    private String status;

    private String description;

    private Boolean countrySegregated;

    private String createdBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;

    private String updatedBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;


    public DataSourceApiResponsePayload(DataSource dataSource) {
        this.id = dataSource.getId();
        this.code = dataSource.getCode();
        this.status = dataSource.getStatus().name();
        this.description = dataSource.getDescription();
        this.countrySegregated = dataSource.getCountrySegregated();
        this.createdBy = dataSource.getCreatedBy();
        this.createdAt = dataSource.getCreatedAt();
        this.updatedBy = dataSource.getUpdatedBy();
        this.updatedAt = dataSource.getUpdatedAt();
    }

    public DataSource toEntity() {
        return DataSource.builder()
                .id(id)
                .code(code)
                .status(DataSource.Status.valueOf(status))
                .description(description)
                .countrySegregated(countrySegregated)
                .createdBy(createdBy)
                .createdAt(createdAt)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .build();
    }
}
