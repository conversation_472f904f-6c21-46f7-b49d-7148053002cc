package pt.jumia.services.easytaxscan.api.payloads.request.audit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuditedEntitySortFiltersApiRequestPayload {

    @ValidEnumValue(enumClass = AuditedEntity.SortingFields.class)
    private String orderField = AuditedEntity.SortingFields.REV.name();

    @ValidEnumValue(enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public AuditedEntitySortFilters toEntity() {
        return AuditedEntitySortFilters.builder()
                .field(AuditedEntity.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}
