package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SubQuerySortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = SubQuery.SortingFields.class)
    private String orderField = SubQuery.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public SubQuerySortFilters toEntity() {
        return SubQuerySortFilters.builder()
                .field(SubQuery.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection)).build();
    }
}
