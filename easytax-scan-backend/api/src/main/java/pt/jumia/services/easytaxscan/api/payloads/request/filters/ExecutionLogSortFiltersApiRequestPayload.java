package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogSortFilters;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExecutionLogSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = ExecutionLog.SortingFields.class)
    private String orderField = ExecutionLog.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public ExecutionLogSortFilters toEntity() {
        return ExecutionLogSortFilters.builder()
                .field(ExecutionLog.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection)).build();
    }
}
