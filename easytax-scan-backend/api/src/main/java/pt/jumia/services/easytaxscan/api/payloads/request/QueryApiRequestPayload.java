package pt.jumia.services.easytaxscan.api.payloads.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.Query;

import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class QueryApiRequestPayload {

    @NotEmpty
    @NotNull
    private String sql;

    @NotEmpty
    private String code;

    @NotEmpty
    private String description;

    @NotNull
    @Min(1)
    private Integer pageSize;

    private String paginationField;

    @NotNull
    private Long dataSourceId;

    @NotNull
    private String sampleResult;

    @Valid
    private List<SubQueryApiRequestPayload> subQueries;  // Optional list of subqueries


    public QueryApiRequestPayload(Query query) {
        this.sql = query.getSql();
        this.code = query.getCode();
        this.description = query.getDescription();
        this.pageSize = query.getPageSize();
        this.paginationField = query.getPaginationField();
        this.dataSourceId = query.getDataSource().getId();
        this.sampleResult = query.getSampleResult();
        this.subQueries = CollectionUtils.isEmpty(query.getSubQueries()) ? null : query.getSubQueries().stream()
                .map(SubQueryApiRequestPayload::new)
                .collect(Collectors.toList());
    }

    public Query toEntity() {
        return Query.builder()
                .sql(sql)
                .code(code)
                .description(description)
                .pageSize(pageSize)
                .paginationField(paginationField)
                .dataSource(DataSource.builder().id(dataSourceId).build())
                .subQueries(CollectionUtils.isEmpty(subQueries) ? null : subQueries.stream()
                        .map(SubQueryApiRequestPayload::toEntity)
                        .collect(Collectors.toList()))
                .sampleResult(sampleResult)
                .build();
    }
}
