package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * The response payload for endpoints with pagination.
 *
 * @param <T> The result type, normally a list of an entity payload.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResponsePayload<T> {
    Links links;
    Integer page;
    Integer size;
    Long total;
    T results;

    /**
     * The payload with the links related to this pagination.
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Links {
        String base;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        String previousPage;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        String nextPage;
    }
}

