package pt.jumia.services.easytaxscan.api.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.easytaxscan.api.payloads.response.JobApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.jobs.PauseJobsUseCase;
import pt.jumia.services.easytaxscan.domain.jobs.ReadJobsUseCase;
import pt.jumia.services.easytaxscan.domain.jobs.ResumeJobsUseCase;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/api/jobs")
public class JobSchedulerController {
    private final PauseJobsUseCase pauseJobsUsecase;
    private final ResumeJobsUseCase resumeJobsUsecase;
    private final ReadJobsUseCase readJobsUseCase;

    @GetMapping
    public List<JobApiResponsePayload> getAllJob() throws SchedulerException, UserForbiddenException {
        log.info("Fetching all jobs for user with identifier {}",
                RequestContext.getUsername());
        return readJobsUseCase.fetchAllJobs()
                .stream()
                .map(JobApiResponsePayload::new)
                .toList();
    }

    @PostMapping("/pauseAllJobs")
    public void pauseAllJob() throws SchedulerException, UserForbiddenException {
        log.info("Request received for pausing all jobs for user with identifier {}",
                RequestContext.getUsername());
        pauseJobsUsecase.pauseAllJobs();
    }

    @PostMapping("/resumeAllJobs")
    public void resumeAllJob() throws SchedulerException, UserForbiddenException {
        log.info("Request received for resume all jobs for user with identifier {}",
                RequestContext.getUsername());
        resumeJobsUsecase.resumeAllJobs();
    }


}
