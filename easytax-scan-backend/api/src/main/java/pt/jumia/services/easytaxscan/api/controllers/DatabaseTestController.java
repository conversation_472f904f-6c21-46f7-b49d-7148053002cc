package pt.jumia.services.easytaxscan.api.controllers;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.easytaxscan.domain.entities.ClientDetailsDTO;
import pt.jumia.services.easytaxscan.domain.usecases.jumiaclient.ReadClientDetailsUseCase;

import java.util.List;

@RestController
@RequestMapping(value = "/api/database")
@Slf4j
@RequiredArgsConstructor
public class DatabaseTestController {

    private final ReadClientDetailsUseCase readClientDetailsUseCase;

    @GetMapping(value = "/clients")
    public List<ClientDetailsDTO> getAllClientsWithDetails(){
        log.info("Fetching all clients with details");
         return readClientDetailsUseCase.getAllClientsWithDetails();
    }
}
