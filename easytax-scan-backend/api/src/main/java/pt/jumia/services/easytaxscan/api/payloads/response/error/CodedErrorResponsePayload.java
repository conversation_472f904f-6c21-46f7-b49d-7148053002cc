package pt.jumia.services.easytaxscan.api.payloads.response.error;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.exceptions.ErrorCode;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CodedErrorResponsePayload {

    private int code;
    private String name;
    private String details;
    private List<CodedErrorResponsePayload> errors;

    private CodedErrorResponsePayload(ErrorCode errorCode, String message, List<CodedErrorResponsePayload> errors) {

        this.code = errorCode.getCode();
        this.name = errorCode.name();
        this.details = message;
        this.errors = errors;
    }

    private CodedErrorResponsePayload(String message, List<CodedErrorResponsePayload> errors) {

        this.details = message;
        this.errors = errors;
    }

    public static CodedErrorResponsePayload forSingleError(ErrorCode errorCode, String message) {

        return new CodedErrorResponsePayload(errorCode, message, null);
    }

    public static CodedErrorResponsePayload forErrorWithCauses(ErrorCode errorCode, String message,
        List<CodedErrorResponsePayload> errors) {

        return new CodedErrorResponsePayload(errorCode, message, errors);
    }

}
