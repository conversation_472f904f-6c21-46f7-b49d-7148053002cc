package pt.jumia.services.easytaxscan.api.controllers.audit;


import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.PageFiltersRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.audit.AuditedEntitySortFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.audit.AuditApiResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.audit.ReadDocumentAuditUseCase;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping(value = "/api/audit/document")
@Slf4j
@RequiredArgsConstructor
public class DocumentAuditController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final ReadDocumentAuditUseCase readDocumentAuditUseCase;
    private final PaginationService paginationService;


    @GetMapping("/{documentId}")
    public PageResponsePayload<List<AuditApiResponsePayload>> fetch(@PathVariable(value = "documentId") Long documentId,
                                                                    HttpServletRequest httpServletRequest,
                                                                    @Valid PageFiltersRequestPayload pageFiltersApiRequestPayload,
                                                                    @Valid AuditedEntitySortFiltersApiRequestPayload auditSortFiltersRequest)
            throws UserForbiddenException {
        log.info("Fetching Document history user with identifier {}",
                RequestContext.getUsername());
        validateUserAccessUseCase.checkCanAccessDocument(RequestContext.getUser());
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();
        AuditedEntitySortFilters sortFilters = auditSortFiltersRequest.toEntity();
        List<AuditApiResponsePayload> list = readDocumentAuditUseCase.executeById(documentId, sortFilters, pageFilters).stream()
                .map(AuditApiResponsePayload::new)
                .toList();
        long total = readDocumentAuditUseCase.executeCountByDocumentId(documentId);
        return paginationService.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                list,
                total
        );
    }
}
