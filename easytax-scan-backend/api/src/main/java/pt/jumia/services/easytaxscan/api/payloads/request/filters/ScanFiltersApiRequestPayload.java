package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanFilters;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ScanFiltersApiRequestPayload {

    private String text;

    private Long queryId;

    @ValidEnumValue(required = false, enumClass = Scan.Status.class)
    private String status;

    @ValidEnumValue(required = false, enumClass = Scan.Mode.class)
    private String mode;

    private Long countryId;

    public ScanFilters toEntity() {
        return new ScanFilters().toBuilder()
                .text(text)
                .status(this.status == null ? null : Scan.Status.valueOf((this.status)))
                .mode(this.mode == null ? null : Scan.Mode.valueOf((this.mode)))
                .queryId(queryId)
                .countryId(countryId)
                .build();
    }
}
