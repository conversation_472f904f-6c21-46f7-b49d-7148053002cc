package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanSortFilters;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScanSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = Scan.SortingFields.class)
    private String orderField = Scan.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public ScanSortFilters toEntity() {
        return ScanSortFilters.builder()
                .field(Scan.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection)).build();
    }
}
