package pt.jumia.services.easytaxscan.api.payloads.request;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.Tag;

/**
 * Tag api request payload pojo representation
 */
@Data
@NoArgsConstructor
public class TagApiRequestPayload {

    private Long id;
    @NotEmpty
    private String name;
    @NotEmpty
    private String description;
    @NotEmpty
    private String color;

    public TagApiRequestPayload(Tag tag) {
        this.id = tag.getId();
        this.name = tag.getName();
        this.description = tag.getDescription();
        this.color = tag.getColor();
    }

    public Tag toEntity() {
        return Tag
            .builder()
            .id(id)
            .name(name)
            .description(description)
            .color(color)
            .build();
    }
}
