package pt.jumia.services.easytaxscan.api.payloads.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.validators.constraints.NotEmpty;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;


@Data
@NoArgsConstructor
public class WebhookApiRequestPayload {

    @NotEmpty
    private String payload;
    @NotEmpty
    private Long documentId;


    public WebHooks toEntity() {
        return new WebHooks().toBuilder()
                .document(documentId != null
                        ? Document.builder().id(documentId).build()
                        : null)
                .payload(this.payload.trim())
                .build();
    }

    public WebhookApiRequestPayload(WebHooks webhooks) {
        this.payload = webhooks.getPayload();
        this.documentId = webhooks.getDocument().getId();
    }
}
