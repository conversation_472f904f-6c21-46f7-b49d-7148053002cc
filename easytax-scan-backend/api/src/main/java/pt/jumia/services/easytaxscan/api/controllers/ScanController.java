package pt.jumia.services.easytaxscan.api.controllers;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.easytaxscan.api.groups.CreateGroup;
import pt.jumia.services.easytaxscan.api.groups.UpdateGroup;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.PageFiltersRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.ScanApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.ScanFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.ScanSortFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.ScanApiResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.exceptions.RecordAlreadyExistsException;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.jobs.ScanJobExecutionUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.scan.CreateScanUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.scan.DeleteScanUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.scan.ReadScanUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.scan.UpdateScanUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/scans")
@Slf4j
@RequiredArgsConstructor
public class ScanController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final JsonUtils jsonUtils;
    private final CreateScanUseCase createScanUseCase;
    private final ReadScanUseCase readScanUseCase;
    private final UpdateScanUseCase updateScanUseCase;
    private final DeleteScanUseCase deleteScanUseCase;
    private final PaginationService paginationService;
    private final ScanJobExecutionUseCase scanJobExecutionUseCase;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ScanApiResponsePayload create
            (@RequestBody @Validated(CreateGroup.class) @Valid ScanApiRequestPayload payload)
            throws UserForbiddenException, RecordAlreadyExistsException {
        validateUserAccessUseCase.checkCanManageScan(RequestContext.getUser());
        log.info("Creating new Scan: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        return new ScanApiResponsePayload(createScanUseCase.execute(payload.toEntity()));
    }

    @PutMapping(value = "/{id}")
    public ScanApiResponsePayload update(@PathVariable(value = "id") Long id,
                                         @RequestBody @Validated(UpdateGroup.class) ScanApiRequestPayload payload)
            throws UserForbiddenException, NotFoundException {
        validateUserAccessUseCase.checkCanManageScan(RequestContext.getUser());
        log.info("Updating Scan: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        return new ScanApiResponsePayload(updateScanUseCase.execute(id, payload.toEntity()));
    }

    @GetMapping(value = "/{id}")
    public ScanApiResponsePayload fetchById(@PathVariable(value = "id") Long id)
            throws UserForbiddenException {
        validateUserAccessUseCase.checkCanAccessScan(RequestContext.getUser());
        log.info("Fetching Scan {} for user with identifier {}",
                id, RequestContext.getUsername());
        return new ScanApiResponsePayload(readScanUseCase.execute(id));
    }

    @GetMapping
    public PageResponsePayload<List<ScanApiResponsePayload>> fetch(HttpServletRequest request,
                                                                   @Valid ScanFiltersApiRequestPayload apiPayload,
                                                                   @Valid ScanSortFiltersApiRequestPayload sortFilter,
                                                                   PageFiltersRequestPayload pageFilter) throws UserForbiddenException {
        validateUserAccessUseCase.checkCanAccessScan(RequestContext.getUser());
        log.info("Fetching all Scans for user with identifier {}", RequestContext.getUsername());
        ScanFilters filters = apiPayload.toEntity();
        ScanSortFilters sortFilters = sortFilter.toEntity();
        PageFilters pageFilters = pageFilter.toEntity();
        Long count = readScanUseCase.executeCount(filters);

        List<ScanApiResponsePayload> results = readScanUseCase.execute(filters, sortFilters, pageFilters)
                .stream().map(ScanApiResponsePayload::new).collect(Collectors.toList());
        return paginationService.buildPageResponsePayload(request, pageFilters, results, count);
    }

    @DeleteMapping(value = "/{id}")
    public void delete(@PathVariable(value = "id") Long id)
            throws UserForbiddenException, NotFoundException {
        validateUserAccessUseCase.checkCanManageScan(RequestContext.getUser());
        log.info("Deleting Scan: {} for user with identifier {}", id
                , RequestContext.getUsername());
        deleteScanUseCase.execute(id);
    }

    @PatchMapping("/{id}/execute")
    public void executeManualScan(@PathVariable("id") Long id)
            throws UserForbiddenException, NotFoundException {
        log.info("Request received for Manual execute scan with id: {} by user: {}", id,
                RequestContext.getUsername());
        validateUserAccessUseCase.checkCanManageScan(RequestContext.getUser());
        scanJobExecutionUseCase.execute(id);

    }
}
