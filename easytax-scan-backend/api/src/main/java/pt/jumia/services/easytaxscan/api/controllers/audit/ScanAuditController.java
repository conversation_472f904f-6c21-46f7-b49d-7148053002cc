package pt.jumia.services.easytaxscan.api.controllers.audit;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.PageFiltersRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.audit.AuditedEntitySortFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.audit.AuditApiResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.audit.ReadScanAuditUseCase;

import java.util.List;

@RestController
@RequestMapping(value = "/api/audit/scan")
@Slf4j
@RequiredArgsConstructor
public class ScanAuditController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final ReadScanAuditUseCase readScanAuditUseCase;
    private final PaginationService paginationService;


    @GetMapping("/{scanId}")
    public PageResponsePayload<List<AuditApiResponsePayload>> fetch(@PathVariable(value = "scanId") Long scanId,
                                                                    HttpServletRequest request,
                                                                    @Valid AuditedEntitySortFiltersApiRequestPayload auditSortFiltersRequest,
                                                                    @Valid PageFiltersRequestPayload pageFilter) throws UserForbiddenException {
        validateUserAccessUseCase.checkCanAccessScan(RequestContext.getUser());
        log.info("Fetching all Scans for user with identifier {}", RequestContext.getUsername());
        AuditedEntitySortFilters sortFilters = auditSortFiltersRequest.toEntity();
        PageFilters pageFilters = pageFilter.toEntity();
        List<AuditApiResponsePayload> results = readScanAuditUseCase.executeById(scanId, sortFilters, pageFilters)
                .stream().
                map(AuditApiResponsePayload::new).toList();
        long total = readScanAuditUseCase.executeCountByScanId(scanId);
        return paginationService.buildPageResponsePayload(request, pageFilters, results, total);
    }

}
