package pt.jumia.services.easytaxscan.api.controllers;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.easytaxscan.api.payloads.request.DocumentApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.DocumentFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.DocumentSortFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.PageFiltersRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.DocumentApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.document.CreateDocumentUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.document.ReadDocumentUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/documents")
@Slf4j
@RequiredArgsConstructor
public class DocumentController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final JsonUtils jsonUtils;
    private final ReadDocumentUseCase readDocumentUseCase;
    private final PaginationService paginationService;
    private final CreateDocumentUseCase createDocumentUseCase;


    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public DocumentApiResponsePayload create(@RequestBody @Valid DocumentApiRequestPayload payload)
            throws UserForbiddenException {
        log.info("Creating new Document: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        validateUserAccessUseCase.checkCanAccessDocument(RequestContext.getUser());
        return new DocumentApiResponsePayload(createDocumentUseCase.execute(payload.toEntity()));
    }

    @GetMapping
    public PageResponsePayload<List<DocumentApiResponsePayload>> fetch(HttpServletRequest request,
                                                                       @Valid DocumentFiltersApiRequestPayload apiPayload,
                                                                       @Valid DocumentSortFiltersApiRequestPayload sortFilter,
                                                                       PageFiltersRequestPayload pageFilter) throws UserForbiddenException {
        validateUserAccessUseCase.checkCanAccessDocument(RequestContext.getUser());
        log.info("Fetching all Documents for user with identifier {}", RequestContext.getUsername());
        DocumentFilters filters = apiPayload.toEntity();
        DocumentSortFilters sortFilters = sortFilter.toEntity();
        PageFilters pageFilters = pageFilter.toEntity();
        Long count = readDocumentUseCase.executeCount(filters);

        List<DocumentApiResponsePayload> results = readDocumentUseCase.execute(filters, sortFilters, pageFilters)
                .stream().map(DocumentApiResponsePayload::new).collect(Collectors.toList());
        return paginationService.buildPageResponsePayload(request, pageFilters, results, count);
    }
}
