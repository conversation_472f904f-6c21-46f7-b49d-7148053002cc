package pt.jumia.services.easytaxscan.api.controllers.audit;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.PageFiltersRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.audit.AuditedEntitySortFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.audit.AuditApiResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.audit.ReadDataSourceAuditUseCase;

import java.util.List;

@RestController
@RequestMapping(value = "/api/audit/datasources")
@Slf4j
@RequiredArgsConstructor
public class DataSourceAuditController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final ReadDataSourceAuditUseCase readDataSourceAuditUseCase;
    private final PaginationService paginationService;

    @GetMapping("/{dataSourceId}")
    public PageResponsePayload<List<AuditApiResponsePayload>> fetch(@PathVariable(value = "dataSourceId") Long dataSourceId,
                                                                    HttpServletRequest request,
                                                                    @Valid AuditedEntitySortFiltersApiRequestPayload auditSortFiltersRequest,
                                                                    @Valid PageFiltersRequestPayload pageFilter) throws UserForbiddenException {
        validateUserAccessUseCase.checkCanAccessDataSource(RequestContext.getUser());
        log.info("Fetching all DataSource audits for user with identifier {}", RequestContext.getUsername());

        AuditedEntitySortFilters sortFilters = auditSortFiltersRequest.toEntity();
        PageFilters pageFilters = pageFilter.toEntity();

        List<AuditApiResponsePayload> results = readDataSourceAuditUseCase.executeById(dataSourceId, sortFilters, pageFilters)
                .stream()
                .map(AuditApiResponsePayload::new)
                .toList();

        long total = readDataSourceAuditUseCase.executeCountByDataSourceId(dataSourceId);

        return paginationService.buildPageResponsePayload(request, pageFilters, results, total);
    }
}
