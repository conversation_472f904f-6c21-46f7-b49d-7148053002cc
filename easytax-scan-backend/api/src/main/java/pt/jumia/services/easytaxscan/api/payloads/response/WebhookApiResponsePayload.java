package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.domain.entities.ConvertibleToDomain;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class WebhookApiResponsePayload implements ConvertibleToDomain<WebHooks> {

    private Long id;
    private String payload;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    private String createdBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;
    private String updatedBy;

    public WebhookApiResponsePayload(WebHooks webHook) {
        this.id = webHook.getId();
        this.payload = webHook.getPayload();
        this.createdAt = webHook.getCreatedAt();
        this.createdBy = webHook.getCreatedBy();
        this.updatedAt = webHook.getUpdatedAt();
        this.updatedBy = webHook.getUpdatedBy();
    }

    public WebHooks toEntity() {
        return new WebHooks().toBuilder()
                .id(this.id)
                .payload(this.payload)
                .createdAt(this.createdAt)
                .createdBy(this.createdBy)
                .updatedAt(this.updatedAt)
                .updatedBy(this.updatedBy)
                .build();
    }
}
