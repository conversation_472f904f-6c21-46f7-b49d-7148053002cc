package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.domain.entities.ScheduledExecution;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ScheduledExecutionApiResponsePayload {
    private Long scanId;
    private String scanCode;
    private Long countryId;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime nextFireTimeLocal;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime nextFireTimeUtc;
    private String localTimeZone;
    private ScheduledExecution.Status status;

    public ScheduledExecutionApiResponsePayload(ScheduledExecution scheduledExecution) {
        this.scanId = scheduledExecution.getScanId();
        this.countryId = scheduledExecution.getCountryId();
        this.scanCode = scheduledExecution.getScanCode();
        this.nextFireTimeLocal = scheduledExecution.getNextFireTimeLocal();
        this.nextFireTimeUtc = scheduledExecution.getNextFireTimeUtc();
        this.localTimeZone = scheduledExecution.getLocalTimeZone();
        this.status = scheduledExecution.getStatus();
    }
}
