package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScheduledExecutionFilters;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ScheduledExecutionFiltersApiRequestPayload {

    private String filterText;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime nextFireTimeFrom;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime nextFireTimeTo;

    public ScheduledExecutionFilters toEntity() {
        return ScheduledExecutionFilters.builder()
                .filterText(StringUtils.isEmpty(filterText) ? null : filterText.trim())
                .nextFireTimeFrom(nextFireTimeFrom)
                .nextFireTimeTo(nextFireTimeTo)
                .build();
    }
}
