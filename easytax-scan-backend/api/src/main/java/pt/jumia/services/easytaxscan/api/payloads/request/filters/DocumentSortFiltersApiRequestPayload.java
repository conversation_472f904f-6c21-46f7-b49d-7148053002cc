package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentSortFilters;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DocumentSortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = Document.SortingFields.class)
    private String orderField = Document.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public DocumentSortFilters toEntity() {
        return DocumentSortFilters.builder()
                .field(Document.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection)).build();
    }
}
