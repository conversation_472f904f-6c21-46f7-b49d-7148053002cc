package pt.jumia.services.easytaxscan.api.validations.validators;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Set;
import java.util.stream.Collectors;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;

public class EnumValueValidator implements ConstraintValidator<ValidEnumValue, String> {

    private ValidEnumValue params;

    private Set<String> availableEnumNames;

    @Override
    public void initialize(ValidEnumValue stringEnumeration) {

        this.params = stringEnumeration;

        Class<? extends Enum<?>> enumSelected = stringEnumeration.enumClass();
        Set<? extends Enum<?>> enumInstances = Set.of(enumSelected.getEnumConstants());
        availableEnumNames = enumInstances.stream().map(Enum::name).collect(Collectors.toSet());
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {

        this.params.required();
        if (this.params.required() && value == null) {
            return false;
        } else if (!this.params.required() && value == null) {
            return true;
        } else {
            return availableEnumNames.stream().anyMatch(s -> s.equalsIgnoreCase(value));
        }
    }

}
