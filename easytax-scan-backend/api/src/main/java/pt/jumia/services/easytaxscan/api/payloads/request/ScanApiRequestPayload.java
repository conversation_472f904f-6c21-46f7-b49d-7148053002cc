package pt.jumia.services.easytaxscan.api.payloads.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.api.groups.CreateGroup;
import pt.jumia.services.easytaxscan.api.groups.UpdateGroup;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.Scan;

@Data
@NoArgsConstructor
public class ScanApiRequestPayload {

    @NotNull(groups = {CreateGroup.class, UpdateGroup.class})
    private Long queryId;

    @NotEmpty(groups = {CreateGroup.class, UpdateGroup.class})
    private String cronExpression;

    @NotEmpty(groups = CreateGroup.class)
    @Size(max = 64)
    private String code;

    @NotNull(groups = {CreateGroup.class, UpdateGroup.class})
    @ValidEnumValue(required = true, enumClass = Scan.Status.class)
    @Size(max = 32)
    private String status;

    @NotNull(groups = {CreateGroup.class, UpdateGroup.class})
    @ValidEnumValue(required = true, enumClass = Scan.Mode.class)
    @Size(max = 32)
    private String mode;

    @NotEmpty(groups = {CreateGroup.class, UpdateGroup.class})
    private String description;

    @NotEmpty(groups = {CreateGroup.class, UpdateGroup.class})
    private String mapping;

    @NotEmpty(groups = {CreateGroup.class, UpdateGroup.class})
    private String sidColumn;

    private Long countryId;

    public ScanApiRequestPayload(Scan scan) {
        this.queryId = scan.getQuery().getId();
        this.cronExpression = scan.getCronExpression();
        this.code = scan.getCode();
        this.status = scan.getStatus() == null ? null : scan.getStatus().name();
        this.mode = scan.getMode() == null ? null : scan.getMode().name();
        this.description = scan.getDescription();
        this.mapping = scan.getMapping();
        this.sidColumn = scan.getSidColumn();
        this.countryId = scan.getCountry() != null ? scan.getCountry().getId() : null;
    }

    public Scan toEntity() {
        return Scan.builder()
                .query(Query.builder().id(queryId).build())
                .cronExpression(cronExpression)
                .code(code)
                .status(status != null ? Scan.Status.valueOf(status) : null)
                .mode(mode != null ? Scan.Mode.valueOf(mode) : null)
                .description(description)
                .mapping(mapping)
                .sidColumn(sidColumn)
                .country(Country.builder().id(countryId).build())
                .build();

    }
}
