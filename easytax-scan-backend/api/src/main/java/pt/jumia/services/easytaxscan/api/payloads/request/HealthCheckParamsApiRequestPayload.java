package pt.jumia.services.easytaxscan.api.payloads.request;


import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.HealthCheckParams;

@Data
@NoArgsConstructor
public class HealthCheckParamsApiRequestPayload {

    private String countryCode;

    public HealthCheckParams toEntity() {
        return HealthCheckParams
                .builder()
                .countryCode(countryCode)
                .build();

    }

    public HealthCheckParamsApiRequestPayload(HealthCheckParams healthCheckParams) {
        this.countryCode = healthCheckParams.getCountryCode();
    }
}
