package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.domain.entities.Setting;

@Data
@NoArgsConstructor
public class SettingApiResponsePayload {

    private Long id;
    private String property;
    private Setting.Type type;
    private String overrideKey;
    private String description;
    private String value;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;
    private String createdBy;
    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;
    private String updatedBy;

    public SettingApiResponsePayload(Setting setting) {
        this.id = setting.getId();
        this.property = setting.getProperty();
        this.type = setting.getType();
        this.overrideKey = setting.getOverrideKey();
        this.description = setting.getDescription();
        this.value = setting.getValue();
        this.createdAt = setting.getCreatedAt();
        this.createdBy = setting.getCreatedBy();
        this.updatedAt = setting.getUpdatedAt();
        this.updatedBy = setting.getUpdatedBy();
    }

    public Setting toEntity() {
        return new Setting().toBuilder()
                .id(this.id)
                .property(this.property)
                .type(this.type)
                .overrideKey(this.overrideKey)
                .description(this.description)
                .value(this.value)
                .createdAt(this.createdAt)
                .createdBy(this.createdBy)
                .updatedAt(this.updatedAt)
                .updatedBy(this.updatedBy)
                .build();
    }
}
