package pt.jumia.services.easytaxscan.api.utils;

import pt.jumia.services.easytaxscan.api.payloads.response.*;
import pt.jumia.services.easytaxscan.domain.entities.*;

/**
 * A factory to create the right audit payload.
 */
public class AuditApiPayloadFactory {
    public static Object createAuditPayload(AuditedEntity<?> auditedEntity) {
        if (auditedEntity.getEntity() instanceof Scan) {
            return new ScanApiResponsePayload((Scan) auditedEntity.getEntity());
        } else if (auditedEntity.getEntity() instanceof Query) {
            return new QueryApiResponsePayload((Query) auditedEntity.getEntity());
        } else if (auditedEntity.getEntity() instanceof ExecutionLog) {
            return new ExecutionLogApiResponsePayload((ExecutionLog) auditedEntity.getEntity());
        } else if (auditedEntity.getEntity() instanceof Document) {
            return new DocumentApiResponsePayload((Document) auditedEntity.getEntity());
        } else if (auditedEntity.getEntity() instanceof DataSource) {
            return new DataSourceApiResponsePayload((DataSource) auditedEntity.getEntity());
        } else if (auditedEntity.getEntity() instanceof WebHooks) {
            return new WebhookApiResponsePayload((WebHooks) auditedEntity.getEntity());
        } else {
            throw new IllegalArgumentException("The given entity is not supported");
        }
    }
}
