package pt.jumia.services.easytaxscan.api.controllers;


import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.PageFiltersRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.WebhookApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.WebhooksSortFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.WebhookApiResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.filter.WebhooksSortFilters;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.webhook.CreateWebhooksUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.webhook.ReadWebhooksUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/documents")
@Slf4j
@RequiredArgsConstructor
public class WebhookController {


    private final CreateWebhooksUseCase createWebhooksUseCase;
    private final ReadWebhooksUseCase readWebhooksUseCase;
    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final PaginationService paginationService;
    private final JsonUtils jsonUtils;

    @GetMapping("/{id}/webhooks")
    @ResponseStatus(HttpStatus.OK)
    public WebhookApiResponsePayload findById(@PathVariable("id") long id) {
        validateUserAccessUseCase.checkCanAccessWebhooks(RequestContext.getUser());
        log.info("Fetching webhooks {} for user with identifier {}",
                id, RequestContext.getUsername());
        WebHooks webhooks = readWebhooksUseCase.findById(id);
        return new WebhookApiResponsePayload(webhooks);
    }

    @GetMapping("/webhooks")
    @ResponseStatus(HttpStatus.OK)
    public PageResponsePayload<List<WebhookApiResponsePayload>> findAll(HttpServletRequest request,
                                                                        WebhooksSortFiltersApiRequestPayload webhooksSortFiltersApiRequestPayload,
                                                                        @Valid PageFiltersRequestPayload pageFiltersRequestPayload) {
        validateUserAccessUseCase.checkCanAccessWebhooks(RequestContext.getUser());
        log.info("Fetching all webhooks to filter for user with identifier {}", RequestContext.getUsername());
        PageFilters pageFilters = pageFiltersRequestPayload.toEntity();
        WebhooksSortFilters webhooksSortFilters = webhooksSortFiltersApiRequestPayload.toEntity();
        List<WebHooks> webHooksList = readWebhooksUseCase.findAll(webhooksSortFilters, pageFilters);
        Long count = readWebhooksUseCase.executeCount();
        List<WebhookApiResponsePayload> results = webHooksList.stream()
                .map(WebhookApiResponsePayload::new)
                .collect(Collectors.toList());
        return paginationService.buildPageResponsePayload(request, pageFilters, results, count);
    }

    @PostMapping("/webhooks")
    @ResponseStatus(HttpStatus.CREATED)
    public WebhookApiResponsePayload create(@RequestBody @Valid WebhookApiRequestPayload payload) {
        validateUserAccessUseCase.checkCanManageWebhooks(RequestContext.getUser());
        log.info("Creating new webhooks: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        WebHooks webHooks = createWebhooksUseCase.execute(payload.toEntity());
        return new WebhookApiResponsePayload(webHooks);
    }
}
