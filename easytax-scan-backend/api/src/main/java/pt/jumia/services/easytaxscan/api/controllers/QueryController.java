package pt.jumia.services.easytaxscan.api.controllers;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.easytaxscan.api.payloads.request.*;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.*;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.QueryApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.QueryPreviewResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.SubQueryApiResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.filter.QueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.QuerySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.CreateQueryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ReadQueryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.UpdateQueryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ValidateDataSourceQuery;
import pt.jumia.services.easytaxscan.domain.usecases.subqueries.ReadSubQueryUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/queries")
@Slf4j
@RequiredArgsConstructor
public class QueryController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final JsonUtils jsonUtils;
    private final CreateQueryUseCase createQueryUseCase;
    private final ReadQueryUseCase readQueryUseCase;
    private final ReadSubQueryUseCase readSubQueryUseCase;
    private final UpdateQueryUseCase updateQueryUseCase;
    private final PaginationService paginationService;
    private final ValidateDataSourceQuery validateDataSourceQueryUseCase;


    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public QueryApiResponsePayload create(@Valid @RequestBody QueryApiRequestPayload payload)
            throws Exception {
        log.info("Creating new Query: {} for user with identifier {}", jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());

        validateUserAccessUseCase.checkCanManageQuery(RequestContext.getUser());
        Query query = createQueryUseCase.execute(payload.toEntity());

        return new QueryApiResponsePayload(query);
    }

    @GetMapping(value = "/{id}")
    public QueryApiResponsePayload fetchById(@PathVariable(value = "id") Long id)
            throws UserForbiddenException {
        log.info("Fetching new Query: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(id), RequestContext.getUsername());
        validateUserAccessUseCase.checkCanAccessQuery(RequestContext.getUser());
        Query query = readQueryUseCase.findById(id);
        return new QueryApiResponsePayload(query);

    }

    @GetMapping
    public PageResponsePayload<List<QueryApiResponsePayload>> fetch(HttpServletRequest request,
                                                                    @Valid QueryFiltersApiRequestPayload apiPayload,
                                                                    @Valid QuerySortFiltersApiRequestPayload sortFilter,
                                                                    PageFiltersRequestPayload pageFilter) throws UserForbiddenException {

        log.info("Fetching all Queries for user with identifier {}", RequestContext.getUsername());
        validateUserAccessUseCase.checkCanAccessQuery(RequestContext.getUser());
        QueryFilters filters = apiPayload.toEntity();
        QuerySortFilters sortFilters = sortFilter.toEntity();
        PageFilters pageFilters = pageFilter.toEntity();
        Long count = readQueryUseCase.executeCount(filters);
        List<QueryApiResponsePayload> results = readQueryUseCase.execute(filters, sortFilters, pageFilters)
                .stream().map(QueryApiResponsePayload::new).collect(Collectors.toList());
        return paginationService.buildPageResponsePayload(request, pageFilters, results, count);
    }

    @PutMapping(value = "/{id}")
    public QueryApiResponsePayload update(@PathVariable(value = "id") Long id, @Valid @RequestBody QueryApiRequestPayload payload)
            throws Exception {
        log.info("update new Query: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        validateUserAccessUseCase.checkCanManageQuery(RequestContext.getUser());
        Query query = updateQueryUseCase.execute(id, payload.toEntity());
        return new QueryApiResponsePayload(query);

    }

    @GetMapping(value = "/{id}/sub-queries")
    public PageResponsePayload<List<SubQueryApiResponsePayload>> fetchByIdSubQuery(
            HttpServletRequest request,
            @PathVariable("id") Long id,
            SubQueryFiltersApiRequestPayload apiPayload,
            SubQuerySortFiltersApiRequestPayload sortFilter,
            PageFiltersRequestPayload pageFilter) throws UserForbiddenException {

        log.info("Fetching SubQueries for Query ID: {} for user with identifier {}", id, RequestContext.getUsername());
        validateUserAccessUseCase.checkCanAccessQuery(RequestContext.getUser());
        SubQueryFilters subQueryFilters = apiPayload.toEntity();
        SubQuerySortFilters subQuerySortFilters = sortFilter.toEntity();
        PageFilters pageFilters = pageFilter.toEntity();
        long count = readSubQueryUseCase.executeCount(id, subQueryFilters);
        List<SubQueryApiResponsePayload> results = readSubQueryUseCase
                .execute(id, subQueryFilters, subQuerySortFilters, pageFilters)
                .stream()
                .map(SubQueryApiResponsePayload::new)
                .collect(Collectors.toList());

        return paginationService.buildPageResponsePayload(request, pageFilters, results, count);
    }


    @GetMapping(value = "/{id}/placeholders")
    public List<String> fetchFields(@PathVariable(value = "id") Long id)
            throws UserForbiddenException {
        log.info("Fetching available fields of Query for Id : {} for user with identifier {}",
                jsonUtils.toJsonOrNull(id), RequestContext.getUsername());
        validateUserAccessUseCase.checkCanAccessQuery(RequestContext.getUser());
        return readQueryUseCase.fetchFields(id);
    }

    @PostMapping(value = "/preview")
    public QueryPreviewResponsePayload getQueriesPreview(@RequestBody QueryPreviewRequestPayload payload) {
        validateUserAccessUseCase.checkCanManageQuery(RequestContext.getUser());
        QueryPreviewResponsePayload queryPreviewResponsePayload = QueryPreviewResponsePayload.builder().success(true).build();
        try {
            List<Map<String, Object>> result = validateDataSourceQueryUseCase.constructPreviewResponse(payload.toEntity());
            queryPreviewResponsePayload.setResult(CollectionUtils.isEmpty(result) ? null : result.get(0));
        } catch (Exception e) {
            log.error("Error previewing query: {}", ExceptionUtils.getStackTrace(e));
            queryPreviewResponsePayload = QueryPreviewResponsePayload.builder()
                    .success(false)
                    .error("Failed to preview query: " + (e.getMessage() != null ? e.getMessage() : e.getCause()))
                    .build();
        }
        return queryPreviewResponsePayload;
    }


}
