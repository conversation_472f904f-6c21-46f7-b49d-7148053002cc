package pt.jumia.services.easytaxscan.api.utils;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;

import java.util.List;

/**
 * A service to take care about pagination concerns, like building the response payload.
 */
@Service
public class PaginationService {

    public <E> PageResponsePayload<List<E>> buildPageResponsePayload(
            HttpServletRequest request, PageFilters pageFilters, List<E> results, Long count) {

        // work around: the query is returning one more entry
        // in order to verify if there is a next page
        boolean hasNextPage = false;
        if (results.size() > pageFilters.getSize()) {
            hasNextPage = true;
            results.remove(results.size() - 1);
        }

        String baseUrl = request.getRequestURL().substring(
                0, request.getRequestURL().length() - request.getRequestURI().length()) + request.getContextPath();
        String endpoint = request.getQueryString() == null ? request.getRequestURI() :
                request.getRequestURI() + "?" + request.getQueryString();

        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromUriString(endpoint);
        urlBuilder.replaceQueryParam("page", pageFilters.getPage() - 1);
        String previousPage = pageFilters.getPage() > 1 ? urlBuilder.build().toUriString() : null;

        urlBuilder.replaceQueryParam("page", pageFilters.getPage() + 1);
        String nextPage = hasNextPage ? urlBuilder.build().toUriString() : null;

        return new PageResponsePayload<>(
                new PageResponsePayload.Links(baseUrl, previousPage, nextPage),
                pageFilters.getPage(), pageFilters.getSize(), count, results);
    }
}
