package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogFilters;

import java.time.LocalDateTime;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ExecutionLogFiltersApiRequestPayload {

    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdFrom;
    @DateTimeFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdTo;
    private Long scanId;
    @ValidEnumValue(required = false, enumClass = ExecutionLog.Status.class)
    private String status;

    public ExecutionLogFilters toEntity() {
        return new ExecutionLogFilters().toBuilder()
                .status(this.status == null ? null : ExecutionLog.Status.valueOf(this.status))
                .scanId(scanId)
                .createdFrom(createdFrom)
                .createdTo(createdTo)
                .build();
    }
}
