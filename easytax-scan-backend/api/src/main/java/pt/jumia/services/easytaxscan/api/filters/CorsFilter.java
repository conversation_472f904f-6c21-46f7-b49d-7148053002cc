package pt.jumia.services.easytaxscan.api.filters;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.jumia.services.easytaxscan.domain.properties.ApiProperties;

import java.io.IOException;

@RequiredArgsConstructor
public class CorsFilter implements Filter {

    private static final Logger LOGGER = LoggerFactory.getLogger(CorsFilter.class);

    private final ApiProperties apiProperties;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse res, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;

        String originHeader = httpRequest.getHeader("Origin");

        HttpServletResponse response = (HttpServletResponse) res;
        if (originHeader != null && apiProperties.getAllowedDomains().contains(originHeader)) {
            response.setHeader("Access-Control-Allow-Origin", sanitizeCarriageReturns(originHeader));
        } else {
            LOGGER.debug("Origin not allowed: {} according to configured domains: {}", originHeader, apiProperties.getAllowedDomains());
        }
        response.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, DELETE, PATCH");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, Content-Encoding");
        response.setHeader("Access-Control-Allow-Credentials", "true");

        chain.doFilter(request, res);
    }

    @Override
    public void destroy() {

    }

    private String sanitizeCarriageReturns(String value) {
        int idxR = value.indexOf('\r');
        int idxN = value.indexOf('\n');

        if (idxN >= 0 || idxR >= 0) {
            if (idxN > idxR) {
                // cut off the part after the LF
                value = value.substring(0, idxN - 1);
            } else {
                // cut off the part after the CR
                value = value.substring(0, idxR - 1);
            }
        }
        return value;
    }
}
