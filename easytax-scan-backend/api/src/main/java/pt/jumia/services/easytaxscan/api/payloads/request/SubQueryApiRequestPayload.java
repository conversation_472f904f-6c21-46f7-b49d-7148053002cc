package pt.jumia.services.easytaxscan.api.payloads.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;

@Data
@NoArgsConstructor
public class SubQueryApiRequestPayload {

    @NotNull
    private Long subQueryId;

    @NotEmpty
    private String mainQueryColumn;

    @NotEmpty
    private String subQueryColumn;

    @NotNull
    @ValidEnumValue(required = true, enumClass = SubQuery.Status.class)
    private String status;

    private Boolean isList;


    public SubQueryApiRequestPayload(SubQuery subQuery) {
        this.subQueryId = subQuery.getSubQuery().getId();
        this.mainQueryColumn = subQuery.getMainQueryColumn();
        this.subQueryColumn = subQuery.getSubQueryColumn();
        this.status = subQuery.getStatus().name();
        this.isList = subQuery.getIsList();
    }

    public SubQuery toEntity() {
        return SubQuery.builder()
                .subQuery(Query.builder().id(subQueryId).build())
                .mainQueryColumn(mainQueryColumn)
                .subQueryColumn(subQueryColumn)
                .status(SubQuery.Status.valueOf(status))
                .isList(isList)
                .build();
    }
}
