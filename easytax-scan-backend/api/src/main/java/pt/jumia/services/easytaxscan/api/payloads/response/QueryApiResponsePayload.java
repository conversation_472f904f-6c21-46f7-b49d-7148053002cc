package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.domain.entities.Query;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class QueryApiResponsePayload {

    private Long id;

    private String sql;

    private String code;

    private String description;

    private Integer pageSize;

    private String paginationField;

    private DataSourceApiResponsePayload dataSource;

    private String sampleResult;

    private String createdBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;

    private String updatedBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;

    // Constructor to initialize from Queries domain model
    public QueryApiResponsePayload(Query queries) {
        this.id = queries.getId();
        this.sql = queries.getSql();
        this.code = queries.getCode();
        this.description = queries.getDescription();
        this.pageSize = queries.getPageSize();
        this.paginationField = queries.getPaginationField();
        this.dataSource = new DataSourceApiResponsePayload(queries.getDataSource());
        this.sampleResult = queries.getSampleResult();
        this.createdBy = queries.getCreatedBy();
        this.createdAt = queries.getCreatedAt();
        this.updatedBy = queries.getUpdatedBy();
        this.updatedAt = queries.getUpdatedAt();
    }

    // Convert back to Queries domain entity
    public Query toEntity() {
        return Query.builder()
                .id(id)
                .sql(sql)
                .code(code)
                .description(description)
                .pageSize(pageSize)
                .paginationField(paginationField)
                .dataSource(dataSource.toEntity())
                .sampleResult(sampleResult)
                .createdBy(createdBy)
                .createdAt(createdAt)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .build();
    }
}
