package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.domain.entities.Scan;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ScanApiResponsePayload {

    private Long id;

    private QueryApiResponsePayload query;

    private String cronExpression;

    private String code;

    private Scan.Status status;

    private Scan.Mode mode;

    private String description;

    private String mapping;

    private String sidColumn;

    private CountryApiResponsePayload country;

    private String createdBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;

    private String updatedBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;


    public ScanApiResponsePayload(Scan scan) {
        this.id = scan.getId();
        this.query = new QueryApiResponsePayload(scan.getQuery());
        this.cronExpression = scan.getCronExpression();
        this.code = scan.getCode();
        this.status = scan.getStatus();
        this.mode = scan.getMode();
        this.description = scan.getDescription();
        this.mapping = scan.getMapping();
        this.country = scan.getCountry() != null ? new CountryApiResponsePayload(scan.getCountry()) : null;
        this.sidColumn = scan.getSidColumn();
        this.createdBy = scan.getCreatedBy();
        this.createdAt = scan.getCreatedAt();
        this.updatedBy = scan.getUpdatedBy();
        this.updatedAt = scan.getUpdatedAt();
    }

    public Scan toEntity() {
        return Scan.builder()
                .id(id)
                .cronExpression(cronExpression)
                .query(query.toEntity())
                .code(code)
                .status(status)
                .mode(mode)
                .description(description)
                .mapping(mapping)
                .sidColumn(sidColumn)
                .country(country != null ? country.toEntity() : null)
                .createdBy(createdBy)
                .createdAt(createdAt)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .build();
    }
}
