package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.QueryParse;

import java.util.List;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueryParseApiResponsePayload {

    private List<String> columns;

    private List<String> arguments;

    private String query;


    public QueryParseApiResponsePayload(QueryParse queryParse) {
        this.columns = queryParse.getColumns();
        this.arguments = queryParse.getArguments();
    }

    public QueryParse toEntity() {
        return QueryParse.builder()
                .query(query)
                .build();
    }
}
