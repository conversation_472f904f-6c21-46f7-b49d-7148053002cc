package pt.jumia.services.easytaxscan.api.payloads.request;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.QueryParse;

@Data
@NoArgsConstructor
public class QueryParseApiRequestPayload {

    @NotEmpty
    private String query;

    public QueryParseApiRequestPayload(QueryParse queryParse) {
        this.query = queryParse.getQuery();
    }

    public QueryParse toEntity() {
        return QueryParse.builder()
                .query(query)
                .build();
    }
}
