package pt.jumia.services.easytaxscan.api.config;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import pt.jumia.services.easytaxscan.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.easytaxscan.domain.exceptions.*;

import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Registers the error handlers. All the exceptions that are thrown by the controllers can be dealt with in here
 */
@ControllerAdvice
@Slf4j
public class RestErrorHandler extends ResponseEntityExceptionHandler {

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex,
                                                                  HttpHeaders headers,
                                                                  HttpStatusCode code,
                                                                  WebRequest request) {

        List<CodedErrorResponsePayload> errorFields = ex.getBindingResult().getFieldErrors().stream()
                .map(error -> CodedErrorResponsePayload.forSingleError(
                        ErrorCode.INVALID_PAYLOAD,
                        String.format("'%s' rejected for field '%s': %s",
                                error.getRejectedValue() == null ? null : error.getRejectedValue().toString(),
                                error.getField(),
                                error.getDefaultMessage())))
                .collect(Collectors.toList());

        if (errorFields.isEmpty()) {
            ObjectError globalError = ex.getBindingResult().getGlobalError();
            CodedErrorResponsePayload errorPayload = CodedErrorResponsePayload.forSingleError(
                    ErrorCode.INVALID_PAYLOAD,
                    globalError != null ? globalError.getDefaultMessage() : null
            );
            return new ResponseEntity<>(errorPayload, code);
        }

        CodedErrorResponsePayload errorPayload = CodedErrorResponsePayload.forErrorWithCauses(
                ErrorCode.INVALID_PAYLOAD,
                "Invalid payload",
                errorFields
        );
        return new ResponseEntity<>(errorPayload, code);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex,
                                                                  HttpHeaders headers,
                                                                  HttpStatusCode code,
                                                                  WebRequest request) {
        //exception thrown by Spring when JSON format does not match the expected payload
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.INVALID_PAYLOAD, ex.getMessage());
        return new ResponseEntity<>(errorResponse, code);
    }

    @ExceptionHandler(value = ConstraintViolationException.class)
    public ResponseEntity<CodedErrorResponsePayload> handleConstraintValidation(ConstraintViolationException exception) {

        CodedErrorResponsePayload errorPayload = CodedErrorResponsePayload.forErrorWithCauses(
                ErrorCode.INVALID_PAYLOAD,
                "Invalid payload",
                exception.getConstraintViolations().stream()
                        .map(constraintViolation -> CodedErrorResponsePayload.forSingleError(
                                ErrorCode.INVALID_PAYLOAD,
                                String.format("'%s' rejected for field '%s': %s",
                                        constraintViolation.getInvalidValue(),
                                        constraintViolation.getPropertyPath().toString(),
                                        constraintViolation.getMessage()
                                )))
                        .collect(Collectors.toList()));
        return new ResponseEntity<>(errorPayload, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<Object> handleBindException(BindException ex, WebRequest request) {
        CodedErrorResponsePayload errorPayload = CodedErrorResponsePayload.forErrorWithCauses(
                ErrorCode.INVALID_PAYLOAD,
                "Invalid payload",
                ex.getFieldErrors().stream()
                        .map(fieldError -> CodedErrorResponsePayload.forSingleError(
                                ErrorCode.INVALID_PAYLOAD,
                                String.format("'%s' rejected for field '%s': %s",
                                        fieldError.getRejectedValue(),
                                        fieldError.getField(),
                                        fieldError.getDefaultMessage()
                                )))
                        .collect(Collectors.toList()));
        return new ResponseEntity<>(errorPayload, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = DataIntegrityViolationException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleDataIntegrityViolationException(DataIntegrityViolationException e)
            throws Exception {

        log.error("DataIntegrityViolationException ", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.DATA_INTEGRITY_VIOLATION,
                e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler
    private ResponseEntity<CodedErrorResponsePayload> handleGeneric(Exception e) {

        log.error("Unhandled exception", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(
                ErrorCode.INTERNAL_SERVER_ERROR,
                String.format("Unexpected exception: %s", e.getMessage()));
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = UserForbiddenException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleUserForbiddenException(UserForbiddenException e, HttpServletResponse response) {

        log.error("UserForbiddenException ", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(value = NotFoundException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleNotFoundException(NotFoundException e) {

        log.error("EntityNotFound ", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(value = RecordAlreadyExistsException.class)
    private ResponseEntity<CodedErrorResponsePayload> recordAlreadyExistException(RecordAlreadyExistsException e) {

        log.error("Record Already Present  ", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = ConflictOperationException.class)
    private ResponseEntity<CodedErrorResponsePayload> conflictOperationException(ConflictOperationException e) {

        log.error("Unable to perform operation  ", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    public ResponseEntity<Object> handleIllegalArgumentException(IllegalArgumentException e) {

        log.debug(e.getMessage());
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.BAD_REQUEST, e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = BadRequestException.class)
    public ResponseEntity<Object> handleBadRequestException(BadRequestException e) {

        log.debug(e.getMessage());
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.BAD_REQUEST, e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = ServiceUnavailableException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleServiceUnavailableException(ServiceUnavailableException e) {
        log.debug(e.getMessage());
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.SERVICE_UNAVAILABLE);
    }
}
