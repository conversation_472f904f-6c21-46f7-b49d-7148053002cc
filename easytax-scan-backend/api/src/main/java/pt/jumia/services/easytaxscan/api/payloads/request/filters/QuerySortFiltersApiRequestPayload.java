package pt.jumia.services.easytaxscan.api.payloads.request.filters;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.filter.QuerySortFilters;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class QuerySortFiltersApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = Query.SortingFields.class)
    private String orderField = Query.SortingFields.ID.name();

    @ValidEnumValue(required = true, enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public QuerySortFilters toEntity() {
        return QuerySortFilters.builder()
                .field(Query.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection)).build();
    }
}
