package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.domain.entities.Country;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class CountryApiResponsePayload {

    private Long id;

    private String countryCode;

    private String countryName;

    private String createdBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;

    private String updatedBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;

    public CountryApiResponsePayload(Country country) {
        this.id = country.getId();
        this.countryCode = country.getCountryCode();
        this.countryName = country.getCountryName();
        this.createdBy = country.getCreatedBy();
        this.createdAt = country.getCreatedAt();
        this.updatedBy = country.getUpdatedBy();
        this.updatedAt = country.getUpdatedAt();
    }

    public Country toEntity() {
        return Country.builder()
                .id(id)
                .countryCode(countryCode)
                .countryName(countryName)
                .createdBy(createdBy)
                .createdAt(createdAt)
                .updatedBy(updatedBy)
                .updatedAt(updatedAt)
                .build();
    }
}
