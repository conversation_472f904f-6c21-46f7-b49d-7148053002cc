package pt.jumia.services.easytaxscan.api.payloads.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.api.groups.CreateGroup;
import pt.jumia.services.easytaxscan.domain.entities.BackgroundJobs;
import pt.jumia.services.easytaxscan.domain.entities.RetryJob;

@Data
@NoArgsConstructor
public class RetryJobApiRequestPayload {

    @NotEmpty(groups = CreateGroup.class)
    @NotNull(groups = CreateGroup.class)
    private String job;

    @NotEmpty
    @NotNull
    private String cronExpression;


    public RetryJobApiRequestPayload(RetryJob retryJob) {
        this.job = retryJob.getJob();
        this.cronExpression = retryJob.getCronExpression();
    }

    public RetryJob toEntity() {
        return RetryJob.builder()
                .job(String.valueOf(BackgroundJobs.valueOf(job)))
                .cronExpression(cronExpression)
                .build();
    }
}
