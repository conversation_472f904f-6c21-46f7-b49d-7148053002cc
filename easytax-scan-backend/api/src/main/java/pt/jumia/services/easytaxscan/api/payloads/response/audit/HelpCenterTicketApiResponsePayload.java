package pt.jumia.services.easytaxscan.api.payloads.response.audit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.HelpCenterTicket;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HelpCenterTicketApiResponsePayload {
    private String link;
    private String justification;

    public HelpCenterTicketApiResponsePayload(HelpCenterTicket helpCenterTicket) {
        this.link = helpCenterTicket.getLink();
        this.justification = helpCenterTicket.getJustification();
    }
}
