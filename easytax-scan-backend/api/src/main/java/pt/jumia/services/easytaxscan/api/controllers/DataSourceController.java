package pt.jumia.services.easytaxscan.api.controllers;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import pt.jumia.services.easytaxscan.api.groups.CreateGroup;
import pt.jumia.services.easytaxscan.api.groups.UpdateGroup;
import pt.jumia.services.easytaxscan.api.payloads.request.*;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.DataSourceFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.DataSourceSortFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.PageFiltersRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.DataSourceApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.HealthStatus;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.exceptions.RecordAlreadyExistsException;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.datasource.*;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/datasources")
@Slf4j
@RequiredArgsConstructor
public class DataSourceController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final JsonUtils jsonUtils;
    private final CreateDataSourceUseCase createDataSourceUseCase;
    private final ReadDataSourceUseCase readDataSourceUseCase;
    private final UpdateDataSourceUseCase updateDataSourceUseCase;
    private final DeleteDataSourceUseCase deleteDataSourceUseCase;
    private final ValidateDataSourceUseCase validateDataSourceUseCase;
    private final PaginationService paginationService;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public DataSourceApiResponsePayload create
            (@RequestBody @Validated(CreateGroup.class) @Valid DataSourceApiRequestPayload payload)
            throws UserForbiddenException, RecordAlreadyExistsException {
        validateUserAccessUseCase.checkCanManageDataSource(RequestContext.getUser());
        log.info("Creating new DataSource: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        return new DataSourceApiResponsePayload(createDataSourceUseCase.execute(payload.toEntity()));
    }

    @PutMapping(value = "/{id}")
    public DataSourceApiResponsePayload update(@PathVariable(value = "id") Long id,
                                               @RequestBody @Validated(UpdateGroup.class) DataSourceApiRequestPayload payload)
            throws UserForbiddenException, NotFoundException {
        validateUserAccessUseCase.checkCanManageDataSource(RequestContext.getUser());
        log.info("Updating DataSource: {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        return new DataSourceApiResponsePayload(updateDataSourceUseCase.execute(id, payload.toEntity()));
    }

    @GetMapping(value = "/{id}")
    public DataSourceApiResponsePayload fetchById(@PathVariable(value = "id") Long id)
            throws UserForbiddenException {
        validateUserAccessUseCase.checkCanAccessDataSource(RequestContext.getUser());
        log.info("Fetching DataSource {} for user with identifier {}",
                id, RequestContext.getUsername());
        return new DataSourceApiResponsePayload(readDataSourceUseCase.execute(id));
    }

    @GetMapping
    public PageResponsePayload<List<DataSourceApiResponsePayload>> fetch(HttpServletRequest request,
                                                                         @Valid DataSourceFiltersApiRequestPayload apiPayload,
                                                                         @Valid DataSourceSortFiltersApiRequestPayload sortFilter,
                                                                         PageFiltersRequestPayload pageFilter) throws UserForbiddenException {
        validateUserAccessUseCase.checkCanAccessDataSource(RequestContext.getUser());
        log.info("Fetching all DataSources for user with identifier {}", RequestContext.getUsername());
        DataSourceFilters filters = apiPayload.toEntity();
        DataSourceSortFilters sortFilters = sortFilter.toEntity();
        PageFilters pageFilters = pageFilter.toEntity();
        Long count = readDataSourceUseCase.executeCount(filters);

        List<DataSourceApiResponsePayload> results = readDataSourceUseCase.execute(filters, sortFilters, pageFilters)
                .stream().map(DataSourceApiResponsePayload::new).collect(Collectors.toList());
        return paginationService.buildPageResponsePayload(request, pageFilters, results, count);
    }

    @DeleteMapping(value = "/{id}")
    public void delete(@PathVariable(value = "id") Long id)
            throws UserForbiddenException, NotFoundException {
        validateUserAccessUseCase.checkCanManageDataSource(RequestContext.getUser());
        log.info("Deleting DataSource: {} for user with identifier {}", id
                , RequestContext.getUsername());
        deleteDataSourceUseCase.execute(id);
    }

    @PostMapping("/{code}/health-check")
    public HealthStatus healthcheck
            (@PathVariable(value = "code") String code,
             @RequestBody HealthCheckParamsApiRequestPayload payload)
            throws UserForbiddenException, RecordAlreadyExistsException {
        validateUserAccessUseCase.checkCanManageDataSource(RequestContext.getUser());
        log.info("Performing health check on DataSource : {} for user: {} for Country: {}",
                code, RequestContext.getUsername(), payload.getCountryCode());
        return validateDataSourceUseCase.execute(code, payload.toEntity());
    }


}
