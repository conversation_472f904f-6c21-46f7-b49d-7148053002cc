package pt.jumia.services.easytaxscan.api.payloads.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class QueryPreviewResponsePayload {
    private boolean success;
    private String error;
    private Map<String, Object> result;
}
