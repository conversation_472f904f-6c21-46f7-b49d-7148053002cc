package pt.jumia.services.easytaxscan.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.aig.aigx.commons.dates.DateUtil;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.Mode;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class DocumentApiResponsePayload {

    private ExecutionLogApiResponsePayload executionLog;

    private String sid;

    private Long id;

    private String queryData;

    private String requestPayload;

    private String status;

    private String mode;

    private String createdBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime createdAt;

    private String updatedBy;

    @JsonFormat(pattern = DateUtil.ISO8601_DATE_TIME_UTC_FORMAT)
    private LocalDateTime updatedAt;


    public DocumentApiResponsePayload(Document document) {
        this.id = document.getId();
        this.executionLog = new ExecutionLogApiResponsePayload(document.getExecutionLog());
        this.sid = document.getSid();
        this.status = document.getStatus().name();
        this.mode = document.getMode().name();
        this.requestPayload = document.getRequestPayload();
        this.queryData = document.getQueryData();
        this.createdBy = document.getCreatedBy();
        this.createdAt = document.getCreatedAt();
        this.updatedBy = document.getUpdatedBy();
        this.updatedAt = document.getUpdatedAt();
    }

    public Document toEntity() {
        return Document
                .builder()
                .id(id)
                .sid(sid)
                .status(Document.Status.valueOf(status))
                .mode(Mode.valueOf(mode))
                .queryData(queryData)
                .requestPayload(requestPayload)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}
