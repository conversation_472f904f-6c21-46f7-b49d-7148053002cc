package pt.jumia.services.easytaxscan.api.controllers;


import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.easytaxscan.api.payloads.request.MappingParseApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.QueryParseApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.QueryParseApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.QueryParse;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.mapping.MappingParseUtilUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.QueryParseUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

@RestController
@RequestMapping(value = "/api/util")
@Slf4j
@RequiredArgsConstructor
public class QueryUtilController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final JsonUtils jsonUtils;
    private final QueryParseUseCase queryParseUseCase;
    private final MappingParseUtilUseCase mappingParseUtilUseCase;


    @PostMapping(value = "/query/parse")
    public QueryParseApiResponsePayload queryParse(@Valid @RequestBody QueryParseApiRequestPayload payload)
            throws Exception {
        log.info("Query parse request received : {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());
        QueryParse queryParse = queryParseUseCase.execute(payload.toEntity());
        return new QueryParseApiResponsePayload(queryParse);
    }

    @PostMapping(value = "/mapping/parse")
    public String parseMapping(@Valid @RequestBody MappingParseApiRequestPayload payload)
            throws Exception {
        log.info("Query parse request received : {} for user with identifier {}",
                jsonUtils.toJsonOrNull(payload), RequestContext.getUsername());
        validateUserAccessUseCase.checkCanAccess(RequestContext.getUser());
        String result = mappingParseUtilUseCase.execute(payload.toEntity());
        return result;
    }

}
