package pt.jumia.services.easytaxscan.api.controllers.audit;


import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.easytaxscan.api.payloads.request.filters.PageFiltersRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.request.audit.AuditedEntitySortFiltersApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.audit.AuditApiResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.audit.ReadQueryAuditUseCase;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping(value = "/api/audit/query")
@Slf4j
@RequiredArgsConstructor
public class QueryAuditController {

    private final ValidateUserAccessUseCase validateUserAccessUseCase;
    private final ReadQueryAuditUseCase readQueryAuditUseCase;
    private final PaginationService paginationService;


    @GetMapping("/{queryId}")
    public PageResponsePayload<List<AuditApiResponsePayload>> fetch(@PathVariable(value = "queryId") Long queryId,
                                                                    HttpServletRequest httpServletRequest,
                                                                    @Valid PageFiltersRequestPayload pageFiltersApiRequestPayload,
                                                                    @Valid AuditedEntitySortFiltersApiRequestPayload auditSortFiltersRequest)
            throws UserForbiddenException {
        log.info("Fetching query history user with identifier {}",
                RequestContext.getUsername());
       validateUserAccessUseCase.checkCanAccessQuery(RequestContext.getUser());
        PageFilters pageFilters = pageFiltersApiRequestPayload.toEntity();
        AuditedEntitySortFilters sortFilters = auditSortFiltersRequest.toEntity();
        List<AuditApiResponsePayload> list = readQueryAuditUseCase.executeById(queryId, sortFilters, pageFilters).stream()
                .map(AuditApiResponsePayload::new)
                .toList();
        long total = readQueryAuditUseCase.executeCountByQueryId(queryId);
        return paginationService.buildPageResponsePayload(
                httpServletRequest,
                pageFilters,
                list,
                total
        );
    }
}
