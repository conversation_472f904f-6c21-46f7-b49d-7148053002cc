package pt.jumia.services.easytaxscan.api.controllers;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;

import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import pt.jumia.services.easytaxscan.api.payloads.response.SettingApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeSettings;
import pt.jumia.services.easytaxscan.domain.entities.filter.SettingFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SettingSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.ErrorCode;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.setting.CreateSettingUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.setting.DeleteSettingUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.setting.ReadSettingUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.setting.UpdateSettingUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

@WebMvcTest(SettingController.class)
public class SettingControllerTest extends BaseControllerTest {

    private static final String BASE_ENDPOINT = "/api/settings";
    private static final long SETTING_ID = 999L;

    private static final Setting SETTING = FakeSettings.SETTING_DEFAULT.toBuilder()
        .id(SETTING_ID)
        .build();

    @MockitoBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;
    @MockitoBean
    private ReadSettingUseCase readSettingUseCase;
    @MockitoBean
    private CreateSettingUseCase createSettingUseCase;
    @MockitoBean
    private JsonUtils jsonUtils;
    @MockitoBean
    private UpdateSettingUseCase updateSettingUseCase;
    @MockitoBean
    private DeleteSettingUseCase deleteSettingUseCase;

    @BeforeEach
    public void setup() {

        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    public void fetchAll_success() throws Exception {
        // prepare

        SettingFilters settingFilters = SettingFilters.builder()
            .property("property1")
            .overrideKey("some-override")
            .type(Setting.Type.OVERRIDE)
            .value("bar")
            .build();

        when(readSettingUseCase.execute(any(), any()))
            .thenReturn(FakeSettings.ALL_DEFAULT);

        // execute
        List<SettingApiResponsePayload> response = requestList(
            get(BASE_ENDPOINT)
                .param("property", "property1")
                .param("overrideKey", "some-override")
                .param("type", Setting.Type.OVERRIDE.toString())
                .param("value", "bar"),
            HttpStatus.OK,
            SettingApiResponsePayload.class);

        // verify
        assertThat(response)
            .hasSize(FakeSettings.ALL_DEFAULT.size());
        verify(validateUserAccessUseCase).checkCanAccessSetting(REQUEST_USER);
        verify(readSettingUseCase).execute(eq(settingFilters), any());
    }

    @Test
    public void fetchAllWithDefaultFilters_success() throws Exception {
        // prepare

        SettingFilters settingFilters = SettingFilters.builder()
            .build();

        SettingSortFilters settingSortFilters = SettingSortFilters.builder()
            .build();

        when(readSettingUseCase.execute(any(), any()))
            .thenReturn(FakeSettings.ALL_DEFAULT);

        // execute
        List<SettingApiResponsePayload> response = requestList(
            get(BASE_ENDPOINT),
            HttpStatus.OK,
            SettingApiResponsePayload.class);

        // verify
        assertThat(response)
            .hasSize(FakeSettings.ALL_DEFAULT.size());
        verify(validateUserAccessUseCase).checkCanAccessSetting(REQUEST_USER);
        verify(readSettingUseCase).execute(settingFilters, settingSortFilters);
    }

    @Test
    public void fetchAll_forbidden() throws Exception {
        // prepare
        doThrow(UserForbiddenException.create(""))
            .when(validateUserAccessUseCase).checkCanAccessSetting(REQUEST_USER);

        // execute
        CodedErrorResponsePayload response = request(
            get(BASE_ENDPOINT),
            HttpStatus.FORBIDDEN,
            CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
            .extracting(CodedErrorResponsePayload::getCode)
            .isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(validateUserAccessUseCase).checkCanAccessSetting(REQUEST_USER);
        verifyNoInteractions(readSettingUseCase);
    }

    @Test
    public void fetchById_success() throws Exception {
        // prepare
        when(readSettingUseCase.findById(SETTING_ID)).thenReturn(SETTING);

        // execute
        SettingApiResponsePayload response = request(
            get(BASE_ENDPOINT + "/" + SETTING_ID),
            HttpStatus.OK,
            SettingApiResponsePayload.class);

        // verify
        assertEquals(SETTING.getId(), response.toEntity().getId());
        verify(validateUserAccessUseCase).checkCanAccessSetting(REQUEST_USER);
        verify(readSettingUseCase).findById(SETTING_ID);
    }

    @Test
    public void fetchById_forbidden() throws Exception {
        // prepare
        doThrow(UserForbiddenException.create(""))
            .when(validateUserAccessUseCase).checkCanAccessSetting(REQUEST_USER);

        // execute
        CodedErrorResponsePayload response = request(
            get(BASE_ENDPOINT + "/" + SETTING_ID),
            HttpStatus.FORBIDDEN,
            CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
            .extracting(CodedErrorResponsePayload::getCode)
            .isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(validateUserAccessUseCase).checkCanAccessSetting(REQUEST_USER);
        verifyNoInteractions(readSettingUseCase);
    }

    @Test
    public void fetchById_notFound() throws Exception {
        // prepare
        doThrow(NotFoundException.build(Setting.class, SETTING_ID))
            .when(readSettingUseCase).findById(SETTING_ID);

        // execute
        CodedErrorResponsePayload response = request(
            get(BASE_ENDPOINT + "/" + SETTING_ID),
            HttpStatus.NOT_FOUND,
            CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
            .extracting(CodedErrorResponsePayload::getCode)
            .isEqualTo(ErrorCode.ENTITY_NOT_FOUND.getCode());
        verify(validateUserAccessUseCase).checkCanAccessSetting(REQUEST_USER);
        verify(readSettingUseCase).findById(SETTING_ID);
    }

    @Test
    public void create_success() throws Exception {
        // prepare
        when(createSettingUseCase.execute(any())).thenReturn(SETTING);

        // execute
        SettingApiResponsePayload response = request(
            post(BASE_ENDPOINT),
            "settings/valid_setting_request.json",
            HttpStatus.CREATED,
            SettingApiResponsePayload.class);

        // verify
        assertEquals(SETTING, response.toEntity());
        verify(validateUserAccessUseCase).checkCanManageSetting(REQUEST_USER);
        verify(createSettingUseCase).execute(any());
    }

    @Test
    public void create_forbidden() throws Exception {
        // prepare
        doThrow(UserForbiddenException.create(""))
            .when(validateUserAccessUseCase).checkCanManageSetting(REQUEST_USER);

        // execute
        CodedErrorResponsePayload response = request(
            post(BASE_ENDPOINT),
            "settings/valid_setting_request.json",
            HttpStatus.FORBIDDEN,
            CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
            .extracting(CodedErrorResponsePayload::getCode)
            .isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(validateUserAccessUseCase).checkCanManageSetting(REQUEST_USER);
        verifyNoInteractions(createSettingUseCase);
    }

    @Test
    public void create_bad_request() throws Exception {
        // execute
        CodedErrorResponsePayload response = request(
            post(BASE_ENDPOINT),
            "settings/invalid_setting_request.json",
            HttpStatus.BAD_REQUEST,
            CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
            .extracting(CodedErrorResponsePayload::getCode)
            .isEqualTo(ErrorCode.INVALID_PAYLOAD.getCode());
        verifyNoInteractions(validateUserAccessUseCase);
        verifyNoInteractions(createSettingUseCase);
    }

    @Test
    public void create_invalid_enum_request() throws Exception {
        // execute
        CodedErrorResponsePayload response = request(
            post(BASE_ENDPOINT),
            "settings/invalid_enum_setting_request.json",
            HttpStatus.BAD_REQUEST,
            CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
            .extracting(CodedErrorResponsePayload::getCode)
            .isEqualTo(ErrorCode.INVALID_PAYLOAD.getCode());
        assertThat(response.getErrors())
            .hasSize(1)
            .containsExactlyInAnyOrder(
                CodedErrorResponsePayload.forSingleError(ErrorCode.INVALID_PAYLOAD, "'TEST' rejected for field 'type': Not a valid value!")
            );
        verifyNoInteractions(validateUserAccessUseCase);
        verifyNoInteractions(createSettingUseCase);
    }

    @Test
    public void update_success() throws Exception {
        // prepare
        when(updateSettingUseCase.execute(anyLong(), any())).thenReturn(SETTING);

        // execute
        SettingApiResponsePayload response = request(
            put(BASE_ENDPOINT + '/' + SETTING_ID),
            "settings/valid_setting_request.json",
            HttpStatus.OK,
            SettingApiResponsePayload.class);

        // verify
        assertEquals(SETTING, response.toEntity());
        verify(validateUserAccessUseCase).checkCanManageSetting(REQUEST_USER);
        verify(updateSettingUseCase).execute(anyLong(), any());
    }

    @Test
    public void update_forbidden() throws Exception {
        // prepare
        doThrow(UserForbiddenException.create(""))
            .when(validateUserAccessUseCase).checkCanManageSetting(REQUEST_USER);

        // execute
        CodedErrorResponsePayload response = request(
            put(BASE_ENDPOINT + '/' + SETTING_ID),
            "settings/valid_setting_request.json",
            HttpStatus.FORBIDDEN,
            CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
            .extracting(CodedErrorResponsePayload::getCode)
            .isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(validateUserAccessUseCase).checkCanManageSetting(REQUEST_USER);
        verifyNoInteractions(updateSettingUseCase);
    }

    @Test
    public void update_bad_request() throws Exception {
        // execute
        CodedErrorResponsePayload response = request(
            put(BASE_ENDPOINT + '/' + SETTING_ID),
            "settings/invalid_setting_request.json",
            HttpStatus.BAD_REQUEST,
            CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
            .extracting(CodedErrorResponsePayload::getCode)
            .isEqualTo(ErrorCode.INVALID_PAYLOAD.getCode());
        verifyNoInteractions(validateUserAccessUseCase);
        verifyNoInteractions(updateSettingUseCase);
    }

    @Test
    public void update_invalid_enum_request() throws Exception {
        // execute
        CodedErrorResponsePayload response = request(
            put(BASE_ENDPOINT + '/' + SETTING_ID),
            "settings/invalid_enum_setting_request.json",
            HttpStatus.BAD_REQUEST,
            CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
            .extracting(CodedErrorResponsePayload::getCode)
            .isEqualTo(ErrorCode.INVALID_PAYLOAD.getCode());
        assertThat(response.getErrors())
            .hasSize(1)
            .containsExactlyInAnyOrder(
                CodedErrorResponsePayload.forSingleError(ErrorCode.INVALID_PAYLOAD, "'TEST' rejected for field 'type': Not a valid value!")
            );
        verifyNoInteractions(validateUserAccessUseCase);
        verifyNoInteractions(updateSettingUseCase);
    }

    @Test
    public void delete_success() throws Exception {
        // prepare
        when(deleteSettingUseCase.execute(SETTING_ID)).thenReturn(SETTING);

        // execute
        SettingApiResponsePayload response = request(
            delete(BASE_ENDPOINT + '/' + SETTING_ID),
            HttpStatus.OK,
            SettingApiResponsePayload.class);

        // verify
        verify(validateUserAccessUseCase).checkCanManageSetting(REQUEST_USER);
        verify(deleteSettingUseCase).execute(SETTING_ID);
    }

    @Test
    public void delete_forbidden() throws Exception {
        // prepare
        doThrow(UserForbiddenException.create(""))
            .when(validateUserAccessUseCase).checkCanManageSetting(REQUEST_USER);

        // execute
        CodedErrorResponsePayload response = request(
            delete(BASE_ENDPOINT + '/' + SETTING_ID),
            HttpStatus.FORBIDDEN,
            CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
            .extracting(CodedErrorResponsePayload::getCode)
            .isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(validateUserAccessUseCase).checkCanManageSetting(REQUEST_USER);
        verifyNoInteractions(deleteSettingUseCase);
    }

}
