package pt.jumia.services.easytaxscan.api.controllers.audit;

import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import pt.jumia.services.easytaxscan.api.controllers.BaseControllerTest;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.audit.ReadScanAuditUseCase;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@ExtendWith(SpringExtension.class)
@WebMvcTest(ScanAuditController.class)
public class ScanAuditControllerTest extends BaseControllerTest {


    private static final String BASE_ENDPOINT = "/api/audit/scan";
    private static final List<AuditedEntity<Scan>> SCAN_AUDITED_ENTITIES = List.of(
            AuditedEntity.<Scan>builder()
                    .entity(FakeScan.SCAN_CREATE)
                    .revisionInfo(AuditedEntity.RevisionInfo.builder()
                            .email("<EMAIL>")
                            .datetime(LocalDateTime.now(ZoneOffset.UTC))
                            .build())
                    .operationType(AuditedEntity.OperationType.CREATE)
                    .auditedEntity(AuditedEntities.SCAN)
                    .build(),
            AuditedEntity.<Scan>builder()
                    .entity(FakeScan.SCAN_UPDATE)
                    .revisionInfo(AuditedEntity.RevisionInfo.builder()
                            .email("<EMAIL>")
                            .datetime(LocalDateTime.now(ZoneOffset.UTC))
                            .build())
                    .operationType(AuditedEntity.OperationType.UPDATE)
                    .auditedEntity(AuditedEntities.SCAN)
                    .build()
    );


    @MockitoBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockitoBean
    private ReadScanAuditUseCase readScanAuditUseCase;

    @MockitoBean
    private PaginationService paginationService;

    @Test
    public void fetchAll_scan_audits_success() throws Exception {
        when(readScanAuditUseCase.executeById(anyLong(), any(), any())).thenReturn(SCAN_AUDITED_ENTITIES);

        var pageResponsePayload = new PageResponsePayload<>(null, 1, 20, (long) SCAN_AUDITED_ENTITIES.size(), SCAN_AUDITED_ENTITIES);

        when(paginationService.buildPageResponsePayload(
                any(HttpServletRequest.class), any(), any(List.class), anyLong()))
                .thenReturn(pageResponsePayload);

        PageResponsePayload response = request(get(BASE_ENDPOINT + "/1"), HttpStatus.OK, PageResponsePayload.class);

        Assertions.assertEquals(SCAN_AUDITED_ENTITIES.size(), response.getTotal());
        verify(validateUserAccessUseCase, times(1)).checkCanAccessScan(any());
        verify(readScanAuditUseCase, times(1)).executeById(anyLong(), any(), any());
        verify(paginationService, times(1)).buildPageResponsePayload(any(), any(), any(), anyLong());
    }
}
