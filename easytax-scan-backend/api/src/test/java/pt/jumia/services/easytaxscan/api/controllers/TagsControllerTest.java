package pt.jumia.services.easytaxscan.api.controllers;

import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.easytaxscan.domain.usecases.tags.CreateTagsUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.tags.DeleteTagsUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.tags.ReadTagsUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.tags.UpdateTagsUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;
import pt.jumia.services.easytaxscan.domain.utils.ResourceLoader;

import java.util.Collections;

import static org.hamcrest.Matchers.containsString;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Tests the controller at API level.
 * In here you should test the API validations and return content
 *
 * Note that you need to mock all the dependencies of your controller
 */
@ExtendWith(SpringExtension.class)
@WebMvcTest(TagsController.class)
public class TagsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockitoBean
    private CreateTagsUseCase createTagsUseCase;

    @MockitoBean
    private ReadTagsUseCase readTagsUseCase;

    @MockitoBean
    private UpdateTagsUseCase updateTagsUseCase;

    @MockitoBean
    private DeleteTagsUseCase deleteTagsUseCase;

    @MockitoBean
    private JsonUtils jsonUtils;

    @Test
    public void testInvalidPayload_clientError() throws Exception {
        String payload = ResourceLoader.getStringFromFile("tags/invalid_tag.json");
        mockMvc
                .perform(post("/api/tags")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isBadRequest())
                .andReturn();
    }


    @Test
    public void testSimpleMapping_success() throws Exception {
        when(createTagsUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        String payload = ResourceLoader.getStringFromFile("tags/valid_tag.json");
        mockMvc
                .perform(post("/api/tags")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvalidContent() throws Exception {
        when(createTagsUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        mockMvc
                .perform(post("/api/tags")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("Invalid Payload"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.details",
                        containsString("JSON parse error: Unrecognized token 'Invalid'")))
                .andReturn();
    }

    @Test
    public void testConflict() throws Exception {
        when(createTagsUseCase.execute(any()))
                .thenThrow(new ConstraintViolationException("Test error", Collections.emptySet()));

        String payload = ResourceLoader.getStringFromFile("tags/valid_tag.json");
        mockMvc
                .perform(post("/api/tags")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isInternalServerError())
                .andReturn();
    }
}
