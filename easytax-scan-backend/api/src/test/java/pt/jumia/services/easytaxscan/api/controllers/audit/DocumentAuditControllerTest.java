package pt.jumia.services.easytaxscan.api.controllers.audit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.easytaxscan.api.controllers.BaseControllerTest;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.HelpCenterTicket;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDocument;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.audit.ReadDocumentAuditUseCase;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@ExtendWith(SpringExtension.class)
@WebMvcTest(DocumentAuditController.class)
public class DocumentAuditControllerTest extends BaseControllerTest {

    private static final String BASE_ENDPOINT = "/api/audit/document";
    private static final List<AuditedEntity<Document>> DOCUMENT_AUDITED_ENTITIES = List.of(
            AuditedEntity.<Document>builder()
                    .entity(FakeDocument.DOCUMENT_CREATE)
                    .revisionInfo(AuditedEntity.RevisionInfo.builder()
                            .email("<EMAIL>")
                            .datetime(LocalDateTime.now(ZoneOffset.UTC))
                            .helpCenterTicket(HelpCenterTicket.builder().build())
                            .build())
                    .operationType(AuditedEntity.OperationType.CREATE)
                    .auditedEntity(AuditedEntities.DOCUMENT)
                    .build(),
            AuditedEntity.<Document>builder()
                    .entity(FakeDocument.DOCUMENT_CREATE)
                    .revisionInfo(AuditedEntity.RevisionInfo.builder()
                            .email("<EMAIL>")
                            .datetime(LocalDateTime.now(ZoneOffset.UTC))
                            .helpCenterTicket(HelpCenterTicket.builder().build())
                            .build())
                    .operationType(AuditedEntity.OperationType.UPDATE)
                    .auditedEntity(AuditedEntities.DOCUMENT)
                    .build()
    );
    @Autowired
    private MockMvc mockMvc;
    @MockitoBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;
    @MockitoBean
    private ReadDocumentAuditUseCase readDocumentAuditUseCase;
    @MockitoBean
    private PaginationService paginationService;

    @Test
    public void fetchAll_document_audits_success() throws Exception {

        when(readDocumentAuditUseCase.executeById(anyLong(), any(), any())).thenReturn(DOCUMENT_AUDITED_ENTITIES);


        var pageResponsePayload = new PageResponsePayload(null, 1, 20, (long) DOCUMENT_AUDITED_ENTITIES.size(), DOCUMENT_AUDITED_ENTITIES);

        when(paginationService.buildPageResponsePayload(any(), any(), any(), anyLong()))
                .thenReturn(pageResponsePayload);
        PageResponsePayload response = request(get(BASE_ENDPOINT + "/1"), HttpStatus.OK, PageResponsePayload.class);

        Assertions.assertEquals(DOCUMENT_AUDITED_ENTITIES.size(), response.getTotal());
        verify(validateUserAccessUseCase, times(1)).checkCanAccessDocument(any());
        verify(readDocumentAuditUseCase, times(1)).executeById(anyLong(), any(), any());
        verify(paginationService, times(1)).buildPageResponsePayload(any(), any(), any(), anyLong());
    }
}
