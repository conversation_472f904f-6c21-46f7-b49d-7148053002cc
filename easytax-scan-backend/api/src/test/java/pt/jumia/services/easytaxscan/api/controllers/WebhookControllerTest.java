package pt.jumia.services.easytaxscan.api.controllers;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.ScanApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.WebhookApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeWebhook;
import pt.jumia.services.easytaxscan.domain.exceptions.ErrorCode;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.GetAclUserUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.webhook.CreateWebhooksUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.webhook.ReadWebhooksUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;
import pt.jumia.services.easytaxscan.domain.utils.ResourceLoader;

import java.util.List;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(WebhookController.class)
public class WebhookControllerTest extends BaseControllerTest {

    private static final String BASE_ENDPOINT = "/api/documents";
    private static final String WEBHOOK = "webhooks";
    private static final long ID = 2L;
    private static final RequestUser REQUEST_USER = RequestUser.builder().username("controllerTestUser").build();


    @MockitoBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;
    @MockitoBean
    private CreateWebhooksUseCase createWebhooksUseCase;
    @MockitoBean
    private ReadWebhooksUseCase readWebhooksUseCase;
    @Autowired
    private MockMvc mockMvc;
    @MockitoBean
    private GetAclUserUseCase getAclUserUseCase;
    @MockitoBean
    private JsonUtils jsonUtils;
    @MockitoBean
    private PaginationService paginationService;

    @BeforeEach
    public void setUp() {
        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    public void findById_success() throws Exception {
        when(readWebhooksUseCase.findById(2L)).thenReturn(FakeWebhook.WEBHOOK);
        WebhookApiResponsePayload response = request(get(BASE_ENDPOINT + "/" + ID + "/" + WEBHOOK)
                , HttpStatus.OK, WebhookApiResponsePayload.class);
        assertEquals(2L, response.getId());
        verify(readWebhooksUseCase, times(1)).findById(ID);
        verify(validateUserAccessUseCase).checkCanAccessWebhooks(REQUEST_USER);
    }

    @Test
    public void fetchById_notFound() throws Exception {
        doThrow(NotFoundException.createNotFound(WebHooks.class, FakeWebhook.WEBHOOK.getId()))
                .when(readWebhooksUseCase).findById(FakeWebhook.WEBHOOK.getId());

        CodedErrorResponsePayload codedErrorResponse = request(
                get(String.format("%s/%d/%s", BASE_ENDPOINT, FakeWebhook.WEBHOOK.getId(), WEBHOOK)),
                HttpStatus.NOT_FOUND,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.ENTITY_NOT_FOUND.getCode());
        verify(readWebhooksUseCase).findById(FakeWebhook.WEBHOOK.getId());
        verify(validateUserAccessUseCase).checkCanAccessWebhooks(REQUEST_USER);
    }

    @Test
    public void findById_forbidden() throws Exception {
        // prepare
        doThrow(UserForbiddenException.create(""))
                .when(validateUserAccessUseCase).checkCanAccessWebhooks(REQUEST_USER);
        CodedErrorResponsePayload codedErrorResponse = request(
                get(String.format("%s/%d/%s", BASE_ENDPOINT, FakeWebhook.WEBHOOK.getId(), WEBHOOK)),
                "webhooks/valid_webhooks.json",
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);
        // verify
        verify(validateUserAccessUseCase).checkCanAccessWebhooks(REQUEST_USER);
    }

    @Test
    public void create_success() throws Exception {
        // prepare
        when(createWebhooksUseCase.execute(any())).thenReturn(FakeWebhook.WEBHOOK);

        String payload = ResourceLoader.getStringFromFile("webhooks/valid_webhooks_create.json");
        // execute
        MvcResult mvcResult = mockMvc.perform(post(BASE_ENDPOINT + "/" + WEBHOOK)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isCreated())
                .andReturn();
        // verify
        verify(createWebhooksUseCase).execute(any());
        verify(validateUserAccessUseCase).checkCanManageWebhooks(REQUEST_USER);
    }

    @Test
    public void create_forbidden() throws Exception {
        // prepare
        doThrow(UserForbiddenException.create(""))
                .when(validateUserAccessUseCase).checkCanManageWebhooks(REQUEST_USER);
        CodedErrorResponsePayload codedErrorResponse = request(
                post(String.format("%s/%s", BASE_ENDPOINT, WEBHOOK)),
                "webhooks/valid_webhooks_create.json",
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        // verify
        verify(validateUserAccessUseCase).checkCanManageWebhooks(REQUEST_USER);
    }

    @Test
    public void fetchAll_success() throws Exception {

        when(readWebhooksUseCase.findAll(any(), any())).thenReturn(List.of(FakeWebhook.WEBHOOK));
        when(readWebhooksUseCase.executeCount()).thenReturn(2L);

        List<ScanApiResponsePayload> results = FakeScan.ALL.stream()
                .map(ScanApiResponsePayload::new)
                .collect(Collectors.toList());

        var pageResponsePayload = new PageResponsePayload(null, 1, 20, (long) results.size(), results);

        when(paginationService.buildPageResponsePayload(any(), any(), any(), anyLong()))
                .thenReturn(pageResponsePayload);

        // execute
        PageResponsePayload response = request(get(BASE_ENDPOINT + "/" + WEBHOOK),
                HttpStatus.OK, PageResponsePayload.class);

        // verify
        Assertions.assertEquals(results.size(), response.getTotal());
        verify(validateUserAccessUseCase).checkCanAccessWebhooks(REQUEST_USER);
        verify(paginationService).buildPageResponsePayload(any(), any(), any(), anyLong());
        verify(readWebhooksUseCase).findAll(any(), any());
        verify(readWebhooksUseCase).executeCount();
    }

}
