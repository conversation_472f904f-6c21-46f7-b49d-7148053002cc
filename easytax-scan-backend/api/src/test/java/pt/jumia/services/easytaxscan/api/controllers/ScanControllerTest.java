package pt.jumia.services.easytaxscan.api.controllers;


import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.ScanApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.exceptions.ErrorCode;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.jobs.ScanJobExecutionUseCase;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ReadQueryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.scan.CreateScanUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.scan.DeleteScanUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.scan.ReadScanUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.scan.UpdateScanUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;
import pt.jumia.services.easytaxscan.domain.utils.ResourceLoader;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Tests the controller at API level.
 * In here you should test the API validations and return content
 * <p>
 * Note that you need to mock all the dependencies of your controller
 */
@ExtendWith(SpringExtension.class)
@WebMvcTest(ScanController.class)
public class ScanControllerTest extends BaseControllerTest {

    public static final String SCAN_PATH = "/api/scans";
    private static final Long SCAN_ID = 1L;

    @Autowired
    private MockMvc mockMvc;

    @MockitoBean
    private JsonUtils jsonUtils;
    @MockitoBean
    private PaginationService paginationService;
    @MockitoBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;
    @MockitoBean
    private CreateScanUseCase createScanUseCase;
    @MockitoBean
    private ReadScanUseCase readScanUseCase;
    @MockitoBean
    private UpdateScanUseCase updateScanUseCase;
    @MockitoBean
    private DeleteScanUseCase deleteScanUseCase;
    @MockitoBean
    private ReadQueryUseCase readQueryUseCase;
    @MockitoBean
    private ScanRepository scanRepository;
    @MockitoBean
    private ScanJobExecutionUseCase scanJobExecutionUseCase;


    @Test
    public void testInvalidPayload_clientError() throws Exception {
        String payload = ResourceLoader.getStringFromFile("scan/invalid_scan.json");
        mockMvc
                .perform(post(SCAN_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isBadRequest())
                .andReturn();
    }


    @Test
    public void testScanCreateSuccess() throws Exception {
        when(createScanUseCase.execute(any()))
                .thenReturn(FakeScan.SCAN_CREATE);

        String payload = ResourceLoader.getStringFromFile("scan/valid_scan.json");
        mockMvc
                .perform(post(SCAN_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvalidContent() throws Exception {
        when(createScanUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        mockMvc
                .perform(post(SCAN_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("Invalid Payload"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.details",
                        containsString("JSON parse error: Unrecognized token 'Invalid'")))
                .andReturn();
    }

    @Test
    public void testInternalServerError() throws Exception {
        when(createScanUseCase.execute(any()))
                .thenThrow(new ConstraintViolationException("Test error", Collections.emptySet()));

        String payload = ResourceLoader.getStringFromFile("scan/valid_scan.json");
        mockMvc
                .perform(post(SCAN_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isInternalServerError())
                .andReturn();
    }

    @Test
    public void fetchAll_success() throws Exception {
        // prepare
        when(readScanUseCase.execute(any(), any(), any())).thenReturn(FakeScan.ALL);
        when(readScanUseCase.executeCount(any())).thenReturn((long) FakeScan.ALL.size());

        List<ScanApiResponsePayload> results = FakeScan.ALL.stream()
                .map(ScanApiResponsePayload::new)
                .collect(Collectors.toList());

        var pageResponsePayload = new PageResponsePayload(null, 1, 20, (long) results.size(), results);

        when(paginationService.buildPageResponsePayload(any(), any(), any(), anyLong()))
                .thenReturn(pageResponsePayload);

        // execute
        PageResponsePayload response = request(get(SCAN_PATH),
                HttpStatus.OK, PageResponsePayload.class);

        // verify
        Assertions.assertEquals(results.size(), response.getTotal());
        verify(validateUserAccessUseCase).checkCanAccessScan(REQUEST_USER);
        verify(paginationService).buildPageResponsePayload(any(), any(), any(), anyLong());
        verify(readScanUseCase).execute(any(), any(), any());
        verify(readScanUseCase).executeCount(any());
    }

    @Test
    public void fetchAll_forbidden() throws Exception {
        // prepare
        doThrow(UserForbiddenException.create(""))
                .when(validateUserAccessUseCase).checkCanAccessScan(REQUEST_USER);

        // execute
        CodedErrorResponsePayload response = request(
                get(SCAN_PATH),
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
                .extracting(CodedErrorResponsePayload::getCode)
                .isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(validateUserAccessUseCase).checkCanAccessScan(REQUEST_USER);
        verifyNoInteractions(readScanUseCase);
    }

    @Test
    public void update_success() throws Exception {
        // prepare
        when(updateScanUseCase.execute(anyLong(), any())).thenReturn(FakeScan.SCAN_UPDATE);

        // execute
        ScanApiResponsePayload response = request(
                put(SCAN_PATH + '/' + SCAN_ID),
                "scan/valid_scan.json",
                HttpStatus.OK,
                ScanApiResponsePayload.class);

        // verify
        assertEquals(FakeScan.SCAN_UPDATE.withoutDbFields(), response.toEntity().withoutDbFields());
        verify(validateUserAccessUseCase).checkCanManageScan(REQUEST_USER);
        verify(updateScanUseCase).execute(anyLong(), any());
    }

    @Test
    public void update_failure_invalidInput() throws Exception {

        String payload = ResourceLoader.getStringFromFile("scan/invalid_update_scan.json");

        // execute
        mockMvc.perform(put(SCAN_PATH + '/' + SCAN_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void update_failure_forbidden() throws Exception {
        String payload = ResourceLoader.getStringFromFile("scan/valid_update_scan.json");
        // prepare
        doThrow(UserForbiddenException.create(""))
                .when(validateUserAccessUseCase).checkCanManageScan(REQUEST_USER);
        // execute
        mockMvc.perform(put(SCAN_PATH + '/' + SCAN_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isForbidden());
    }

    @Test
    public void fetchById_success() throws Exception {
        Long scanId = 2L;
        when(readScanUseCase.execute(scanId)).thenReturn(FakeScan.SCAN_FILTER_DATA2);

        mockMvc.perform(get(SCAN_PATH + "/{id}", scanId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(FakeScan.SCAN_FILTER_DATA2.getId()))
                .andExpect(jsonPath("$.code").value(FakeScan.SCAN_FILTER_DATA2.getCode()));

        verify(validateUserAccessUseCase).checkCanAccessScan(RequestContext.getUser());
        verify(readScanUseCase).execute(scanId);
    }

    @Test
    public void fetchById_badRequest_invalidId() throws Exception {
        when(readScanUseCase.execute(any()))
                .thenThrow(NotFoundException.build("Scan with Id doesn't exist"));
        mockMvc.perform(get(SCAN_PATH + "/{id}", 99999L))
                .andExpect(status().isNotFound());
    }

}
