package pt.jumia.services.easytaxscan.api.controllers;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.easytaxscan.api.payloads.response.CountryApiResponsePayload;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.country.CreateCountryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.country.ReadCountryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.country.UpdateCountryUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;
import pt.jumia.services.easytaxscan.domain.utils.ResourceLoader;

import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(CountryController.class)
public class CountryControllerTest extends BaseControllerTest {

    private static final String COUNTRY_PATH = "/api/countries";
    private static final Long COUNTRY_ID = 1L;

    @Autowired
    private MockMvc mockMvc;

    @MockitoBean
    private JsonUtils jsonUtils;
    @MockitoBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;
    @MockitoBean
    private CreateCountryUseCase createCountryUseCase;
    @MockitoBean
    private ReadCountryUseCase readCountryUseCase;
    @MockitoBean
    private UpdateCountryUseCase updateCountryUseCase;


    @Test
    public void testCountryCreateSuccess() throws Exception {
        when(createCountryUseCase.execute(any())).thenAnswer(returnsFirstArg());

        String payload = ResourceLoader.getStringFromFile("country/valid_country.json");
        mockMvc.perform(post(COUNTRY_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvalidPayload_clientError() throws Exception {
        String payload = ResourceLoader.getStringFromFile("country/invalid_country.json");
        mockMvc.perform(post(COUNTRY_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isBadRequest())
                .andReturn();
    }


    @Test
    public void fetchById_success() throws Exception {
        when(readCountryUseCase.execute(COUNTRY_ID)).thenReturn(FakeCountry.COUNTRY_TEST1);

        mockMvc.perform(get(COUNTRY_PATH + "/{id}", COUNTRY_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(FakeCountry.COUNTRY_TEST1.getId()))
                .andExpect(jsonPath("$.countryCode").value(FakeCountry.COUNTRY_TEST1.getCountryCode()));

        verify(validateUserAccessUseCase).checkCanAccessCountry(RequestContext.getUser());
        verify(readCountryUseCase).execute(COUNTRY_ID);
    }

    @Test
    public void fetchById_notFound() throws Exception {
        when(readCountryUseCase.execute(any()))
                .thenThrow(NotFoundException.build("Country with Id doesn't exist"));

        mockMvc.perform(get(COUNTRY_PATH + "/{id}", 99999L))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.details", containsString("Country with Id doesn't exist")));
    }

    @Test
    public void update_success() throws Exception {
        when(updateCountryUseCase.execute(anyLong(), any())).thenReturn(FakeCountry.COUNTRY_UPDATE);

        CountryApiResponsePayload response = request(
                put(COUNTRY_PATH + '/' + COUNTRY_ID),
                "country/valid_country.json",
                HttpStatus.OK,
                CountryApiResponsePayload.class);

        assertEquals(FakeCountry.COUNTRY_UPDATE.withoutDbFields(), response.toEntity().withoutDbFields());
        verify(validateUserAccessUseCase).checkCanManageCountry(REQUEST_USER);
        verify(updateCountryUseCase).execute(anyLong(), any());
    }


}
