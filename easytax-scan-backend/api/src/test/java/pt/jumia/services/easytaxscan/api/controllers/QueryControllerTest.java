package pt.jumia.services.easytaxscan.api.controllers;

import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import pt.jumia.services.easytaxscan.api.payloads.request.QueryApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.DataSourceApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeSubQuery;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.exceptions.RecordAlreadyExistsException;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.CreateQueryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ReadQueryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.UpdateQueryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ValidateDataSourceQuery;
import pt.jumia.services.easytaxscan.domain.usecases.subqueries.ReadSubQueryUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;
import pt.jumia.services.easytaxscan.domain.utils.ResourceLoader;

import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.containsString;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(QueryController.class)
public class QueryControllerTest extends BaseControllerTest {

    private static final String QUERY_PATH = "/api/queries";
    private static final Long QUERY_ID = 1L;

    @InjectMocks
    private QueryController queryController;

    @Autowired
    private MockMvc mockMvc;
    @MockitoBean
    private JsonUtils jsonUtils;
    @MockitoBean
    private PaginationService paginationService;
    @MockitoBean
    private CreateQueryUseCase createQueryUseCase;
    @MockitoBean
    private ReadQueryUseCase readQueryUseCase;
    @MockitoBean
    private UpdateQueryUseCase updateQueryUseCase;
    @MockitoBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;
    @MockitoBean
    private ReadSubQueryUseCase readSubQueryUseCase;
    @MockitoBean
    private ValidateDataSourceQuery validateDataSourceQuery;


    @Test
    public void testInvalidPayload_clientError() throws Exception {
        String payload = ResourceLoader.getStringFromFile("queries/invalid_query.json");
        mockMvc
                .perform(post(QUERY_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isBadRequest())
                .andReturn();
    }


    @Test
    public void testQueryCreateFailure() throws Exception {
        when(createQueryUseCase.execute(FakeQuery.QUERY_CREATE)).thenThrow(new NullPointerException("Expected NPE"));
        String payload = ResourceLoader.getStringFromFile("queries/valid_query.json");
        mockMvc.perform(post(QUERY_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isInternalServerError())  // Handle failure status
                .andReturn();
    }


    @Test
    public void testInvalidContent() throws Exception {
        when(createQueryUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());
        mockMvc
                .perform(post(QUERY_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("Invalid Payload"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.details",
                        containsString("JSON parse error: Unrecognized token 'Invalid'")))
                .andReturn();
    }

    @Test
    public void testInternalServerError() throws Exception {
        when(createQueryUseCase.execute(any()))
                .thenThrow(new ConstraintViolationException("Test error", Collections.emptySet()));
        String payload = ResourceLoader.getStringFromFile("queries/valid_query.json");
        mockMvc
                .perform(post(QUERY_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isInternalServerError())
                .andReturn();
    }


    @Test
    public void fetchById_badRequest_invalidId() throws Exception {
        when(readQueryUseCase.findById(any()))
                .thenThrow(NotFoundException.build("Query with Id doesnt exist"));
        mockMvc.perform(get(QUERY_PATH + "/{id}", 99999L))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.details",
                        containsString("Query with Id doesnt exist")));
    }

    @Test
    public void testQueryCreateSuccess() throws Exception {
        when(createQueryUseCase.execute(any())).thenReturn(FakeQuery.QUERY_CREATE);
        String payload = ResourceLoader.getStringFromFile("queries/valid_query.json");
        mockMvc
                .perform(post(QUERY_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testQueryCreateRecordAlreadyExists() throws Exception {
        when(createQueryUseCase.execute(any()))
                .thenThrow(new RecordAlreadyExistsException("A query with the same code already exists"));

        String payload = ResourceLoader.getStringFromFile("queries/valid_query.json");

        mockMvc.perform(MockMvcRequestBuilders.post(QUERY_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isConflict())
                .andReturn();
    }

    @Test
    public void fetchById_success() throws Exception {
        Long queryId = 2L;
        when(readQueryUseCase.findById(queryId)).thenReturn(FakeQuery.QUERY_UPDATE);
        mockMvc.perform(get(QUERY_PATH + "/{id}", queryId))
                .andExpect(status().is2xxSuccessful())
                .andExpect(jsonPath("$.code").value(FakeQuery.QUERY_UPDATE.getCode()));
        verify(readQueryUseCase).findById(queryId);
    }

    @Test
    public void fetchById_notFound() throws Exception {
        Long queryId = 99999L;
        when(readQueryUseCase.findById(any()))
                .thenThrow(NotFoundException.build("query with Id doesnt exist"));

        mockMvc.perform(get(QUERY_PATH + "/{id}", queryId))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.details").value("query with Id doesnt exist"));

        verify(readQueryUseCase).findById(queryId);
    }

    @Test
    public void update_success() throws Exception {
        Query updatedQuery = FakeQuery.QUERY_CREATE;
        when(updateQueryUseCase.execute(anyLong(), any())).thenReturn(updatedQuery);
        doNothing().when(validateUserAccessUseCase).checkCanManageQuery(any());
        DataSourceApiResponsePayload response = request(
                MockMvcRequestBuilders.put(QUERY_PATH + '/' + QUERY_ID),
                "queries/valid_query.json",
                HttpStatus.OK,
                DataSourceApiResponsePayload.class);
        updatedQuery.setCreatedAt(updatedQuery.getCreatedAt().truncatedTo(ChronoUnit.SECONDS));
        updatedQuery.setUpdatedAt(updatedQuery.getUpdatedAt().truncatedTo(ChronoUnit.SECONDS));
        verify(updateQueryUseCase).execute(anyLong(), any());
        verify(validateUserAccessUseCase).checkCanManageQuery(any());
    }

    @Test
    public void update_failure_invalidInput() throws Exception {
        String payload = ResourceLoader.getStringFromFile("queries/invalid_query.json");
        mockMvc.perform(put(QUERY_PATH + '/' + QUERY_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void update_failure_forbidden() throws Exception {
        when(updateQueryUseCase.execute(anyLong(), any()))
                .thenThrow(new UserForbiddenException("User is not authorized to update the query"));

        String payload = ResourceLoader.getStringFromFile("queries/valid_query.json");
        mockMvc.perform(MockMvcRequestBuilders.put(QUERY_PATH + '/' + QUERY_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isForbidden())
                .andReturn();
    }

    @Test
    public void fetchAllQueries() throws Exception {

        when(readQueryUseCase.execute(any(), any(), any())).
                thenReturn(List.of(FakeQuery.QUERY_CREATE, FakeQuery.QUERY_CREATE));
        when(readQueryUseCase.executeCount(any())).thenReturn(2L);

        List<QueryApiRequestPayload> results = List.of(new QueryApiRequestPayload(FakeQuery.QUERY_CREATE),
                new QueryApiRequestPayload(FakeQuery.QUERY_CREATE));
        var pageResponsePayload = new PageResponsePayload(null, 1, 20, (long) results.size(), results);

        when(paginationService.buildPageResponsePayload(any(), any(), any(), anyLong()))
                .thenReturn(pageResponsePayload);
        PageResponsePayload response = request(get(QUERY_PATH), HttpStatus.OK, PageResponsePayload.class);

        Assertions.assertEquals(results.size(), response.getTotal());
        verify(validateUserAccessUseCase, times(1)).checkCanAccessQuery(any());
        verify(readQueryUseCase, times(1)).execute(any(), any(), any());
        verify(paginationService, times(1)).buildPageResponsePayload(any(), any(), any(), anyLong());
    }

    @Test
    public void fetchAllQueries_forbidden() throws Exception {
        doThrow(new UserForbiddenException("User does not have permission to access queries"))
                .when(validateUserAccessUseCase).checkCanAccessQuery(any());
        mockMvc.perform(get(QUERY_PATH))
                .andExpect(status().isForbidden())
                .andReturn();
        verify(validateUserAccessUseCase, times(1)).checkCanAccessQuery(any());
        verifyNoInteractions(readQueryUseCase, paginationService);
    }

    @Test
    public void fetchByIdSubQuery_success() throws Exception {
        Long queryId = 2L;
        when(readSubQueryUseCase.execute(eq(queryId), any(), any(), any())).thenReturn(FakeSubQuery.ALL);
        when(readSubQueryUseCase.executeCount(eq(queryId), any()))
                .thenReturn(3L);
        mockMvc.perform(get("/api/queries/{id}/sub-queries", queryId))
                .andExpect(status().is2xxSuccessful());
        verify(readSubQueryUseCase).execute(eq(queryId), any(), any(), any());
        verify(readSubQueryUseCase).executeCount(eq(queryId), any());
    }

    @Test
    public void fetchByIdSubQuery_forbidden() throws Exception {
        Long queryId = 2L;
        doThrow(new UserForbiddenException("User is not authorized to access sub-queries"))
                .when(validateUserAccessUseCase).checkCanAccessQuery(any());
        mockMvc.perform(get("/api/queries/{id}/sub-queries", queryId))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.details").value("User is not authorized to access sub-queries"));
        verify(validateUserAccessUseCase).checkCanAccessQuery(any());
        verifyNoInteractions(readSubQueryUseCase);
    }

    @Test
    public void fetchFields_success() throws Exception {
        Long queryId = 1L;
        List<String> mockFields = List.of(
                "context.id",
                "context.items[0].item_name",
                "context.items[0].item_details[0].sku"
        );

        when(readQueryUseCase.fetchFields(queryId)).thenReturn(mockFields);

        mockMvc.perform(get(QUERY_PATH + "/{id}/placeholders", queryId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0]").value("context.id"))
                .andExpect(jsonPath("$[1]").value("context.items[0].item_name"))
                .andExpect(jsonPath("$[2]").value("context.items[0].item_details[0].sku"));

        verify(readQueryUseCase).fetchFields(queryId);
    }

    @Test
    public void fetchFields_notFound() throws Exception {
        Long queryId = 99999L;

        when(readQueryUseCase.fetchFields(queryId))
                .thenThrow(NotFoundException.build("Query with Id " + queryId + " not found"));

        mockMvc.perform(get(QUERY_PATH + "/{id}/placeholders", queryId))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.details").value("Query with Id " + queryId + " not found"));

        verify(readQueryUseCase).fetchFields(queryId);
    }

    @Test
    public void fetchFields_emptyResult() throws Exception {
        Long queryId = 1L;

        when(readQueryUseCase.fetchFields(queryId))
                .thenThrow(new IllegalArgumentException("Sample result is empty for query ID: " + queryId));

        mockMvc.perform(get(QUERY_PATH + "/{id}/placeholders", queryId))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.details").value("Sample result is empty for query ID: " + queryId));

        verify(readQueryUseCase).fetchFields(queryId);
    }

    @Test
    public void fetchFields_forbidden() throws Exception {
        Long queryId = 1L;

        when(readQueryUseCase.fetchFields(queryId))
                .thenThrow(new UserForbiddenException("User does not have permission to access query fields"));

        mockMvc.perform(get(QUERY_PATH + "/{id}/placeholders", queryId))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.details").value("User does not have permission to access query fields"));

        verify(readQueryUseCase).fetchFields(queryId);
    }

    public void previewByQueryId_success() throws Exception {
        String payload = ResourceLoader.getStringFromFile("queries/valid_query_preview.json");
        mockMvc
                .perform(post(QUERY_PATH + "/preview")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isOk())
                .andReturn();
    }
    @Test
    public void preview_success() throws Exception {
        String payload = ResourceLoader.getStringFromFile("queries/valid_query.json");
        mockMvc
                .perform(post(QUERY_PATH + "/preview")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isOk())
                .andReturn();
    }

}
