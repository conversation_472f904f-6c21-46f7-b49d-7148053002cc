package pt.jumia.services.easytaxscan.api.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.util.MultiValueMap;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.utils.ResourceLoader;

import java.util.List;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
public class BaseControllerTest {

    protected static final RequestUser REQUEST_USER = RequestUser.builder().username("controller-tests-req-user").build();

    @Autowired
    protected MockMvc mockMvc;
    @Autowired
    protected ObjectMapper objectMapper;

    @BeforeEach
    public void setUp() {
        RequestContext.setUser(REQUEST_USER);
    }

    protected <T> T request(
            MockHttpServletRequestBuilder requestBuilder,
            Object payload,
            Class<T> responseClass) throws Exception {

        MockHttpServletResponse response = request(requestBuilder, payload).andReturn().getResponse();
        return objectMapper.readValue(response.getContentAsString(), responseClass);
    }

    protected <T> T request(
            MockHttpServletRequestBuilder requestBuilder,
            String requestPayloadPath,
            HttpStatus expectedStatus,
            Class<T> responseClass) throws Exception {

        MockHttpServletResponse response = request(requestBuilder, requestPayloadPath, expectedStatus).andReturn().getResponse();
        return objectMapper.readValue(response.getContentAsString(), responseClass);
    }

    protected <T> T requestStringPayload(
            MockHttpServletRequestBuilder requestBuilder,
            Object payload,
            HttpStatus expectedStatus,
            Class<T> responseClass) throws Exception {

        MockHttpServletResponse response = request(requestBuilder, payload, expectedStatus).andReturn().getResponse();
        return objectMapper.readValue(response.getContentAsString(), responseClass);
    }

    protected ResultActions request(
            MockHttpServletRequestBuilder requestBuilder,
            Object payload) throws Exception {

        return request(requestBuilder, payload, HttpStatus.OK);
    }

    protected ResultActions request(
            MockHttpServletRequestBuilder requestBuilder,
            Object payload,
            HttpStatus expectedStatus) throws Exception {
        String content = payload instanceof String ? (String) payload : objectMapper.writeValueAsString(payload);
        return request(requestBuilder
                        .content(content)
                        .contentType(MediaType.APPLICATION_JSON),
                expectedStatus);
    }

    protected ResultActions request(
            MockHttpServletRequestBuilder requestBuilder,
            String requestPayloadPath,
            HttpStatus expectedStatus) throws Exception {
        String payload = ResourceLoader.getStringFromFile(requestPayloadPath);
        return request(requestBuilder
                        .content(payload)
                        .contentType(MediaType.APPLICATION_JSON),
                expectedStatus);
    }

    protected <T> T request(
            MockHttpServletRequestBuilder requestBuilder,
            Class<T> responseClass) throws Exception {
        MockHttpServletResponse response = request(requestBuilder).andReturn().getResponse();
        return objectMapper.readValue(response.getContentAsString(), responseClass);
    }

    protected <T> T request(
            MockHttpServletRequestBuilder requestBuilder,
            HttpStatus expectedStatus,
            Class<T> responseClass) throws Exception {
        MockHttpServletResponse response = request(requestBuilder, expectedStatus).andReturn().getResponse();
        return objectMapper.readValue(response.getContentAsString(), responseClass);
    }

    protected ResultActions request(MockHttpServletRequestBuilder requestBuilder) throws Exception {
        return request(requestBuilder, HttpStatus.OK);
    }

    protected ResultActions request(
            MockHttpServletRequestBuilder requestBuilder,
            HttpStatus expectedStatus) throws Exception {
        return mockMvc.perform(requestBuilder)
                .andExpect(status().is(expectedStatus.value()));
    }

    protected <T> List<T> requestList(
            MockHttpServletRequestBuilder requestBuilder,
            Object payload,
            Class<T> responseClass) throws Exception {

        MockHttpServletResponse response = request(requestBuilder, payload).andReturn().getResponse();
        return objectMapper.readValue(response.getContentAsString(),
                objectMapper.getTypeFactory().constructCollectionType(List.class, responseClass));
    }

    protected <T> List<T> requestList(
            MockHttpServletRequestBuilder requestBuilder,
            Object payload,
            HttpStatus expectedStatus,
            Class<T> responseClass) throws Exception {

        MockHttpServletResponse response = request(requestBuilder, payload, expectedStatus).andReturn().getResponse();
        return objectMapper.readValue(response.getContentAsString(),
                objectMapper.getTypeFactory().constructCollectionType(List.class, responseClass));
    }

    protected <T> List<T> requestList(
            MockHttpServletRequestBuilder requestBuilder,
            Class<T> responseClass) throws Exception {
        MockHttpServletResponse response = request(requestBuilder).andReturn().getResponse();
        return objectMapper.readValue(response.getContentAsString(),
                objectMapper.getTypeFactory().constructCollectionType(List.class, responseClass));
    }

    protected <T> List<T> requestList(
            MockHttpServletRequestBuilder requestBuilder,
            HttpStatus expectedStatus,
            Class<T> responseClass) throws Exception {
        MockHttpServletResponse response = request(requestBuilder, expectedStatus).andReturn().getResponse();
        return objectMapper.readValue(response.getContentAsString(),
                objectMapper.getTypeFactory().constructCollectionType(List.class, responseClass));
    }

    protected static  MockHttpServletRequestBuilder multipartRequest(final String url, MultiValueMap<String, String> content,
                                                                     final MockMultipartFile mockMultipartFile) {

        return MockMvcRequestBuilders.multipart(url)
                .file(mockMultipartFile)
                .params(content);
    }

}
