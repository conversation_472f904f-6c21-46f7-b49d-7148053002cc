package pt.jumia.services.easytaxscan.api.controllers;

import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.easytaxscan.api.payloads.response.DocumentApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDocument;
import pt.jumia.services.easytaxscan.domain.exceptions.ErrorCode;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.document.CreateDocumentUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.document.ReadDocumentUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;
import pt.jumia.services.easytaxscan.domain.utils.ResourceLoader;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(DocumentController.class)
public class DocumentControllerTest extends BaseControllerTest {

    public static final String DOCUMENT_PATH = "/api/documents";
    private static final Long DOCUMENT_ID = 1L;

    @Autowired
    private MockMvc mockMvc;

    @MockitoBean
    private JsonUtils jsonUtils;
    @MockitoBean
    private PaginationService paginationService;
    @MockitoBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;
    @MockitoBean
    private CreateDocumentUseCase createDocumentUseCase;
    @MockitoBean
    private ReadDocumentUseCase readDocumentUseCase;

    @Test
    public void createDocument_InvalidPayload_ShouldReturnBadRequest() throws Exception {
        String payload = ResourceLoader.getStringFromFile("document/invalid_document.json");

        mockMvc.perform(post(DOCUMENT_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void createDocument_Success() throws Exception {

        Document document = FakeDocument.DOCUMENT_CREATE;
        when(createDocumentUseCase.execute(any())).thenReturn(document);

        String payload = ResourceLoader.getStringFromFile("document/valid_document.json");

        mockMvc.perform(post(DOCUMENT_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.sid").value("DOC0000"))
                .andExpect(jsonPath("$.status").value("CREATED"));

        verify(createDocumentUseCase).execute(any()); // Verify use case execution
    }


    @Test
    public void createDocument_InvalidJson_ShouldReturnBadRequest() throws Exception {
        when(createDocumentUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        mockMvc.perform(post(DOCUMENT_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("Invalid Payload"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.details", containsString("JSON parse error: Unrecognized token 'Invalid'")));
    }

    @Test
    public void createDocument_InternalServerError_ShouldReturn500() throws Exception {
        when(createDocumentUseCase.execute(any()))
                .thenThrow(new ConstraintViolationException("Test error", Collections.emptySet()));

        String payload = ResourceLoader.getStringFromFile("document/valid_document.json");

        mockMvc.perform(post(DOCUMENT_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isInternalServerError());
    }

    @Test
    public void fetchAllDocuments_Success() throws Exception {


        when(readDocumentUseCase.execute(any(), any(), any())).thenReturn(FakeDocument.ALL);
        when(readDocumentUseCase.executeCount(any())).thenReturn((long) FakeDocument.ALL.size());

        List<DocumentApiResponsePayload> results = FakeDocument.ALL.stream()
                .map(DocumentApiResponsePayload::new)
                .collect(Collectors.toList());

        var pageResponsePayload = new PageResponsePayload(null, 1, 20, (long) results.size(), results);

        when(paginationService.buildPageResponsePayload(any(), any(), any(), anyLong()))
                .thenReturn(pageResponsePayload);

        PageResponsePayload response = request(get(DOCUMENT_PATH),
                HttpStatus.OK, PageResponsePayload.class);

        assertEquals(results.size(), response.getTotal());
        verify(validateUserAccessUseCase).checkCanAccessDocument(REQUEST_USER);
        verify(paginationService).buildPageResponsePayload(any(), any(), any(), anyLong());
        verify(readDocumentUseCase).execute(any(), any(), any());
        verify(readDocumentUseCase).executeCount(any());
    }

    @Test
    public void fetchAllDocuments_Forbidden_ShouldReturn403() throws Exception {
        doThrow(UserForbiddenException.create(""))
                .when(validateUserAccessUseCase).checkCanAccessDocument(REQUEST_USER);

        CodedErrorResponsePayload response = request(
                get(DOCUMENT_PATH),
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        assertThat(response)
                .extracting(CodedErrorResponsePayload::getCode)
                .isEqualTo(ErrorCode.FORBIDDEN.getCode());

        verify(validateUserAccessUseCase).checkCanAccessDocument(REQUEST_USER);
        verifyNoInteractions(readDocumentUseCase);
    }

}
