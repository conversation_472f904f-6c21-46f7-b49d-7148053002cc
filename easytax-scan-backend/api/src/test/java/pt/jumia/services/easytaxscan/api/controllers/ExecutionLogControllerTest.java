package pt.jumia.services.easytaxscan.api.controllers;


import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.easytaxscan.api.payloads.request.QueryApiRequestPayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.ExecutionLogRepository;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.CreateExecutionLogUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.ReadExecutionLogUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;
import pt.jumia.services.easytaxscan.domain.utils.ResourceLoader;

import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.containsString;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Tests the controller at API level.
 * In here you should test the API validations and return content
 * <p>
 * Note that you need to mock all the dependencies of your controller
 */
@ExtendWith(SpringExtension.class)
@WebMvcTest(ExecutionLogController.class)
public class ExecutionLogControllerTest extends BaseControllerTest {

    public static final String EXECUTION_LOG_PATH = "/api/execution-logs";
    private static final Long EXECUTION_LOG_ID = 1L;

    @Autowired
    private MockMvc mockMvc;

    @MockitoBean
    private JsonUtils jsonUtils;
    @MockitoBean
    private PaginationService paginationService;
    @MockitoBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;
    @MockitoBean
    private ExecutionLogRepository executionLogRepository;
    @MockitoBean
    private CreateExecutionLogUseCase createExecutionLogUseCase;
    @MockitoBean
    private ReadExecutionLogUseCase readExecutionLogsUseCase;

    @Test
    public void testInvalidPayload_clientError() throws Exception {
        String payload = ResourceLoader.getStringFromFile("executionlogs/invalid_execution_log.json");
        mockMvc
                .perform(post(EXECUTION_LOG_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isBadRequest())
                .andReturn();
    }


    @Test
    public void testScanCreateSuccess() throws Exception {
        when(createExecutionLogUseCase.execute(any()))
                .thenReturn(FakeExecutionLog.EXECUTION_LOG_CREATE);

        String payload = ResourceLoader.getStringFromFile("executionlogs/valid_execution_log.json");
        mockMvc
                .perform(post(EXECUTION_LOG_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvalidContent() throws Exception {
        when(createExecutionLogUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        mockMvc
                .perform(post(EXECUTION_LOG_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("Invalid Payload"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.details",
                        containsString("JSON parse error: Unrecognized token 'Invalid'")))
                .andReturn();
    }

    @Test
    public void testInternalServerError() throws Exception {
        when(createExecutionLogUseCase.execute(any()))
                .thenThrow(new ConstraintViolationException("Test error", Collections.emptySet()));

        String payload = ResourceLoader.getStringFromFile("executionlogs/valid_execution_log.json");
        mockMvc
                .perform(post(EXECUTION_LOG_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isInternalServerError())
                .andReturn();
    }


    @Test
    public void fetchById_success() throws Exception {
        Long executionId = 2L;
        when(readExecutionLogsUseCase.execute(executionId)).thenReturn(FakeExecutionLog.EXECUTION_LOG_CREATE);

        mockMvc.perform(get(EXECUTION_LOG_PATH + "/{id}", executionId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(FakeExecutionLog.EXECUTION_LOG_CREATE.getId()));

        verify(validateUserAccessUseCase).checkCanAccessExecutionLog(RequestContext.getUser());
        verify(readExecutionLogsUseCase).execute(executionId);
    }

    @Test
    public void fetchById_badRequest_invalidId() throws Exception {
        when(readExecutionLogsUseCase.execute(any()))
                .thenThrow(NotFoundException.build("Scan with Id doesn't exist"));
        mockMvc.perform(get(EXECUTION_LOG_PATH + "/{id}", 99999L))
                .andExpect(status().isNotFound());
    }

    @Test
    public void fetchAllQueries() throws Exception {
        when(readExecutionLogsUseCase.execute(any(), any(), any())).
                thenReturn(List.of(FakeExecutionLog.EXECUTION_LOG_CREATE, FakeExecutionLog.EXECUTION_LOG_UPDATE));
        when(readExecutionLogsUseCase.executeCount(any())).thenReturn(2L);

        List<QueryApiRequestPayload> results = List.of(new QueryApiRequestPayload(FakeQuery.QUERY_CREATE),
                new QueryApiRequestPayload(FakeQuery.QUERY_CREATE));
        var pageResponsePayload = new PageResponsePayload(null, 1, 20, (long) results.size(), results);

        when(paginationService.buildPageResponsePayload(any(), any(), any(), anyLong()))
                .thenReturn(pageResponsePayload);
        PageResponsePayload response = request(get(EXECUTION_LOG_PATH), HttpStatus.OK, PageResponsePayload.class);

        Assertions.assertEquals(results.size(), response.getTotal());
        verify(validateUserAccessUseCase, times(1)).checkCanAccessExecutionLog(any());
        verify(readExecutionLogsUseCase, times(1)).execute(any(), any(), any());
        verify(paginationService, times(1)).buildPageResponsePayload(any(), any(), any(), anyLong());
    }

}
