package pt.jumia.services.easytaxscan.api.controllers;


import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import pt.jumia.services.easytaxscan.api.payloads.response.DataSourceApiResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.HealthStatus;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.exceptions.ErrorCode;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.datasource.*;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;
import pt.jumia.services.easytaxscan.domain.utils.ResourceLoader;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.AdditionalAnswers.returnsFirstArg;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Tests the controller at API level.
 * In here you should test the API validations and return content
 * <p>
 * Note that you need to mock all the dependencies of your controller
 */
@ExtendWith(SpringExtension.class)
@WebMvcTest(DataSourceController.class)
public class DataSourceControllerTest extends BaseControllerTest {

    public static final String DATASOURCE_PATH = "/api/datasources";
    private static final Long DATASOURCE_ID = 1L;

    @Autowired
    private MockMvc mockMvc;

    @MockitoBean
    private JsonUtils jsonUtils;
    @MockitoBean
    private PaginationService paginationService;
    @MockitoBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;
    @MockitoBean
    private CreateDataSourceUseCase createDataSourceUseCase;
    @MockitoBean
    private ReadDataSourceUseCase readDataSourceUseCase;
    @MockitoBean
    private UpdateDataSourceUseCase updateDataSourceUseCase;
    @MockitoBean
    private DeleteDataSourceUseCase deleteDataSourceUseCase;
    @MockitoBean
    private ValidateDataSourceUseCase validateDataSourceUseCase;


    @Test
    public void testInvalidPayload_clientError() throws Exception {
        String payload = ResourceLoader.getStringFromFile("datasource/invalid_datasource.json");
        mockMvc
                .perform(post(DATASOURCE_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isBadRequest())
                .andReturn();
    }


    @Test
    public void testDataSourceCreateSuccess() throws Exception {
        when(createDataSourceUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        String payload = ResourceLoader.getStringFromFile("datasource/valid_datasource.json");
        mockMvc
                .perform(post(DATASOURCE_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isCreated())
                .andReturn();
    }

    @Test
    public void testInvalidContent() throws Exception {
        when(createDataSourceUseCase.execute(any()))
                .thenAnswer(returnsFirstArg());

        mockMvc
                .perform(post(DATASOURCE_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("Invalid Payload"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.details",
                        containsString("JSON parse error: Unrecognized token 'Invalid'")))
                .andReturn();
    }

    @Test
    public void testInternalServerError() throws Exception {
        when(createDataSourceUseCase.execute(any()))
                .thenThrow(new ConstraintViolationException("Test error", Collections.emptySet()));

        String payload = ResourceLoader.getStringFromFile("datasource/valid_datasource.json");
        mockMvc
                .perform(post(DATASOURCE_PATH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isInternalServerError())
                .andReturn();
    }

    @Test
    public void fetchAll_success() throws Exception {
        // prepare
        when(readDataSourceUseCase.execute(any(), any(), any())).thenReturn(FakeDataSource.ALL);
        when(readDataSourceUseCase.executeCount(any())).thenReturn((long) FakeDataSource.ALL.size());

        List<DataSourceApiResponsePayload> results = FakeDataSource.ALL.stream()
                .map(DataSourceApiResponsePayload::new)
                .collect(Collectors.toList());

        var pageResponsePayload = new PageResponsePayload(null, 1, 20, (long) results.size(), results);

        when(paginationService.buildPageResponsePayload(any(), any(), any(), anyLong()))
                .thenReturn(pageResponsePayload);

        // execute
        PageResponsePayload response = request(get(DATASOURCE_PATH),
                HttpStatus.OK, PageResponsePayload.class);

        // verify
        Assertions.assertEquals(results.size(), response.getTotal());
        verify(validateUserAccessUseCase).checkCanAccessDataSource(REQUEST_USER);
        verify(paginationService).buildPageResponsePayload(any(), any(), any(), anyLong());
        verify(readDataSourceUseCase).execute(any(), any(), any());
        verify(readDataSourceUseCase).executeCount(any());
    }

    @Test
    public void fetchAll_forbidden() throws Exception {
        // prepare
        doThrow(UserForbiddenException.create(""))
                .when(validateUserAccessUseCase).checkCanAccessDataSource(REQUEST_USER);

        // execute
        CodedErrorResponsePayload response = request(
                get(DATASOURCE_PATH),
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        // verify
        assertThat(response)
                .extracting(CodedErrorResponsePayload::getCode)
                .isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(validateUserAccessUseCase).checkCanAccessDataSource(REQUEST_USER);
        verifyNoInteractions(readDataSourceUseCase);
    }

    @Test
    public void update_success() throws Exception {
        // prepare
        when(updateDataSourceUseCase.execute(anyLong(), any())).thenReturn(FakeDataSource.DATA_SOURCE_UPDATE);

        // execute
        DataSourceApiResponsePayload response = request(
                put(DATASOURCE_PATH + '/' + DATASOURCE_ID),
                "datasource/valid_datasource.json",
                HttpStatus.OK,
                DataSourceApiResponsePayload.class);


        // verify
        assertEquals(FakeDataSource.DATA_SOURCE_UPDATE.withoutDbFields(), response.toEntity().withoutDbFields());
        verify(validateUserAccessUseCase).checkCanManageDataSource(REQUEST_USER);
        verify(updateDataSourceUseCase).execute(anyLong(), any());
    }

    @Test
    public void update_failure_invalidInput() throws Exception {

        String payload = ResourceLoader.getStringFromFile("datasource/invalid_update_datasource.json");

        // execute
        mockMvc.perform(put(DATASOURCE_PATH + '/' + DATASOURCE_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void update_failure_forbidden() throws Exception {
        String payload = ResourceLoader.getStringFromFile("datasource/valid_update_datasource.json");
        // prepare
        doThrow(UserForbiddenException.create(""))
                .when(validateUserAccessUseCase).checkCanManageDataSource(REQUEST_USER);
        // execute
        mockMvc.perform(put(DATASOURCE_PATH + '/' + DATASOURCE_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isForbidden());
    }

    @Test
    public void fetchById_success() throws Exception {
        Long dataSourceId = 2L;
        when(readDataSourceUseCase.execute(dataSourceId)).thenReturn(FakeDataSource.DATA_SOURCE_FILTER_DATA2);

        mockMvc.perform(get(DATASOURCE_PATH + "/{id}", dataSourceId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(FakeDataSource.DATA_SOURCE_FILTER_DATA2.getId()))
                .andExpect(jsonPath("$.code").value(FakeDataSource.DATA_SOURCE_FILTER_DATA2.getCode()));

        verify(validateUserAccessUseCase).checkCanAccessDataSource(RequestContext.getUser());
        verify(readDataSourceUseCase).execute(dataSourceId);
    }

    @Test
    public void fetchById_badRequest_invalidId() throws Exception {
        when(readDataSourceUseCase.execute(any()))
                .thenThrow(NotFoundException.build("DataSource with Id doesnt exist"));
        mockMvc.perform(get(DATASOURCE_PATH + "/{id}", 99999L))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.details",
                        containsString("DataSource with Id doesnt exist")));
    }

    @Test
    public void healthcheck_success() throws Exception {
        // prepare
        String dataSourceCode = "omsCode";
        HealthStatus expectedStatus = new HealthStatus(true, null,null);

        when(validateDataSourceUseCase.execute(eq(dataSourceCode), any()))
                .thenReturn(expectedStatus);

        String payload = ResourceLoader.getStringFromFile("datasource/valid_health_check_params.json");

        // execute
        MvcResult result = mockMvc
                .perform(
                        post(DATASOURCE_PATH + "/" + dataSourceCode + "/health-check")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(payload))
                .andExpect(status().isOk())
                .andReturn();

        // verify - parse the response
        String responseJson = result.getResponse().getContentAsString();
        ObjectMapper mapper = new ObjectMapper();
        HealthStatus response = mapper.readValue(responseJson, HealthStatus.class);

        assertEquals(expectedStatus.isSuccess(), response.isSuccess());
        assertNull(response.getError());
        verify(validateUserAccessUseCase).checkCanManageDataSource(REQUEST_USER);
        verify(validateDataSourceUseCase).execute(eq(dataSourceCode), any());
    }

    @Test
    public void healthcheck_failure() throws Exception {
        // prepare
        String dataSourceCode = "navCode";
        String errorMessage = "Connection failed";
        HealthStatus expectedStatus = new HealthStatus(false, errorMessage,null);

        when(validateDataSourceUseCase.execute(eq(dataSourceCode), any()))
                .thenReturn(expectedStatus);

        String payload = ResourceLoader.getStringFromFile("datasource/valid_health_check_params.json");

        // execute
        MvcResult result = mockMvc
                .perform(
                        post(DATASOURCE_PATH + "/" + dataSourceCode + "/health-check")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(payload))
                .andExpect(status().isOk())
                .andReturn();

        // verify - parse the response
        String responseJson = result.getResponse().getContentAsString();
        ObjectMapper mapper = new ObjectMapper();
        HealthStatus response = mapper.readValue(responseJson, HealthStatus.class);
        // verify
        assertFalse(response.isSuccess());
        assertEquals(errorMessage, response.getError());
        verify(validateUserAccessUseCase).checkCanManageDataSource(REQUEST_USER);
        verify(validateDataSourceUseCase).execute(eq(dataSourceCode), any());
    }

    @Test
    public void healthcheck_forbidden() throws Exception {
        // prepare
        String dataSourceCode = "cashRecCode";
        String payload = ResourceLoader.getStringFromFile("datasource/valid_health_check_params.json");

        doThrow(UserForbiddenException.create(""))
                .when(validateUserAccessUseCase).checkCanManageDataSource(REQUEST_USER);

        // execute
        mockMvc.perform(
                        post(DATASOURCE_PATH + "/" + dataSourceCode + "/health-check")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(payload))
                .andExpect(status().isForbidden());

        verify(validateUserAccessUseCase).checkCanManageDataSource(REQUEST_USER);
        verifyNoInteractions(validateDataSourceUseCase);
    }

    @Test
    public void healthcheck_invalidPayload() throws Exception {
        // prepare
        String dataSourceCode = "test_code";
        String invalidPayload = "";

        // execute
        mockMvc.perform(post(DATASOURCE_PATH + "/" + dataSourceCode + "/health-check")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(invalidPayload))
                .andExpect(status().isBadRequest());

        // verify
        verifyNoInteractions(validateDataSourceUseCase);
    }

}
