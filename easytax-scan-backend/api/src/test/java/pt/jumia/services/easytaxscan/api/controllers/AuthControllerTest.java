package pt.jumia.services.easytaxscan.api.controllers;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import pt.jumia.services.easytaxscan.api.payloads.response.JwtResponsePayload;
import pt.jumia.services.easytaxscan.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.easytaxscan.domain.usecases.ReadUserAccessUseCase;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@WebMvcTest(AuthController.class)
public class AuthControllerTest extends BaseControllerTest {

    private static final String BASE_PUBLIC_ENDPOINT = "/auth";
    private static final String BASE_ENDPOINT = "/api/auth";

    @MockitoBean
    private ReadUserAccessUseCase readUserAccessUseCase;

    @Test
    public void swapTempToken() throws Exception {

        String tempToken = "temp-token-to-swap";
        String finalToken = "final-token";
        when(readUserAccessUseCase.findRealToken(tempToken)).thenReturn(finalToken);

        JwtResponsePayload swappedJwtResponsePayload = requestStringPayload(
                post(BASE_PUBLIC_ENDPOINT + "/swap-token"),
                tempToken,
                HttpStatus.OK,
                JwtResponsePayload.class);

        verify(readUserAccessUseCase).findRealToken(tempToken);
        assertThat(swappedJwtResponsePayload.getJwt()).isEqualTo(finalToken);
    }

    @Test
    public void swapTempTokenNull() throws Exception {

        CodedErrorResponsePayload codedErrorResponsePayload = request(
                post(BASE_PUBLIC_ENDPOINT + "/swap-token"),
                HttpStatus.BAD_REQUEST,
                CodedErrorResponsePayload.class);

        verifyNoInteractions(readUserAccessUseCase);
        assertThat(codedErrorResponsePayload.getDetails()).contains("Required request body is missing");
    }

    @Test
    public void swapTempTokenEmpty() throws Exception {

        CodedErrorResponsePayload codedErrorResponsePayload = requestStringPayload(
                post(BASE_PUBLIC_ENDPOINT + "/swap-token"),
                "",
                HttpStatus.BAD_REQUEST,
                CodedErrorResponsePayload.class);

        verifyNoInteractions(readUserAccessUseCase);
        assertThat(codedErrorResponsePayload.getDetails()).contains("Required request body is missing");
    }

    @Test
    public void fetchAllPermissions() throws Exception {

        Map<String, Map<String, List<String>>> userPermissions = Map.of(
                "APPLICATION", Map.of(
                        "EASYTAXSCAN", List.of("can_access", "manage_everything")
                )
        );
        when(readUserAccessUseCase.execute(REQUEST_USER)).thenReturn(userPermissions);

        Map<String, Map<String, List<String>>> permissionsResponsePayload = request(
                get(BASE_ENDPOINT + "/user/permissions"),
                HttpStatus.OK,
                Map.class);

        verify(readUserAccessUseCase).execute(REQUEST_USER);
        assertThat(permissionsResponsePayload).isEqualTo(userPermissions);
    }

    @Test
    public void logout() throws Exception {

        request(get(BASE_ENDPOINT + "/logout"), HttpStatus.OK);

        verify(readUserAccessUseCase).logout(REQUEST_USER);
    }

}
