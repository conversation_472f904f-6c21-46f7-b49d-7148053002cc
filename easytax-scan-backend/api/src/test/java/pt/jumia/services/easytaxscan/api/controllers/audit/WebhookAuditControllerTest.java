package pt.jumia.services.easytaxscan.api.controllers.audit;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.easytaxscan.api.controllers.BaseControllerTest;
import pt.jumia.services.easytaxscan.api.payloads.response.PageResponsePayload;
import pt.jumia.services.easytaxscan.api.utils.PaginationService;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.HelpCenterTicket;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeWebhook;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.audit.ReadWebhookAuditUseCase;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@ExtendWith(SpringExtension.class)
@WebMvcTest(WebhookAuditController.class)
public class WebhookAuditControllerTest extends BaseControllerTest {

    private static final String BASE_ENDPOINT = "/api/audit/webhook";
    static WebHooks webHooks = FakeWebhook.WEBHOOK;
    private static final List<AuditedEntity<WebHooks>> QUERY_AUDITED_ENTITIES = List.of(
            AuditedEntity.<WebHooks>builder()
                    .entity(webHooks)
                    .revisionInfo(AuditedEntity.RevisionInfo.builder()
                            .email("<EMAIL>")
                            .datetime(LocalDateTime.now(ZoneOffset.UTC))
                            .helpCenterTicket(HelpCenterTicket.builder().build())
                            .build())
                    .operationType(AuditedEntity.OperationType.CREATE)
                    .auditedEntity(AuditedEntities.WEBHOOKS)
                    .build(),
            AuditedEntity.<WebHooks>builder()
                    .entity(webHooks)
                    .revisionInfo(AuditedEntity.RevisionInfo.builder()
                            .email("<EMAIL>")
                            .datetime(LocalDateTime.now(ZoneOffset.UTC))
                            .helpCenterTicket(HelpCenterTicket.builder().build())
                            .build())
                    .operationType(AuditedEntity.OperationType.UPDATE)
                    .auditedEntity(AuditedEntities.WEBHOOKS)
                    .build()
    );
    @Autowired
    private MockMvc mockMvc;
    @MockitoBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;
    @MockitoBean
    private ReadWebhookAuditUseCase readWebhookAuditUseCase;
    @MockitoBean
    private PaginationService paginationService;

    @Test
    public void fetchAll_webhooks_audits_success() throws Exception {

        when(readWebhookAuditUseCase.executeById(anyLong(), any(), any())).thenReturn(QUERY_AUDITED_ENTITIES);


        var pageResponsePayload = new PageResponsePayload(null, 1, 20, (long) QUERY_AUDITED_ENTITIES.size(), QUERY_AUDITED_ENTITIES);

        when(paginationService.buildPageResponsePayload(any(), any(), any(), anyLong()))
                .thenReturn(pageResponsePayload);
        PageResponsePayload response = request(get(BASE_ENDPOINT + "/1"), HttpStatus.OK, PageResponsePayload.class);

        Assertions.assertEquals(QUERY_AUDITED_ENTITIES.size(), response.getTotal());
        verify(validateUserAccessUseCase, times(1)).checkCanAccessWebhooks(any());
        verify(readWebhookAuditUseCase, times(1)).executeById(anyLong(), any(), any());
        verify(paginationService, times(1)).buildPageResponsePayload(any(), any(), any(), anyLong());
    }
}
