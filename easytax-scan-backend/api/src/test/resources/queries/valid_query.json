{"sql": "SELECT mainquery, subQuery FROM users WHERE active = true AND id > :fromId AND id IN (:sidList)", "code": "code2", "description": "Query to fetch active users", "pageSize": 10, "paginationField": "id", "dataSourceCode": "Prasu2", "subQueries": [{"id": "1", "mainQueryColumn": "mainquery", "subQueryColumn": "subQuery", "isList": "false", "status": "ACTIVE"}, {"id": "2", "mainQueryColumn": "mainquery", "subQueryColumn": "subQuery", "isList": "false", "status": "ACTIVE"}], "sampleResult": "Sample result data"}