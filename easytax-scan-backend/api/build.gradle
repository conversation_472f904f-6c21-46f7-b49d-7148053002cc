apply from: '../config/quality/quality.gradle'

dependencies {
    implementation project(':domain')

    // TODO: this was added to remove the dependency from data module, but should be removed
    implementation("org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    api("org.springframework.boot:spring-boot-starter-web:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    implementation ("org.springframework.boot:spring-boot-starter-validation:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    // Decode auth token
    implementation "com.nimbusds:nimbus-jose-jwt:2.10.1"

    // Decoding b2b token
    implementation group: 'com.sun.xml.security', name: 'xml-security-impl', version: '1.0'

    //swagger, for api documentation
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.6")
    testImplementation ("org.springframework.boot:spring-boot-starter-test:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    implementation "org.springframework:spring-test:${springVersion}"

    //quartz
    implementation "org.quartz-scheduler:quartz:${quartzVersion}"

}


configurations.configureEach {
    resolutionStrategy.eachDependency { details ->
        if (details.requested.group == 'org.springframework') {
            details.useVersion '6.2.2'
        }
    }
}