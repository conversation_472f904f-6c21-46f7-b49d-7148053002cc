<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC
  "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
  "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<!-- This is a checkstyle configuration file. For descriptions of
what the following rules do, please see the checkstyle configuration
page at http://checkstyle.sourceforge.net/config.html -->

<module name="Checker">

  <module name="FileTabCharacter">
    <!-- Checks that there are no tab characters in the file.
    -->
  </module>

  <module name="NewlineAtEndOfFile">
    <property name="lineSeparator" value="lf"/>
  </module>

  <!--

  LENGTH CHECKS

  -->
  <module name="LineLength">
    <!-- Checks if a line is too long. -->
    <property name="max" value="${com.puppycrawl.tools.checkstyle.checks.sizes.LineLength.max}" default="150"/>
    <property name="severity" value="warning"/>

    <!--
      The default ignore pattern exempts the following elements:
        - import statements
        - long URLs inside comments
    -->

    <property name="ignorePattern"
              value="${com.puppycrawl.tools.checkstyle.checks.sizes.LineLength.ignorePattern}"
              default="^(package .*;\s*)|(import .*;\s*)|( *(\*|//).*https?://.*)$"/>
  </module>

  <!-- All Java AST specific tests live under TreeWalker module. -->
  <module name="TreeWalker">

    <!--

    IMPORT CHECKS

    -->

    <module name="RedundantImport">
      <!-- Checks for redundant import statements. -->
      <property name="severity" value="error"/>
    </module>

    <!--

    JAVADOC CHECKS

    -->

    <!-- No need for Javadoc -->
    <module name="JavadocType">
      <property name="severity" value="ignore"/>
    </module>
    <module name="JavadocMethod">
      <property name="severity" value="ignore"/>
    </module>
    <module name="JavadocVariable">
      <property name="severity" value="ignore"/>
    </module>

    <!--

    NAMING CHECKS

    -->

    <!-- Item 38 - Adhere to generally accepted naming conventions -->

    <module name="PackageName">
      <!-- Validates identifiers for package names against the
        supplied expression. -->
      <!-- Here the default checkstyle rule restricts package name parts to
        seven characters, this is not in line with common practice at Google.
      -->
      <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]{1,})*$"/>
      <property name="severity" value="warning"/>
    </module>

    <module name="TypeNameCheck">
      <!-- Validates static, final fields against the
      expression "^[A-Z][a-zA-Z0-9]*$". -->
      <metadata name="altname" value="TypeName"/>
      <property name="severity" value="warning"/>
    </module>

    <module name="ConstantNameCheck">
      <!-- Validates non-private, static, final fields against the supplied
      public/package final fields "^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$". -->
      <metadata name="altname" value="ConstantName"/>
      <property name="applyToPublic" value="true"/>
      <property name="applyToProtected" value="true"/>
      <property name="applyToPackage" value="true"/>
      <property name="applyToPrivate" value="false"/>
      <property name="format" value="^([A-Z][A-Z0-9]*(_[A-Z0-9]+)*|FLAG_.*)$"/>
      <message key="name.invalidPattern"
        value="Variable ''{0}'' should be in ALL_CAPS (if it is a constant) or be private (otherwise)."/>
      <property name="severity" value="warning"/>
    </module>

    <module name="StaticVariableNameCheck">
      <!-- Validates static, non-final fields against the supplied
      expression "^[a-z][a-zA-Z0-9]*_?$". -->
      <metadata name="altname" value="StaticVariableName"/>
      <property name="applyToPublic" value="true"/>
      <property name="applyToProtected" value="true"/>
      <property name="applyToPackage" value="true"/>
      <property name="applyToPrivate" value="true"/>
      <property name="format" value="^[a-z][a-zA-Z0-9]*_?$"/>
      <property name="severity" value="warning"/>
    </module>

    <module name="MemberNameCheck">
      <!-- Validates non-static members against the supplied expression. -->
      <metadata name="altname" value="MemberName"/>
      <property name="applyToPublic" value="true"/>
      <property name="applyToProtected" value="true"/>
      <property name="applyToPackage" value="true"/>
      <property name="applyToPrivate" value="true"/>
      <property name="format" value="^[a-z][a-zA-Z0-9]*$"/>
      <property name="severity" value="warning"/>
    </module>

    <module name="MethodNameCheck">
      <!-- Validates identifiers for method names. -->
      <metadata name="altname" value="MethodName"/>
      <property name="format" value="^[a-z][a-zA-Z0-9]*(_[a-zA-Z0-9]+)*$"/>
      <property name="severity" value="warning"/>
    </module>

    <module name="ParameterName">
      <!-- Validates identifiers for method parameters against the
        expression "^[a-z][a-zA-Z0-9]*$". -->
      <property name="severity" value="warning"/>
    </module>

    <module name="LocalFinalVariableName">
      <!-- Validates identifiers for local final variables against the
        expression "^[a-z][a-zA-Z0-9]*$". -->
      <property name="severity" value="warning"/>
    </module>

    <module name="LocalVariableName">
      <!-- Validates identifiers for local variables against the
        expression "^[a-z][a-zA-Z0-9]*$". -->
      <property name="severity" value="warning"/>
    </module>


    <!--

    CODING CHECKS

    -->
    <module name="LeftCurly">
      <!-- Checks for placement of the left curly brace ('{'). -->
      <property name="severity" value="warning"/>
    </module>

    <module name="RightCurly">
      <!-- Checks right curlies on CATCH, ELSE, and TRY blocks are on
      the same line. e.g., the following example is fine:
      <pre>
        if {
          ...
        } else
      </pre>
      -->
      <!-- This next example is not fine:
      <pre>
        if {
          ...
        }
        else
      </pre>
      -->
      <property name="option" value="same"/>
      <property name="severity" value="warning"/>
    </module>

    <!-- Checks for braces around if and else blocks -->
    <module name="NeedBraces">
      <property name="severity" value="warning"/>
      <property name="tokens" value="LITERAL_IF, LITERAL_ELSE, LITERAL_FOR, LITERAL_WHILE, LITERAL_DO"/>
    </module>

    <module name="UpperEll">
      <!-- Checks that long constants are defined with an upper ell.-->
      <property name="severity" value="error"/>
    </module>

    <module name="FallThrough">
      <!-- Warn about falling through to the next case statement.  Similar to
      javac -Xlint:fallthrough, but the check is suppressed if a single-line comment
      on the last non-blank line preceding the fallen-into case contains 'fall through' (or
      some other variants which we don't publicized to promote consistency).
      -->
      <property name="reliefPattern"
        value="fall through|Fall through|fallthru|Fallthru|falls through|Falls through|fallthrough|Fallthrough|No break|NO break|no break|continue on"/>
      <property name="severity" value="error"/>
    </module>


    <!--

    MODIFIERS CHECKS

    -->

    <module name="ModifierOrder">
      <!-- Warn if modifier order is inconsistent with JLS3 8.1.1, 8.3.1, and
           8.4.3.  The prescribed order is:
           public, protected, private, abstract, static, final, transient, volatile,
           synchronized, native, strictfp
        -->
    </module>


    <!--

    WHITESPACE CHECKS

    -->

    <module name="WhitespaceAround">
      <!-- Checks that various tokens are surrounded by whitespace.
           This includes most binary operators and keywords followed
           by regular or curly braces.
      -->
      <property name="tokens" value="ASSIGN, BAND, BAND_ASSIGN, BOR,
        BOR_ASSIGN, BSR, BSR_ASSIGN, BXOR, BXOR_ASSIGN, COLON, DIV, DIV_ASSIGN,
        EQUAL, GE, GT, LAND, LE, LITERAL_CATCH, LITERAL_DO, LITERAL_ELSE,
        LITERAL_FINALLY, LITERAL_FOR, LITERAL_IF, LITERAL_RETURN,
        LITERAL_SYNCHRONIZED, LITERAL_TRY, LITERAL_WHILE, LOR, LT, MINUS,
        MINUS_ASSIGN, MOD, MOD_ASSIGN, NOT_EQUAL, PLUS, PLUS_ASSIGN, QUESTION,
        SL, SL_ASSIGN, SR_ASSIGN, STAR, STAR_ASSIGN"/>
      <property name="severity" value="error"/>
    </module>

    <module name="WhitespaceAfter">
      <!-- Checks that commas, semicolons and typecasts are followed by
           whitespace.
      -->
      <property name="tokens" value="COMMA, SEMI, TYPECAST"/>
    </module>

    <module name="NoWhitespaceAfter">
      <!-- Checks that there is no whitespace after various unary operators.
           Linebreaks are allowed.
      -->
      <property name="tokens" value="BNOT, DEC, DOT, INC, LNOT, UNARY_MINUS,
        UNARY_PLUS"/>
      <property name="allowLineBreaks" value="true"/>
      <property name="severity" value="error"/>
    </module>

    <module name="NoWhitespaceBefore">
      <!-- Checks that there is no whitespace before various unary operators.
           Linebreaks are allowed.
      -->
      <property name="tokens" value="SEMI, DOT, POST_DEC, POST_INC"/>
      <property name="allowLineBreaks" value="true"/>
      <property name="severity" value="error"/>
    </module>

    <module name="ParenPad">
      <!-- Checks that there is no whitespace before close parens or after
           open parens.
      -->
      <property name="severity" value="warning"/>
    </module>

  </module>
</module>
