// Work around this issue: https://github.com/spotbugs/spotbugs-gradle-plugin/issues/32
buildscript {
    repositories {
        maven { url 'https://plugins.gradle.org/m2/' }
    }
    dependencies {
        classpath "gradle.plugin.com.github.spotbugs:spotbugs-gradle-plugin:2.0.0"
    }
}

apply plugin: 'jacoco'
apply plugin: 'com.github.spotbugs'
apply plugin: 'pmd'
apply plugin: 'checkstyle'
apply plugin: 'de.aaschmid.cpd'

// spotbugs and cpd do not allow both xml and html/txt reports, so toggle this for readable reports
def readableReports = false

jacoco {
    toolVersion = "0.8.11"
}

jacocoTestReport {
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: ['**/*Test*.*'])
        }))
    }
}

spotbugs {
    ignoreFailures = true
    excludeFilter = new File("${project.rootDir}/config/quality/spotbugs-filter.xml")
}

tasks.withType(com.github.spotbugs.SpotBugsTask) {
    reports {
        text {
            required.set(readableReports)
        }
        xml {
            required.set(!readableReports)
        }
    }
}

pmd {
    if (project.hasProperty("ignoreQualityFailures")) {
        ignoreFailures = project.ignoreQualityFailures
    }
    consoleOutput = true
    toolVersion = '6.54.0'
    ruleSetFiles = files("${project.rootDir}/config/quality/pmd-ruleset.xml")
    ruleSets = []
    pmdTest.enabled = false
}

checkstyle {
    if (project.hasProperty("ignoreQualityFailures")) {
        ignoreFailures = project.ignoreQualityFailures
    }
    configFile = file("${project.rootDir}/config/quality/checkstyle.xml")
    checkstyleTest.enabled = false
    maxErrors = 0
    maxWarnings = 0
}

cpd{
    ignoreFailures = true
}

cpdCheck{
    reports {
        text {
            required.set(readableReports)
        }
        xml {
            required.set(!readableReports)
        }
    }
    exclude '**/*Properties.java'
    exclude '**/*Test.java'
    exclude '**/test/**/*.java'
    exclude '**/*Payload.java'
    exclude '**/*DTO.java'
    exclude '**/rabbitmq/configurations/**/*.java'
    exclude '**/robots/**/*.java'
    exclude '**/dev/**/*.java'
    exclude '**/entities/fake/**/*.java'
}
