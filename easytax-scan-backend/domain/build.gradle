apply plugin: 'idea'
apply from: '../config/quality/quality.gradle'

dependencies {

    api "org.jetbrains:annotations-java5:23.0.0"

    // Acl lib
    api group: 'com.jumia.services', name: 'acl-lib', version: "${aclLibVersion}"

    api group: 'com.github.spotbugs', name: 'spotbugs-annotations', version: "${spotbugsVersion}"

    testImplementation "org.springframework:spring-test:${springVersion}"
    //and load properties

    implementation platform("org.springframework.boot:spring-boot-dependencies:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    implementation("org.springframework.boot:spring-boot-starter-web") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    implementation("org.springframework.boot:spring-boot-starter-freemarker") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    implementation "org.springframework.boot:spring-boot:${springBootVersion}"


    // needed to initialize defaultReader (for the tests to not fail in intellij)
    implementation group: 'com.jayway.jsonpath', name: 'json-path', version: '2.7.0'

    // Prometheus client
    // https://mvnrepository.com/artifact/io.micrometer/micrometer-registry-prometheus
    api group: 'io.micrometer', name: 'micrometer-registry-prometheus', version: '1.10.3'

    //logs
    api group: 'org.slf4j', name: 'slf4j-api', version: '1.7.30'

    // json converter
    implementation group: 'com.google.code.gson', name: 'gson', version: '2.9.1'
    // sql conversion
    implementation group: 'com.github.jsqlparser', name: 'jsqlparser', version: '5.1'

    //quartz
    implementation "org.quartz-scheduler:quartz:${quartzVersion}"
    // Spring context
//    implementation "org.springframework:spring-context-support:${springVersion}"
//    implementation ("org.springframework:spring-web:${springVersion}")

    // commons
    api "pt.aig.aigx.commons:common-utils:0.0.6-RELEASE"

    implementation 'org.apache.commons:commons-lang3:3.12.0'

    // Force caffeine version to prevent it from downgrading to a lower version.
    api 'com.github.ben-manes.caffeine:caffeine:2.8.0'

    //new relic, for monitoring
    implementation group: 'com.newrelic.agent.java', name: 'newrelic-api', version: '7.11.0'

    implementation 'org.springframework.retry:spring-retry:2.0.9'
    runtimeOnly 'org.aspectj:aspectjweaver:1.9.22.1'

    // Template parsing
    implementation group: 'org.freemarker', name: 'freemarker', version: '2.3.32'


    testImplementation "org.mockito:mockito-junit-jupiter:${mockitoVersion}"
    //enable a logging framework in the tests
    testImplementation group: 'org.slf4j', name: 'slf4j-simple', version: '2.0.6'
    implementation 'org.quartz-scheduler:quartz:2.5.0'
}


idea {
    module {
        sourceDirs += file("$buildDir/classes/java")
    }
}
