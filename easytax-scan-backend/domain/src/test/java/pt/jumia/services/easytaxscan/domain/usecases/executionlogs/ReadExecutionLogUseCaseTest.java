package pt.jumia.services.easytaxscan.domain.usecases.executionlogs;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogSortFilters;
import pt.jumia.services.easytaxscan.domain.repository.ExecutionLogRepository;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ReadExecutionLogUseCaseTest {

    @Mock
    private ExecutionLogRepository executionLogRepository;

    @InjectMocks
    private ReadExecutionLogUseCase readExecutionLogUseCase;

    @Test
    public void testExecute_readExecutionLogs_filtersSuccess() {
        ExecutionLog ExecutionLog = FakeExecutionLog.EXECUTION_LOG_CREATE;
        ExecutionLogFilters filters = ExecutionLogFilters.builder().build();
        ExecutionLogSortFilters sortFilters = ExecutionLogSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<ExecutionLog> queries = List.of(ExecutionLog);
        when(executionLogRepository.findAll(filters, sortFilters, pageFilters)).thenReturn(queries);
        List<ExecutionLog> result = readExecutionLogUseCase.execute(filters, sortFilters, pageFilters);
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    public void fechById() {
        ExecutionLog executionLog = FakeExecutionLog.EXECUTION_LOG_CREATE;
        when(executionLogRepository.findById(executionLog.getId())).thenReturn(Optional.of(executionLog));
        ExecutionLog result = readExecutionLogUseCase.execute(executionLog.getId());
        assertNotNull(result);
    }

    @Test
    public void testExecuteCount() {
        ExecutionLogFilters filters = ExecutionLogFilters.builder().build();
        when(executionLogRepository.executeCount(filters)).thenReturn(5L);
        Long result = readExecutionLogUseCase.executeCount(filters);
        assertEquals(5L, result);
        verify(executionLogRepository, times(1)).executeCount(filters);
    }
}
