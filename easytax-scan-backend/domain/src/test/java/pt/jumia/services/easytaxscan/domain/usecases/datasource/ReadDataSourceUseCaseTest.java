package pt.jumia.services.easytaxscan.domain.usecases.datasource;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceRepository;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ReadDataSourceUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class ReadDataSourceUseCaseTest {

    @Mock
    private DataSourceRepository dataSourceRepository;

    @InjectMocks
    private ReadDataSourceUseCase readDataSourceUseCase;

    @Test
    public void execute_success() {
        DataSourceFilters filters = new DataSourceFilters();
        DataSourceSortFilters sortFilters = DataSourceSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();

        DataSource dataSource1 = FakeDataSource.DATA_SOURCE_FILTER_DATA2;
        DataSource dataSource2 = FakeDataSource.DATA_SOURCE_FILTER_DATA3;

        List<DataSource> dataSources = Arrays.asList(dataSource1, dataSource2);
        when(dataSourceRepository.findAll(filters, sortFilters, pageFilters)).thenReturn(dataSources);
        List<DataSource> result = readDataSourceUseCase.execute(filters, sortFilters, pageFilters);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("DSCR2000", result.get(0).getCode());
        assertEquals("DSCR3000", result.get(1).getCode());

        verify(dataSourceRepository).findAll(filters, sortFilters, pageFilters);
    }

    @Test
    public void execute_emptyResult() {
        DataSourceFilters filters = new DataSourceFilters();
        DataSourceSortFilters sortFilters = DataSourceSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        when(dataSourceRepository.findAll(filters, sortFilters, pageFilters)).thenReturn(Collections.emptyList());

        List<DataSource> result = readDataSourceUseCase.execute(filters, sortFilters, pageFilters);

        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(dataSourceRepository).findAll(filters, sortFilters, pageFilters);
    }

    @Test
    public void findByCode_success() throws NotFoundException {
        String code = "DSCR0000";
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;

        when(dataSourceRepository.findByCode(code)).thenReturn(Optional.of(dataSource));
        DataSource result = readDataSourceUseCase.findByCode(code);

        assertNotNull(result);
        assertEquals(code, result.getCode());

        verify(dataSourceRepository).findByCode(code);
    }

    @Test
    public void findByCode_notFound() {
        String code = "DSCR29999";

        when(dataSourceRepository.findByCode(code)).thenReturn(Optional.empty());

        assertThrows(NotFoundException.class, () -> readDataSourceUseCase.findByCode(code));

        verify(dataSourceRepository).findByCode(code);
    }

    @Test
    public void executeCount_success() {
        DataSourceFilters filters = new DataSourceFilters();
        long expectedCount = 5L;

        when(dataSourceRepository.executeCount(filters)).thenReturn(expectedCount);

        Long result = readDataSourceUseCase.executeCount(filters);

        assertNotNull(result);
        assertEquals(expectedCount, result);

        verify(dataSourceRepository).executeCount(filters);
    }

    @Test
    public void execute_successById() throws NotFoundException {
        Long id = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(id);

        when(dataSourceRepository.findById(id)).thenReturn(Optional.of(dataSource));

        DataSource result = readDataSourceUseCase.execute(id);

        assertNotNull(result);
        assertEquals(id, result.getId());

        verify(dataSourceRepository).findById(id);
    }

    @Test
    public void execute_notFoundById() {
        Long id = 1L;

        when(dataSourceRepository.findById(id)).thenReturn(Optional.empty());

        assertThrows(NotFoundException.class, () -> readDataSourceUseCase.execute(id));

        verify(dataSourceRepository).findById(id);
    }
}
