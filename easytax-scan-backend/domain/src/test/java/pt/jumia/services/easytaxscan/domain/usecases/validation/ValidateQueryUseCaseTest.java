package pt.jumia.services.easytaxscan.domain.usecases.validation;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.exceptions.BadRequestException;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ReadQueryUseCase;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class ValidateQueryUseCaseTest {

    @Mock
    private ReadQueryUseCase readQueryUseCase;

    @InjectMocks
    private ValidateQueryUseCase validateQueryUseCase;

    private Query validQuery;
    private Query invalidQueryMissingFromId;
    private Query invalidQueryMissingSidList;
    private Query queryWithInvalidSubQueryColumn;

    @BeforeEach
    void setUp() {
        validQuery = FakeQuery.QUERY_CREATE;

        invalidQueryMissingFromId = FakeQuery.createQueryRequest(
                1L, "SELECT mainquery, subQuery FROM users WHERE active = true",
                "SQL003", "Missing fromId", 10, "field1",
                FakeQuery.QUERY_CREATE.getDataSource().getStatus(), "Sample Result");

        invalidQueryMissingSidList = FakeQuery.createQueryRequest(
                1L, "SELECT mainquery, subQuery FROM users WHERE active = true AND id > :fromId",
                "SQL004", "Missing sidList", 10, "field1",
                FakeQuery.QUERY_CREATE.getDataSource().getStatus(), "Sample Result");

        queryWithInvalidSubQueryColumn = FakeQuery.createQueryRequest(
                1L, "SELECT mainquery FROM users WHERE active = true AND id > :fromId AND id IN (:sidList)",
                "SQL005", "Invalid SubQuery Column", 10, "field1",
                FakeQuery.QUERY_CREATE.getDataSource().getStatus(), "Sample Result");
    }


    @Test
    void shouldValidateQuerySuccessfully() {
        assertDoesNotThrow(() -> validateQueryUseCase.validate(validQuery));
    }

    @Test
    void shouldThrowExceptionWhenFromIdIsMissing() {
        BadRequestException exception = assertThrows(BadRequestException.class,
                () -> validateQueryUseCase.validate(invalidQueryMissingFromId));
        assertEquals("fromId placeholder is required when paginationField is set.", exception.getMessage());
    }

    @Test
    void shouldThrowExceptionWhenSubQueryColumnIsInvalid() {
        SubQuery invalidSubQuery = SubQuery.builder()
                .id(1L)
                .mainQueryColumn("mainquery")
                .subQueryColumn("nonExistingColumn")
                .status(SubQuery.Status.valueOf("ACTIVE"))
                .build();

        queryWithInvalidSubQueryColumn.setSubQueries(List.of(invalidSubQuery));

        when(readQueryUseCase.findById(anyLong())).thenReturn(validQuery);

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            validateQueryUseCase.validate(queryWithInvalidSubQueryColumn);
        });

        assertTrue(exception.getMessage().contains("query column is not matched"));
    }


    @Test
    void shouldThrowExceptionWhenOffsetIsMissingAndPaginationFieldIsNull() {
        Query query = FakeQuery.createQueryRequest(
                1L, "SELECT mainquery, subQuery FROM users WHERE active = true AND id > :fromId",
                "SQL006", "Missing Offset", 10, null,
                FakeQuery.QUERY_CREATE.getDataSource().getStatus(), "Sample Result");

        BadRequestException exception = assertThrows(BadRequestException.class,
                () -> validateQueryUseCase.validate(query));
        assertEquals("Offset placeholder is required when paginationField is null.", exception.getMessage());
    }

}
