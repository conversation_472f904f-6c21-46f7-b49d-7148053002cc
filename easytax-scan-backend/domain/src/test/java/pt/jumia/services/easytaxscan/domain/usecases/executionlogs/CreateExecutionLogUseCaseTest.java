package pt.jumia.services.easytaxscan.domain.usecases.executionlogs;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeExecutionLog;
import pt.jumia.services.easytaxscan.domain.repository.ExecutionLogRepository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CreateExecutionLogUseCaseTest {

    @InjectMocks
    private CreateExecutionLogUseCase createExecutionLogUseCase;
    @Mock
    private ExecutionLogRepository executionLogRepository;


    @Test
    public void testCreateExecutionLog_Success() {
        ExecutionLog executionLog = FakeExecutionLog.EXECUTION_LOG_CREATE;
        when(createExecutionLogUseCase.execute(executionLog)).thenReturn(executionLog);
        ExecutionLog executionLogExecuted = createExecutionLogUseCase.execute(executionLog);
        assertNotNull(executionLogExecuted);
        assertEquals(executionLog.getException(), executionLogExecuted.getException());

    }

    @Test
    public void testCreateExecutionLog_isNull() {
        ExecutionLog executionLog = null;
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            createExecutionLogUseCase.execute(executionLog);
        });
        assertEquals("ExecutionLog to save cannot be null", exception.getMessage());
    }
}
