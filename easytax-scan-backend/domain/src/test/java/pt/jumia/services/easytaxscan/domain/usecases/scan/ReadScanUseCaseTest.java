package pt.jumia.services.easytaxscan.domain.usecases.scan;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ReadScanUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class ReadScanUseCaseTest {

    @Mock
    private ScanRepository ScanRepository;

    @InjectMocks
    private ReadScanUseCase readScanUseCase;

    @Test
    public void execute_success() {
        ScanFilters filters = new ScanFilters();
        ScanSortFilters sortFilters = ScanSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();

        Scan Scan1 = FakeScan.SCAN_FILTER_DATA2;
        Scan Scan2 = FakeScan.SCAN_FILTER_DATA3;

        List<Scan> Scans = Arrays.asList(Scan1, Scan2);
        when(ScanRepository.findAll(filters, sortFilters, pageFilters)).thenReturn(Scans);
        List<Scan> result = readScanUseCase.execute(filters, sortFilters, pageFilters);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("SCAN2000", result.get(0).getCode());
        assertEquals("SCAN3000", result.get(1).getCode());

        verify(ScanRepository).findAll(filters, sortFilters, pageFilters);
    }

    @Test
    public void execute_emptyResult() {
        ScanFilters filters = new ScanFilters();
        ScanSortFilters sortFilters = ScanSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        when(ScanRepository.findAll(filters, sortFilters, pageFilters)).thenReturn(Collections.emptyList());

        List<Scan> result = readScanUseCase.execute(filters, sortFilters, pageFilters);

        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(ScanRepository).findAll(filters, sortFilters, pageFilters);
    }

    @Test
    public void findByCode_success() throws NotFoundException {
        String code = "SCAN0000";
        Scan Scan = FakeScan.SCAN_CREATE;

        when(ScanRepository.findByCode(code)).thenReturn(Optional.of(Scan));
        Scan result = readScanUseCase.findByCode(code);

        assertNotNull(result);
        assertEquals(code, result.getCode());

        verify(ScanRepository).findByCode(code);
    }

    @Test
    public void findByCode_notFound() {
        String code = "SCAN29999";

        when(ScanRepository.findByCode(code)).thenReturn(Optional.empty());

        assertThrows(NotFoundException.class, () -> readScanUseCase.findByCode(code));

        verify(ScanRepository).findByCode(code);
    }

    @Test
    public void executeCount_success() {
        ScanFilters filters = new ScanFilters();
        long expectedCount = 5L;

        when(ScanRepository.executeCount(filters)).thenReturn(expectedCount);

        Long result = readScanUseCase.executeCount(filters);

        assertNotNull(result);
        assertEquals(expectedCount, result);

        verify(ScanRepository).executeCount(filters);
    }

    @Test
    public void execute_successById() throws NotFoundException {
        Long id = 1L;
        Scan Scan = new Scan();
        Scan.setId(id);

        when(ScanRepository.findById(id)).thenReturn(Scan);

        Scan result = readScanUseCase.execute(id);

        assertNotNull(result);
        assertEquals(id, result.getId());

        verify(ScanRepository).findById(id);
    }

    @Test
    public void execute_notFoundById() {
        Long id = 1L;

        when(ScanRepository.findById(id)).thenThrow(NotFoundException.class);

        assertThrows(NotFoundException.class, () -> readScanUseCase.execute(id));

        verify(ScanRepository).findById(id);
    }
}
