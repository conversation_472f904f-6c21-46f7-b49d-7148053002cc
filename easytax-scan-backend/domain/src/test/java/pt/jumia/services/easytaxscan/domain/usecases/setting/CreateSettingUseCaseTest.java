package pt.jumia.services.easytaxscan.domain.usecases.setting;


import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.DataEventsNotificator;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.repository.SettingRepository;
import static org.assertj.core.api.Assertions.assertThatThrownBy;


/**
 * Unit test for {@link CreateSettingUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class CreateSettingUseCaseTest {

    private static final RequestUser REQUEST_USER = RequestUser.builder().username("<EMAIL>").build();
    private static final Setting SETTING = Setting.builder()
            .property("payout_automatic_max_attempts")
            .type(Setting.Type.OVERRIDE)
            .value("3")
            .overrideKey("seller-ng-jumia")
            .description("Max allowed attempts to process automatic queue payouts, using external API.")
            .build();

    @Mock
    private SettingRepository settingRepository;
    @Mock
    private DataEventsNotificator dataEventsNotificator;
    private CreateSettingUseCase createSettingUseCase;
    @Captor
    private ArgumentCaptor<Setting> settingCaptor;

    @BeforeEach
    public void setUp() {
        RequestContext.setUser(REQUEST_USER);
        createSettingUseCase = new CreateSettingUseCase(
                settingRepository,
                dataEventsNotificator
        );
    }

    @Test
    public void create() {
        when(settingRepository.findByProperty(SETTING.getProperty())).thenReturn(List.of(Setting.builder().build()));
        when(settingRepository.insert(any())).thenReturn(SETTING);

        Setting insertedSetting = createSettingUseCase.execute(SETTING);

        verify(settingRepository).findByProperty(SETTING.getProperty());
        verify(settingRepository).insert(settingCaptor.capture());
        verify(dataEventsNotificator).notifySettingChanges();
        assertThat(insertedSetting).isEqualTo(SETTING);
    }

    @Test
    public void create_nullSetting() {
        assertThatThrownBy(() -> createSettingUseCase.execute(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Setting cannot be null.");

        verifyNoInteractions(settingRepository);
        verifyNoInteractions(dataEventsNotificator);
    }

    @Test
    public void create_defaultSetting() {
        Setting defaultSetting = SETTING.toBuilder().type(Setting.Type.DEFAULT).build();

        assertThatThrownBy(() -> createSettingUseCase.execute(defaultSetting))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("You can only create override settings.");

        verifyNoInteractions(settingRepository);
        verifyNoInteractions(dataEventsNotificator);
    }

    @Test
    public void create_settingWithoutDefaultParent() {
        when(settingRepository.findByProperty(SETTING.getProperty()))
                .thenReturn(List.of());

        assertThatThrownBy(() -> createSettingUseCase.execute(SETTING))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("You must have a default setting to make an override.");

        verify(settingRepository).findByProperty(SETTING.getProperty());
        verify(settingRepository, never()).insert(any());
        verifyNoInteractions(dataEventsNotificator);
    }
}
