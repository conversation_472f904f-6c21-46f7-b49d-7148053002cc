package pt.jumia.services.easytaxscan.domain.usecases.queries;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.utils.ResourceLoader;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class JsonExtractorUseCaseTest {

    @InjectMocks
    private JsonExtractorUseCase jsonExtractor;

    @Mock
    private ObjectMapper objectMapper;

    private String sampleJson;

    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        jsonExtractor = new JsonExtractorUseCase(objectMapper);

        sampleJson = ResourceLoader.getStringFromFile("query/sampleresult.json");
    }

    @Test
    void shouldExtractKeysFromSampleJson() {
        // When
        List<String> keys = jsonExtractor.analyzeJson(sampleJson);

        // Then
        assertThat(keys).isNotEmpty();

        assertThat(keys).contains(
                "context.id",
                "context.tracking_number",
                "context.shipping_cost",
                "context.items",
                "context.items[0]",
                "context.items[0].item_name",
                "context.items[0].item_details",
                "context.items[0].item_details[0]",
                "context.items[0].item_details[0].sku",
                "context.items[1].item_details[0].capacity",
                "context.items[2].item_details[2].description",
                "context.customer.name",
                "context.shipping_address.country"
        );

        assertThat(keys).contains("context.items[0].item_details[1].coverage[0]");
        assertThat(keys).contains("context.items[3].item_details[0].material");

        assertThat(keys.size()).isGreaterThan(100);
    }

    @Test
    void shouldHandleEmptyJsonObject() {
        // Given
        String json = "{}";

        // When
        List<String> keys = jsonExtractor.analyzeJson(json);

        // Then
        assertThat(keys).isEmpty();
    }

    @Test
    void shouldThrowExceptionForInvalidJson() {
        // Given
        String invalidJson = "{\"key\": missing_quotes}";

        // When & Then
        Exception exception = assertThrows(RuntimeException.class, () ->
                jsonExtractor.analyzeJson(invalidJson)
        );
        assertThat(exception.getMessage()).contains("Error analyzing JSON");
    }

    @Test
    void shouldConvertKeysListToJsonArray() throws Exception {
        // Given
        List<String> keys = List.of("context.key1", "context.key2", "context.key3");

        // When
        String jsonArray = jsonExtractor.getKeysAsJsonArray(keys);

        // Then
        List<String> parsedKeys = objectMapper.readValue(jsonArray, List.class);
        assertThat(parsedKeys).containsExactly("context.key1", "context.key2", "context.key3");
    }

}