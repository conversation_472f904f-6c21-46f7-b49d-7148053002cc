package pt.jumia.services.easytaxscan.domain.usecases.tag;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Tag;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeTags;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.TagRepository;
import pt.jumia.services.easytaxscan.domain.usecases.tags.DeleteTagsUseCase;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link DeleteTagsUseCase}
 * <p>
 * Created by ruisoares on 11/20/17.
 */
@ExtendWith(MockitoExtension.class)
public class DeleteTagsUseCaseTest {

    private static final Tag TAG = FakeTags.ORDER_CANCELED.toBuilder().id(1L).build();

    @Mock
    private TagRepository tagRepository;

    @InjectMocks
    private DeleteTagsUseCase deleteTagsUseCase;

    @Test
    public void deleteTag_success() {
        when(tagRepository.findById(TAG.getId())).thenReturn(Optional.of(TAG));
        deleteTagsUseCase.execute(TAG.getId());
        verify(tagRepository).findById(eq(TAG.getId()));
        verify(tagRepository).deleteById(eq(TAG.getId()));
    }

    @Test
    public void deleteTag_nullable() {
        when(tagRepository.findById(eq(TAG.getId()))).thenReturn(Optional.empty());
        assertThrows(NotFoundException.class, () ->
                deleteTagsUseCase.execute(TAG.getId())
        );
    }
}
