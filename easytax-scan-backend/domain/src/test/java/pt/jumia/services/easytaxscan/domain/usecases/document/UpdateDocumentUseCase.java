package pt.jumia.services.easytaxscan.domain.usecases.document;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.repository.DocumentRepository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;
import static pt.jumia.services.easytaxscan.domain.entities.fake.FakeDocument.DOCUMENT_UPDATE;

class UpdateDocumentUseCaseTest {

    private DocumentRepository documentRepository;
    private UpdateDocumentUseCase updateDocumentUseCase;

    @BeforeEach
    void setUp() {
        documentRepository = mock(DocumentRepository.class);
        updateDocumentUseCase = new UpdateDocumentUseCase(documentRepository);
    }

    @Test
    void updateStatus_shouldUpdateFieldsAndCallRepository() {
        Document doc = DOCUMENT_UPDATE.toBuilder().build();
        Document.Status newStatus = Document.Status.SUBMITTED;
        updateDocumentUseCase.updateStatus(doc, newStatus);
        ArgumentCaptor<Document> captor = ArgumentCaptor.forClass(Document.class);
        verify(documentRepository, times(1)).update(captor.capture());

        Document updatedDoc = captor.getValue();
        assertEquals(newStatus, updatedDoc.getStatus());
        assertEquals("system", updatedDoc.getUpdatedBy());
        assertNotNull(updatedDoc.getUpdatedAt());
    }
}
