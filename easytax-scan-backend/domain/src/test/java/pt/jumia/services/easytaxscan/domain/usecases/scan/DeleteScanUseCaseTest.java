package pt.jumia.services.easytaxscan.domain.usecases.scan;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;


/**
 * Unit test for {@link DeleteScanUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class DeleteScanUseCaseTest {

    @Mock
    private ScanRepository ScanRepository;

    @Mock
    private ReadScanUseCase readScanUseCase;

    @InjectMocks
    private DeleteScanUseCase deleteScanUseCase;

    @Test
    public void execute_success() throws NotFoundException {
        Long id = 1L;
        Scan Scan = FakeScan.SCAN_CREATE;

        when(readScanUseCase.execute(id)).thenReturn(Scan);
        deleteScanUseCase.execute(id);

        verify(readScanUseCase).execute(id);
        verify(ScanRepository).deleteById(id);
    }

    @Test
    public void execute_notFoundException() {
        Long id = 1L;

        when(readScanUseCase.execute(id)).thenThrow(NotFoundException.class);

        assertThrows(NotFoundException.class, () -> deleteScanUseCase.execute(id));

        verify(readScanUseCase).execute(id);
        verify(ScanRepository, never()).deleteById(id);
    }
}

