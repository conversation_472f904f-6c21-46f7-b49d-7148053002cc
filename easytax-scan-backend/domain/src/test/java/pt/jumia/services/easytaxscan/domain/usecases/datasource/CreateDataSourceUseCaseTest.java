package pt.jumia.services.easytaxscan.domain.usecases.datasource;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.exceptions.RecordAlreadyExistsException;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceRepository;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit test for {@link CreateDataSourceUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class CreateDataSourceUseCaseTest {

    @Mock
    private DataSourceRepository dataSourceRepository;

    @InjectMocks
    private CreateDataSourceUseCase createDataSourceUseCase;

    @Test
    public void execute_success() throws RecordAlreadyExistsException {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_TEST;

        when(dataSourceRepository.findByCode(dataSource.getCode())).thenReturn(Optional.empty());
        when(dataSourceRepository.insert(dataSource)).thenReturn(dataSource);

        DataSource result = createDataSourceUseCase.execute(dataSource);

        assertNotNull(result);
        assertEquals("DSCR5000", result.getCode());
        assertEquals("DataSource Data5 TestCase", result.getDescription());

        verify(dataSourceRepository).findByCode(dataSource.getCode());
        verify(dataSourceRepository).insert(dataSource);
    }

    @Test
    public void execute_illegalArgumentException_nullDataSource() {
        assertThrows(IllegalArgumentException.class, () -> createDataSourceUseCase.execute(null));
    }

    @Test
    public void execute_recordAlreadyExistsException() throws RecordAlreadyExistsException {
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        when(dataSourceRepository.findByCode(dataSource.getCode())).thenReturn(Optional.of(dataSource));

        assertThrows(RecordAlreadyExistsException.class, () -> createDataSourceUseCase.execute(dataSource));

        verify(dataSourceRepository).findByCode(dataSource.getCode());
        verify(dataSourceRepository, never()).insert(dataSource);
    }
}
