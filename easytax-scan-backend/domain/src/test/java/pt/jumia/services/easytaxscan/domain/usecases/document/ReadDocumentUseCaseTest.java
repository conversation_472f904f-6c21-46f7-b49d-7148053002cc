package pt.jumia.services.easytaxscan.domain.usecases.document;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDocument;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentSortFilters;
import pt.jumia.services.easytaxscan.domain.repository.DocumentRepository;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ReadDocumentUseCaseTest {

    @Mock
    private DocumentRepository documentRepository;

    @InjectMocks
    private ReadDocumentUseCase readDocumentUseCase;

    @Test
    public void execute_success_fetchDocuments() {
        // Arrange
        DocumentFilters filters = mock(DocumentFilters.class);
        DocumentSortFilters sortFilters = mock(DocumentSortFilters.class);
        PageFilters pageFilters = mock(PageFilters.class);

        List<Document> expectedDocuments = List.of(FakeDocument.DOCUMENT_CREATE, FakeDocument.DOCUMENT_UPDATE);
        when(documentRepository.findAll(filters, sortFilters, pageFilters)).thenReturn(expectedDocuments);
        List<Document> result = readDocumentUseCase.execute(filters, sortFilters, pageFilters);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(FakeDocument.DOCUMENT_CREATE.getSid(), result.get(0).getSid());
        assertEquals(FakeDocument.DOCUMENT_UPDATE.getSid(), result.get(1).getSid());

        verify(documentRepository, times(1)).findAll(filters, sortFilters, pageFilters);
    }

    @Test
    public void execute_success_countDocuments() {
        // Arrange
        DocumentFilters filters = mock(DocumentFilters.class);
        when(documentRepository.executeCount(filters)).thenReturn(5L);
        Long result = readDocumentUseCase.executeCount(filters);
        assertNotNull(result);
        assertEquals(5L, result);
        verify(documentRepository, times(1)).executeCount(filters);
    }

    @Test
    public void execute_emptyResult_returnsEmptyList() {
        DocumentFilters filters = mock(DocumentFilters.class);
        DocumentSortFilters sortFilters = mock(DocumentSortFilters.class);
        PageFilters pageFilters = mock(PageFilters.class);
        when(documentRepository.findAll(filters, sortFilters, pageFilters)).thenReturn(Collections.emptyList());
        List<Document> result = readDocumentUseCase.execute(filters, sortFilters, pageFilters);
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(documentRepository, times(1)).findAll(filters, sortFilters, pageFilters);
    }


    @Test
    public void execute_repositoryFailure_fetchDocuments_throwsException() {
        DocumentFilters filters = mock(DocumentFilters.class);
        DocumentSortFilters sortFilters = mock(DocumentSortFilters.class);
        PageFilters pageFilters = mock(PageFilters.class);

        when(documentRepository.findAll(filters, sortFilters, pageFilters))
                .thenThrow(new RuntimeException("Database error"));
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> readDocumentUseCase.execute(filters, sortFilters, pageFilters));
        assertEquals("Database error", exception.getMessage());
        verify(documentRepository, times(1)).findAll(filters, sortFilters, pageFilters);
    }

    @Test
    public void execute_repositoryFailure_countDocuments_throwsException() {
        DocumentFilters filters = mock(DocumentFilters.class);
        when(documentRepository.executeCount(filters)).thenThrow(new RuntimeException("Database error"));
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> readDocumentUseCase.executeCount(filters));

        assertEquals("Database error", exception.getMessage());

        verify(documentRepository, times(1)).executeCount(filters);
    }
}
