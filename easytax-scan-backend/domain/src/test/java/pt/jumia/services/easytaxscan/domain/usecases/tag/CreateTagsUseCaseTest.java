package pt.jumia.services.easytaxscan.domain.usecases.tag;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Tag;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeTags;
import pt.jumia.services.easytaxscan.domain.monitoring.TagMonitoring;
import pt.jumia.services.easytaxscan.domain.repository.TagRepository;
import pt.jumia.services.easytaxscan.domain.usecases.tags.CreateTagsUseCase;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link CreateTagsUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class CreateTagsUseCaseTest {

    private static final Tag TAG = FakeTags.CUSTOMER_JOUNERY;

    @Mock
    private TagRepository tagRepository;

    @Mock
    private TagMonitoring tagMonitoring;

    @InjectMocks
    private CreateTagsUseCase createTagsUseCase;

    @Test
    public void createTag_success() {
        when(tagRepository.insert(any(Tag.class))).thenReturn(TAG);

        Tag tag = createTagsUseCase.execute(TAG);

        assertEquals(TAG, tag);
        verify(tagRepository).insert(eq(TAG));
    }

    @Test
    public void createTag_nullable() {
        assertThrows(IllegalArgumentException.class, () ->
                createTagsUseCase.execute(null)
        );
    }
}
