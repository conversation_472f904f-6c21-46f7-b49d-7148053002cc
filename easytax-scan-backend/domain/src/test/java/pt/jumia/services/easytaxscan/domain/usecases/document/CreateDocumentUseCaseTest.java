package pt.jumia.services.easytaxscan.domain.usecases.document;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.BillRequester;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDocument;
import pt.jumia.services.easytaxscan.domain.repository.DocumentRepository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CreateDocumentUseCaseTest {

    @Mock
    private DocumentRepository documentRepository;

    @Mock
    private BillRequester billRequester;

    @Mock
    private UpdateDocumentUseCase updateDocumentUseCase;

    @InjectMocks
    private CreateDocumentUseCase createDocumentUseCase;


    @Test
    void execute_success() {
        Document inputDoc = FakeDocument.DOCUMENT_CREATE.toBuilder().build();
        Document savedDoc = inputDoc.toBuilder().id(1L).build();
        when(documentRepository.insert(inputDoc)).thenReturn(savedDoc);
        when(billRequester.sendDocumentToBill(anyString(), eq(savedDoc))).thenReturn(true);
        doNothing().when(updateDocumentUseCase).updateStatus(savedDoc, Document.Status.SUBMITTED);
        Document result = createDocumentUseCase.execute(inputDoc);
        assertNotNull(result);
        assertEquals(savedDoc.getSid(), result.getSid());
        verify(documentRepository).insert(inputDoc);
        verify(updateDocumentUseCase).updateStatus(savedDoc, Document.Status.SUBMITTED);
    }

    @Test
    public void execute_nullDocument_throwsIllegalArgumentException() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> createDocumentUseCase.execute(null));

        assertEquals("Document to save cannot be null", exception.getMessage());
        verify(documentRepository, never()).insert(any());
    }

    @Test
    public void execute_repositoryFailure_throwsRuntimeException() {
        // Arrange
        Document document = FakeDocument.DOCUMENT_CREATE;
        when(documentRepository.insert(document)).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> createDocumentUseCase.execute(document));

        assertEquals("Database error", exception.getMessage());
        verify(documentRepository, times(1)).insert(document);
    }
}
