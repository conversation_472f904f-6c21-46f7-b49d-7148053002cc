package pt.jumia.services.easytaxscan.domain.usecases.tag;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Tag;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeTags;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.TagRepository;
import pt.jumia.services.easytaxscan.domain.usecases.tags.UpdateTagsUseCase;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link UpdateTagsUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class UpdateTagsUseCaseTest {

    private static final Tag TAG = FakeTags.ORDER_CANCELED.toBuilder().id(1L).build();

    @Mock
    private TagRepository tagRepository;

    @InjectMocks
    private UpdateTagsUseCase updateTagsUseCase;

    @Test
    public void updateTag_nullable() {
        assertThrows(IllegalArgumentException.class, () ->
                updateTagsUseCase.execute(1, null)
        );
    }

    @Test
    public void updateTag_notFound() {
        when(tagRepository.findById(1)).thenReturn(Optional.empty());
        assertThrows(NotFoundException.class, () ->
                updateTagsUseCase.execute(1, TAG)
        );
    }

    @Test
    public void updateTag_success() {
        when(tagRepository.findById(1)).thenReturn(Optional.of(TAG));
        Tag toUpdateTag = TAG.toBuilder().color("#334354").build();
        updateTagsUseCase.execute(1, toUpdateTag);
    }
}
