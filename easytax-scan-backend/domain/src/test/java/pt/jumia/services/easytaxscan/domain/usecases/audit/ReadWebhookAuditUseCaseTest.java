package pt.jumia.services.easytaxscan.domain.usecases.audit;

import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeWebhook;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.WebhookAuditFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.audit.WebhookAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.webhook.ReadWebhooksUseCase;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class ReadWebhookAuditUseCaseTest {

    private static final AuditedEntitySortFilters DEFAULT_AUDITED_ENTITY_SORT_FILTERS = AuditedEntitySortFilters.builder().build();
    private static final PageFilters DEFAULT_PAGE_FILTERS = PageFilters.builder().build();
    private static final WebHooks webHooks = FakeWebhook.WEBHOOK;
    private static final RequestUser REQUEST_USER = RequestUser.builder().build();

    @Mock
    private WebhookAuditRepository webhookAuditRepository;
    @Mock
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @Mock
    private ReadWebhooksUseCase readDocumentUseCase;
    @InjectMocks
    private ReadWebhookAuditUseCase readWebhookAuditUseCase;

    @BeforeAll
    static void setup() {
        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    void executeAuditLog_webhooks_success() {
        AuditedEntity<WebHooks> auditedEntity = AuditedEntity.<WebHooks>builder()
                .entity(webHooks)
                .revisionInfo(AuditedEntity.RevisionInfo.builder()
                        .email("<EMAIL>")
                        .datetime(LocalDateTime.now(ZoneOffset.UTC))
                        .build())
                .operationType(AuditedEntity.OperationType.CREATE)
                .auditedEntity(AuditedEntities.WEBHOOKS)
                .build();

        WebhookAuditFilters invoiceAuditFilters = WebhookAuditFilters.builder()
                .id(1L)
                .sortFilter(DEFAULT_AUDITED_ENTITY_SORT_FILTERS)
                .pageFilters(DEFAULT_PAGE_FILTERS)
                .build();

        when(readDocumentUseCase.findById(anyLong())).thenReturn(webHooks);
        when(webhookAuditRepository.getWebhookAuditLogById(invoiceAuditFilters, webHooks)).thenReturn(List.of(auditedEntity));

        List<AuditedEntity<WebHooks>> auditedEntities = readWebhookAuditUseCase.executeById(1L,
                DEFAULT_AUDITED_ENTITY_SORT_FILTERS, DEFAULT_PAGE_FILTERS);

        verify(webhookAuditRepository).getWebhookAuditLogById(invoiceAuditFilters, webHooks);
        assertThat(auditedEntities).containsExactly(auditedEntity);
    }


    @Test
    void executeAuditLogCount_document_success() {
        WebhookAuditFilters invoiceAuditFilters = WebhookAuditFilters.builder()
                .id(1L)
                .build();

        when(webhookAuditRepository.getAuditLogCountById(invoiceAuditFilters)).thenReturn(2L);
        long count = readWebhookAuditUseCase.executeCountByWebhookID(invoiceAuditFilters.getId());
        verify(webhookAuditRepository).getAuditLogCountById(invoiceAuditFilters);
        AssertionsForClassTypes.assertThat(count).isEqualTo(2L);
    }

    @Test
    void executeAuditLog_document_NotFoundException() {
        when(readDocumentUseCase.findById(anyLong())).thenThrow(NotFoundException.class);

        assertThrows(NotFoundException.class, () ->
                readWebhookAuditUseCase.executeById(1L,
                        DEFAULT_AUDITED_ENTITY_SORT_FILTERS, DEFAULT_PAGE_FILTERS)
        );

    }
}
