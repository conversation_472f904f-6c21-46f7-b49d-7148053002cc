package pt.jumia.services.easytaxscan.domain.usecases.queries;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeSubQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;
import pt.jumia.services.easytaxscan.domain.repository.SubQueryRepository;
import pt.jumia.services.easytaxscan.domain.usecases.subqueries.ReadSubQueryUseCase;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ReadSubQueryUseCaseTest {

    @Mock
    private SubQueryRepository subQueryRepository;

    @InjectMocks
    private ReadSubQueryUseCase readSubQueryUseCase;

    @Test
    public void testExecute_ReadSubQueries_Success() {
        SubQuery subQuery = FakeSubQuery.SUB_QUERY_CREATE;
        SubQueryFilters filters = SubQueryFilters.builder().build();
        SubQuerySortFilters sortFilters = SubQuerySortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<SubQuery> queries = List.of(subQuery);
        when(subQueryRepository.findAll(subQuery.getId(), filters, sortFilters, pageFilters)).thenReturn(queries);
        List<SubQuery> result = readSubQueryUseCase.execute(subQuery.getId(), filters, sortFilters, pageFilters);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(subQuery, result.get(0));
    }

    @Test
    public void testExecute_ReadSubQueries_NoResults() {
        SubQueryFilters filters = SubQueryFilters.builder().build();
        SubQuerySortFilters sortFilters = SubQuerySortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        when(subQueryRepository.findAll(1L, filters, sortFilters, pageFilters)).thenReturn(List.of());  // Return an empty list
        List<SubQuery> result = readSubQueryUseCase.execute(1L, filters, sortFilters, pageFilters);
        assertNotNull(result);
        assertTrue(result.isEmpty(), "The result should be an empty list.");
    }

    @Test
    public void testExecuteCount_ReadSubQueries_CountSuccess() {
        SubQueryFilters filters = SubQueryFilters.builder().build();
        when(subQueryRepository.executeCount(1L, filters)).thenReturn(3L);
        Long count = readSubQueryUseCase.executeCount(1L, filters);
        assertNotNull(count);
        assertEquals(3L, count, "The count should be 3.");
    }

    @Test
    public void testExecute_ReadSubQueries_WithDifferentFilters() {
        SubQuery subQuery1 = FakeSubQuery.SUB_QUERY_CREATE;
        SubQuery subQuery2 = FakeSubQuery.SUB_QUERY_UPDATE;
        SubQueryFilters filters = SubQueryFilters.builder().
                text(String.valueOf(SubQuery.Status.ACTIVE)).build();
        SubQuerySortFilters sortFilters = SubQuerySortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();

        List<SubQuery> queries = List.of(subQuery1, subQuery2);
        when(subQueryRepository.findAll(1L, filters, sortFilters, pageFilters)).thenReturn(queries);
        List<SubQuery> result = readSubQueryUseCase.execute(1L, filters, sortFilters, pageFilters);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(subQuery1));
        assertTrue(result.contains(subQuery2));
    }
}
