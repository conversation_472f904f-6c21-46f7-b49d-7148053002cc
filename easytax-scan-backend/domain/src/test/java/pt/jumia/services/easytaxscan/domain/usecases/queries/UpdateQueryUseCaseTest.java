package pt.jumia.services.easytaxscan.domain.usecases.queries;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.repository.QueryRepository;
import pt.jumia.services.easytaxscan.domain.usecases.validation.ValidateQueryUseCase;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class UpdateQueryUseCaseTest {

    @Mock
    private QueryRepository queryRepository;

    @Mock
    private ValidateQueryUseCase validateQueryUseCase;

    @InjectMocks
    private UpdateQueryUseCase updateQueryUseCase;

    private Query queryToUpdate;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        queryToUpdate = FakeQuery.QUERY_UPDATE;
    }

    @Test
    public void testExecute_UpdateQuery_Success() throws Exception {
        when(queryRepository.update(eq(4L), eq(queryToUpdate))).thenReturn(queryToUpdate);
        doNothing().when(validateQueryUseCase).validate(any(Query.class));
        Query result = updateQueryUseCase.execute(4L, queryToUpdate);
        assertNotNull(result, "The result should not be null.");
        assertEquals(4L, result.getId(), "The ID of the updated query should be 4L.");
        assertEquals("SELECT mainquery, subQuery FROM users WHERE active = true AND id > :fromId AND id IN (:sidList)", result.getSql(), "The SQL query should match.");
        assertEquals("SQL1000", result.getCode(), "The code should match.");
        assertEquals("Query Create Test", result.getDescription(), "The description should match.");
        verify(queryRepository, times(1)).update(eq(4L), eq(queryToUpdate));
        verify(validateQueryUseCase, times(1)).validate(eq(queryToUpdate));
    }

    @Test
    public void testExecute_UpdateQuery_QueryNull() {
        IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class, () -> {
            updateQueryUseCase.execute(4L, null);
        });
        assertEquals("Query to update cannot be null", thrown.getMessage(), "The error message should match.");
    }
}
