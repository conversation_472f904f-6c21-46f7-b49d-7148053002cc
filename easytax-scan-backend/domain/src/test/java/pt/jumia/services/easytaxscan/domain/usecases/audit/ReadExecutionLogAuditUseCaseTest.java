package pt.jumia.services.easytaxscan.domain.usecases.audit;

import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.ExecutionLogAuditFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.audit.ExecutionLogAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.ReadExecutionLogUseCase;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ReadExecutionLogAuditUseCaseTest {

    private static final AuditedEntitySortFilters DEFAULT_SORT_FILTERS = AuditedEntitySortFilters.builder().build();
    private static final PageFilters DEFAULT_PAGE_FILTERS = PageFilters.builder().build();
    private static final ExecutionLog EXECUTION_LOG = ExecutionLog.builder().id(1L).build();

    @Mock
    private ExecutionLogAuditRepository executionLogAuditRepository;

    @Mock
    private ReadExecutionLogUseCase readExecutionLogUseCase;

    @InjectMocks
    private ReadExecutionLogAuditUseCase readExecutionLogAuditUseCase;

    @BeforeAll
    static void setup() {
        RequestContext.clear();
    }

    @Test
    void executeAuditLog_executionLog_success() {
        AuditedEntity<ExecutionLog> auditedEntity = AuditedEntity.<ExecutionLog>builder()
                .entity(EXECUTION_LOG)
                .revisionInfo(AuditedEntity.RevisionInfo.builder()
                        .email("<EMAIL>")
                        .datetime(LocalDateTime.now(ZoneOffset.UTC))
                        .build())
                .operationType(AuditedEntity.OperationType.CREATE)
                .auditedEntity(AuditedEntities.EXECUTION_LOG)
                .build();

        ExecutionLogAuditFilters filters = ExecutionLogAuditFilters.builder()
                .id(1L)
                .sortFilter(DEFAULT_SORT_FILTERS)
                .pageFilters(DEFAULT_PAGE_FILTERS)
                .build();

        when(readExecutionLogUseCase.execute(anyLong())).thenReturn(EXECUTION_LOG);
        when(executionLogAuditRepository.getExecutionLogAuditLogById(filters, EXECUTION_LOG)).thenReturn(List.of(auditedEntity));

        List<AuditedEntity<ExecutionLog>> auditedEntities = readExecutionLogAuditUseCase.executeById(1L, DEFAULT_SORT_FILTERS, DEFAULT_PAGE_FILTERS);

        verify(executionLogAuditRepository).getExecutionLogAuditLogById(filters, EXECUTION_LOG);
        assertThat(auditedEntities).containsExactly(auditedEntity);
    }

    @Test
    void executeAuditLogCount_executionLog_success() {
        ExecutionLogAuditFilters filters = ExecutionLogAuditFilters.builder()
                .id(1L)
                .build();

        when(executionLogAuditRepository.getAuditLogCountById(filters)).thenReturn(2L);
        long count = readExecutionLogAuditUseCase.executeCountByExecutionLogId(filters.getId());

        verify(executionLogAuditRepository).getAuditLogCountById(filters);
        AssertionsForClassTypes.assertThat(count).isEqualTo(2L);
    }

    @Test
    void executeAuditLog_executionLog_NotFoundException() {
        when(readExecutionLogUseCase.execute(anyLong())).thenThrow(NotFoundException.class);

        assertThrows(NotFoundException.class, () ->
                readExecutionLogAuditUseCase.executeById(1L, DEFAULT_SORT_FILTERS, DEFAULT_PAGE_FILTERS));
    }
}
