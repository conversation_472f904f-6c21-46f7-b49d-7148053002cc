package pt.jumia.services.easytaxscan.domain.usecases.setting;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.repository.SettingRepository;
import pt.jumia.services.easytaxscan.domain.settings.OverallSettings;

/**
 * Unit test for {@link ReloadSettingUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class ReloadSettingUseCaseTest {

    private static final List<Setting> SETTINGS = List.of(Setting.builder()
            .id(1L)
            .property("payout_automatic_max_attempts")
            .type(Setting.Type.DEFAULT)
            .description("Max allowed attempts to process automatic queue payouts, using external API.")
            .build());

    @Mock
    private SettingRepository settingRepository;
    @Mock
    private OverallSettings overallSettings;
    @InjectMocks
    private ReloadSettingUseCase reloadSettingsUseCase;

    @Test
    public void reloadSettings() {
        when(settingRepository.findAll()).thenReturn(SETTINGS);

        reloadSettingsUseCase.execute();

        verify(settingRepository).findAll();
        verify(overallSettings).refreshAllSettings(SETTINGS);
    }

    @Test
    public void reloadSettings_givenSettings() {
        reloadSettingsUseCase.execute(SETTINGS);

        verify(overallSettings).refreshAllSettings(SETTINGS);
        verifyNoInteractions(settingRepository);
    }
}
