package pt.jumia.services.easytaxscan.domain.usecases.datasource;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceRepository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link UpdateDataSourceUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class UpdateDataSourceUseCaseTest {

    @Mock
    private DataSourceRepository dataSourceRepository;

    @InjectMocks
    private UpdateDataSourceUseCase updateDataSourceUseCase;

    @Test
    public void execute_success() throws NotFoundException {
        long id = 1L;
        DataSource updatedDataSource = FakeDataSource.DATA_SOURCE_CREATE;
        updatedDataSource.setDescription("DSCR1000_UPDATED");

        when(dataSourceRepository.update(id, updatedDataSource)).thenReturn(updatedDataSource);

        DataSource result = updateDataSourceUseCase.execute(id, updatedDataSource);

        assertNotNull(result);
        assertEquals("DSCR1000_UPDATED", result.getDescription());

        verify(dataSourceRepository).update(id, updatedDataSource);
    }

    @Test
    public void execute_illegalArgumentException_nullDataSource() {
        Long id = 1L;
        assertThrows(IllegalArgumentException.class, () -> updateDataSourceUseCase.execute(id, null));
    }

    @Test
    public void execute_notFoundException() throws NotFoundException {
        long id = 1L;
        DataSource updatedDataSource = FakeDataSource.DATA_SOURCE_UPDATE;
        updatedDataSource.setCode("DSCR2000_UPDATED");

        when(dataSourceRepository.update(id, updatedDataSource)).thenThrow(NotFoundException.class);

        assertThrows(NotFoundException.class, () -> updateDataSourceUseCase.execute(id, updatedDataSource));

        verify(dataSourceRepository).update(id, updatedDataSource);
    }
}

