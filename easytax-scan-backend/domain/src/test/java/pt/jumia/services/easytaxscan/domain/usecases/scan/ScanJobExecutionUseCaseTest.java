package pt.jumia.services.easytaxscan.domain.usecases.scan;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.jobs.ScanJobExecutionUseCase;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;
import pt.jumia.services.easytaxscan.domain.usecases.document.CreateDocumentUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.CreateExecutionLogUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.UpdateExecutionLogUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ValidateDataSourceQueryUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ScanJobExecutionUseCaseTest {

    @Mock
    private ScanRepository scanRepository;
    @Mock
    private CreateExecutionLogUseCase createExecutionLogUseCase;
    @Mock
    private UpdateExecutionLogUseCase updateExecutionLogUseCase;
    @Mock
    private CreateDocumentUseCase createDocumentUseCase;
    @Mock
    private ValidateDataSourceQueryUseCase validateDataSourceQueryUseCase;
    @Mock
    private JsonUtils jsonUtils;

    @InjectMocks
    private ScanJobExecutionUseCase scanJobExecutionUseCase;

    private Scan scan;
    private ExecutionLog executionLog;
    private Document document;

    @BeforeEach
    void setup() {
        scan = FakeScan.SCAN_CREATE;
        executionLog = ExecutionLog.createExecutionlog(scan);
        document = Document.createDocument(executionLog, "{\"test\":\"data\"}");
    }

    @Test
    void execute_shouldReturnDocument_whenSuccessful() throws Exception {
        when(scanRepository.findByCode(scan.getCode())).thenReturn(Optional.of(scan));
        when(createExecutionLogUseCase.execute(any())).thenReturn(executionLog);
        when(validateDataSourceQueryUseCase.constructPreviewResponse(any()))
                .thenReturn(List.of(Map.of("key", Map.of("value", "123"))));
        when(jsonUtils.toJsonOrNull(any())).thenReturn("{\"test\":\"data\"}");
        when(createDocumentUseCase.execute(any())).thenReturn(document);

        Document result = scanJobExecutionUseCase.execute(scan.getCode());

        assertNotNull(result);
        verify(scanRepository).findByCode(scan.getCode());
        verify(createExecutionLogUseCase).execute(any());
        verify(createDocumentUseCase).execute(any());
    }

    @Test
    void execute_shouldReturnNull_whenScanNotFound() {
        when(scanRepository.findByCode("invalid-code")).thenReturn(Optional.empty());
        Document result = scanJobExecutionUseCase.execute("invalid-code");
        assertNull(result);
        verify(scanRepository).findByCode("invalid-code");
        verify(updateExecutionLogUseCase, never()).execute(anyLong(), any());
    }

}
