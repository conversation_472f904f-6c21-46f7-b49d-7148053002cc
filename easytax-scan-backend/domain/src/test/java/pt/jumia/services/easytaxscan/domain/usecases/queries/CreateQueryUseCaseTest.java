package pt.jumia.services.easytaxscan.domain.usecases.queries;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.exceptions.RecordAlreadyExistsException;
import pt.jumia.services.easytaxscan.domain.repository.QueryRepository;
import pt.jumia.services.easytaxscan.domain.repository.SubQueryRepository;
import pt.jumia.services.easytaxscan.domain.usecases.datasource.ReadDataSourceUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.validation.ValidateQueryUseCase;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CreateQueryUseCaseTest {

    @InjectMocks
    private CreateQueryUseCase createQueryUseCase;
    @Mock
    private QueryRepository queryRepository;
    @Mock
    private SubQueryRepository subQueryRepository;
    @Mock
    private ReadDataSourceUseCase readDataSourceUseCase;

    @Mock
    private ValidateQueryUseCase validateQueryUseCase;

    @BeforeEach
    public void setUp() {
        queryRepository = mock(QueryRepository.class);
        subQueryRepository = mock(SubQueryRepository.class);
        validateQueryUseCase = mock(ValidateQueryUseCase.class);
        readDataSourceUseCase = mock(ReadDataSourceUseCase.class);
        createQueryUseCase = new CreateQueryUseCase(queryRepository, subQueryRepository, validateQueryUseCase, readDataSourceUseCase);
    }

    @Test
    public void testCreateQuery_Success() throws Exception {
        Query queryToCreate = FakeQuery.QUERY_CREATE.toBuilder().id(null).build();
        Query insertedQuery = queryToCreate.toBuilder().id(1L).build();
        doNothing().when(validateQueryUseCase).validate(any(Query.class));
        when(queryRepository.insert(any(Query.class))).thenReturn(insertedQuery);
        if (queryToCreate.getSubQueries() != null && !queryToCreate.getSubQueries().isEmpty()) {
            when(subQueryRepository.insert(anyLong(), any(SubQuery.class)))
                    .thenAnswer(invocation -> {
                        Long savedQueryId = invocation.getArgument(0);
                        SubQuery subQuery = invocation.getArgument(1);
                        return subQuery.toBuilder().id(savedQueryId).build();
                    });
        }

        Query createdQuery = createQueryUseCase.execute(queryToCreate);

        assertNotNull(createdQuery);
        assertEquals(insertedQuery.getId(), createdQuery.getId());
        assertEquals(queryToCreate.getCode(), createdQuery.getCode());
        assertEquals(queryToCreate.getDescription(), createdQuery.getDescription());
        verify(validateQueryUseCase, times(1)).validate(queryToCreate);
        if (queryToCreate.getSubQueries() != null && !queryToCreate.getSubQueries().isEmpty()) {
            verify(subQueryRepository, times(queryToCreate.getSubQueries().size()))
                    .insert(eq(insertedQuery.getId()), any(SubQuery.class));
        }
    }


    @Test
    public void testCreateQuery_QueryAlreadyExists() {
        Query queryToCreate = FakeQuery.QUERY_CREATE.toBuilder().id(null).build();

        when(queryRepository.insert(any(Query.class)))
                .thenThrow(new RecordAlreadyExistsException("Query with code " + queryToCreate.getCode() + " already exists"));

        RecordAlreadyExistsException exception = assertThrows(RecordAlreadyExistsException.class, () -> {
            createQueryUseCase.execute(queryToCreate);
        });

        assertEquals("Query with code SQL001 already exists", exception.getMessage().trim().replaceAll("\\.$", ""));
    }


    @Test
    public void testCreateQuery_QueryIsNull() {
        Query queryToCreate = null;
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            createQueryUseCase.execute(queryToCreate);
        });
        assertEquals("Query to save cannot be null", exception.getMessage());
    }

}
