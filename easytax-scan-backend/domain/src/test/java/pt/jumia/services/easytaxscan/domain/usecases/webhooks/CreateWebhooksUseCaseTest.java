package pt.jumia.services.easytaxscan.domain.usecases.webhooks;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeWebhook;
import pt.jumia.services.easytaxscan.domain.repository.WebhooksRepository;
import pt.jumia.services.easytaxscan.domain.usecases.webhook.CreateWebhooksUseCase;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CreateWebhooksUseCaseTest {

    @InjectMocks
    private CreateWebhooksUseCase createWebhooksUseCase;
    @Mock
    private WebhooksRepository webhooksRepository;

    @BeforeEach
    public void setUp() {
        // Mocking the dependencies
        webhooksRepository = mock(WebhooksRepository.class);
        createWebhooksUseCase = new CreateWebhooksUseCase(webhooksRepository);
    }

    @Test
    public void create_Success() {
        WebHooks webHooksToCreate = FakeWebhook.WEBHOOK;
        when(webhooksRepository.insert(webHooksToCreate)).thenReturn(webHooksToCreate);
        WebHooks createdWebhooks = createWebhooksUseCase.execute(webHooksToCreate);
        assertNotNull(createdWebhooks);
        assertEquals(webHooksToCreate.getId(), createdWebhooks.getId());
        assertEquals(webHooksToCreate.getPayload(), createdWebhooks.getPayload());
    }


    @Test
    public void create_Failure() {
        WebHooks webHooksToCreate = null;
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            createWebhooksUseCase.execute(webHooksToCreate);
        });
        assertEquals("Webhooks to save cannot be null.", exception.getMessage());
    }

}
