package pt.jumia.services.easytaxscan.domain.usecases.acl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.AccessController;
import pt.jumia.services.easytaxscan.domain.Permissions;
import pt.jumia.services.easytaxscan.domain.User;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.properties.AclProperties;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class GetAclUserUseCaseTest {

private static final RequestUser REQUEST_USER = RequestUser.builder()
        .username("username")
        .build();

@Mock
private AccessController accessController;
@Mock
private AclProperties aclProperties;
@Mock
private GetAclUserUseCase getAclUserUseCase;

@BeforeEach
public void setUp() {
when(aclProperties.getAppName()).thenReturn("easytaxscan");
getAclUserUseCase = new GetAclUserUseCase(accessController, aclProperties);
}

@Test
void executeUserWithAccess() {
// prepare
Map<String, Map<String, List<String>>> permissionsMap = Map.of(
        "APPLICATION", Map.of(
                "easytaxscan", List.of(
                        Permissions.CAN_ACCESS
                )
        )
);

when(accessController.getPermissions(REQUEST_USER)).thenReturn(permissionsMap);
// execute
User user = getAclUserUseCase.execute(REQUEST_USER);

// verify
assertTrue(user.isCanAccess());
verify(accessController).getPermissions(REQUEST_USER);

}

@Test
void executeUserWithoutAccess() {
// prepare
Map<String, Map<String, List<String>>> permissionsMap = Map.of(
        "APPLICATION", Map.of(
                "TEST", List.of(
                        "another-permission-code"
                )
        )
);
when(accessController.getPermissions(REQUEST_USER)).thenReturn(permissionsMap);

// execute
User user = getAclUserUseCase.execute(REQUEST_USER);

// verify
verify(accessController).getPermissions(REQUEST_USER);

}

@Test
public void executeErrorGettingUserPermissions() {
// prepare
doThrow(AclErrorException.build("")).when(accessController).getPermissions(REQUEST_USER);

// execute and verify
assertThatThrownBy(() -> getAclUserUseCase.execute(REQUEST_USER))
        .isInstanceOf(UserForbiddenException.class);

verify(accessController).getPermissions(REQUEST_USER);
}


}
