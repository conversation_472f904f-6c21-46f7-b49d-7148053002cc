package pt.jumia.services.easytaxscan.domain.usecases.tag;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Tag;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeTags;
import pt.jumia.services.easytaxscan.domain.repository.TagRepository;
import pt.jumia.services.easytaxscan.domain.usecases.tags.ReadTagsUseCase;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link ReadTagsUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class ReadTagsUseCaseTest {

    private static final List<Tag> TAGS = new ArrayList<>(FakeTags.ALL);

    @Mock
    private TagRepository tagRepository;

    @InjectMocks
    private ReadTagsUseCase readTagsUseCase;

    @Test
    public void readTags_success() {
        when(tagRepository.findAll()).thenReturn(TAGS);
        List<Tag> tags = readTagsUseCase.execute();
        assertEquals(TAGS, tags);
        verify(tagRepository).findAll();
    }

    @Test
    public void readTags_empty() {
        when(tagRepository.findAll()).thenReturn(Collections.emptyList());
        List<Tag> tags = readTagsUseCase.execute();
        assertTrue(tags.isEmpty());
        verify(tagRepository).findAll();
    }
}
