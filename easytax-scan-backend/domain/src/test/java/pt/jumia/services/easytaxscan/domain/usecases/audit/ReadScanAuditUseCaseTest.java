package pt.jumia.services.easytaxscan.domain.usecases.audit;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.ScanAuditFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.audit.ScanAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.scan.ReadScanUseCase;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ReadScanAuditUseCaseTest {

    private static final AuditedEntitySortFilters DEFAULT_SORT_FILTERS = AuditedEntitySortFilters.builder().build();
    private static final PageFilters DEFAULT_PAGE_FILTERS = PageFilters.builder().build();
    private static final Scan fakeScan = FakeScan.SCAN_CREATE;

    @Mock
    private ScanAuditRepository scanAuditRepository;
    @Mock
    private ReadScanUseCase readScanUseCase;
    @InjectMocks
    private ReadScanAuditUseCase readScanAuditUseCase;

    @Test
    void executeAuditLog_scan_success() {
        AuditedEntity<Scan> auditedEntity = AuditedEntity.<Scan>builder()
                .entity(fakeScan)
                .revisionInfo(AuditedEntity.RevisionInfo.builder()
                        .email("<EMAIL>")
                        .datetime(LocalDateTime.now(ZoneOffset.UTC))
                        .build())
                .operationType(AuditedEntity.OperationType.CREATE)
                .auditedEntity(AuditedEntities.SCAN)
                .build();

        ScanAuditFilters scanAuditFilters = ScanAuditFilters.builder()
                .id(1L)
                .sortFilter(DEFAULT_SORT_FILTERS)
                .pageFilters(DEFAULT_PAGE_FILTERS)
                .build();

        when(readScanUseCase.execute(anyLong())).thenReturn(fakeScan);
        when(scanAuditRepository.getScanAuditLogById(scanAuditFilters, fakeScan)).thenReturn(List.of(auditedEntity));

        List<AuditedEntity<Scan>> auditedEntities = readScanAuditUseCase.executeById(1L,
                DEFAULT_SORT_FILTERS, DEFAULT_PAGE_FILTERS);

        verify(scanAuditRepository).getScanAuditLogById(scanAuditFilters, fakeScan);
        assertThat(auditedEntities).containsExactly(auditedEntity);
    }

    @Test
    void executeAuditLogCount_scan_success() {
        ScanAuditFilters scanAuditFilters = ScanAuditFilters.builder()
                .id(1L)
                .build();

        when(scanAuditRepository.getAuditLogCountById(scanAuditFilters)).thenReturn(2L);
        long count = readScanAuditUseCase.executeCountByScanId(scanAuditFilters.getId());
        verify(scanAuditRepository).getAuditLogCountById(scanAuditFilters);
        assertThat(count).isEqualTo(2L);
    }

    @Test
    void executeAuditLog_scan_NotFoundException() {
        when(readScanUseCase.execute(anyLong())).thenThrow(NotFoundException.class);

        assertThrows(NotFoundException.class, () ->
                readScanAuditUseCase.executeById(1L,
                        DEFAULT_SORT_FILTERS, DEFAULT_PAGE_FILTERS)
        );
    }
}
