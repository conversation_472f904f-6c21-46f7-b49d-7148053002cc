package pt.jumia.services.easytaxscan.domain.usecases.setting;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.SettingRepository;

/**
 * Unit test for {@link DeleteSettingUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class DeleteSettingUseCaseTest {

    private static final Setting SETTING = Setting.builder()
            .id(1L)
            .property("payout_automatic_max_attempts")
            .type(Setting.Type.OVERRIDE)
            .value("3")
            .overrideKey("seller-ng-jumia")
            .description("Max allowed attempts to process automatic queue payouts, using external API.")
            .build();
    @Mock
    private SettingRepository settingRepository;
    @Mock
    private ReadSettingUseCase readSettingUseCase;
    @InjectMocks
    private DeleteSettingUseCase deleteSettingUseCase;

    @Test
    public void deleteSetting() {
        when(readSettingUseCase.findById(SETTING.getId())).thenReturn(SETTING);

        Setting setting = deleteSettingUseCase.execute(SETTING.getId());

        assertThat(setting).isEqualTo(SETTING);
        verify(readSettingUseCase).findById(SETTING.getId());
        verify(settingRepository).deleteById(SETTING.getId());
    }

    @Test
    public void deleteSetting_settingNotFound() {
        when(readSettingUseCase.findById(SETTING.getId()))
                .thenThrow(NotFoundException.build(Setting.class, SETTING.getId()));

        assertThatThrownBy(() -> deleteSettingUseCase.execute(SETTING.getId()))
                .isInstanceOf(NotFoundException.class);

        verify(readSettingUseCase).findById(SETTING.getId());
        verifyNoInteractions(settingRepository);
    }

    @Test
    public void deleteSetting_defaultSetting() {
        Setting setting = SETTING.toBuilder().type(Setting.Type.DEFAULT).build();

        when(readSettingUseCase.findById(setting.getId())).thenReturn(setting);

        assertThatThrownBy(() -> deleteSettingUseCase.execute(SETTING.getId()))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Only settings of type OVERRIDE can be deleted.");
    }
}
