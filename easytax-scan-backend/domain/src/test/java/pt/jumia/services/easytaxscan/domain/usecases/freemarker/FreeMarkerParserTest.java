package pt.jumia.services.easytaxscan.domain.usecases.freemarker;

import freemarker.template.Configuration;
import freemarker.template.Template;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.CharacterOverridePair;
import pt.jumia.services.easytaxscan.domain.caches.TemplatesCache;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeMessages;
import pt.jumia.services.easytaxscan.domain.entities.mappingtemplates.WrappedMessage;
import pt.jumia.services.easytaxscan.domain.settings.CharacterOverrideSettings;
import pt.jumia.services.easytaxscan.domain.settings.OverallSettings;
import pt.jumia.services.easytaxscan.domain.templates.freemarker.FreeMarkerParser;
import pt.jumia.services.easytaxscan.domain.usecases.templates.SanitizeUseCase;
import pt.jumia.services.easytaxscan.domain.utils.BeanUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

/**
 * Unit tests for {@link pt.jumia.services.easytaxscan.domain.templates.freemarker.FreeMarkerParser}.
 */
@ExtendWith(MockitoExtension.class)
class FreeMarkerParserTest {

    private static final Configuration CONFIGURATION = new Configuration(Configuration.getVersion());
    private static final String TEMPLATE_NAME = "FreeMarkerParserTest";

    private Map<String, Object> map = new HashMap<>();

    @Mock
    private SanitizeUseCase sanitizeUseCase;
    private FreeMarkerParser freeMarkerParser;

    @Mock
    private TemplatesCache templatesCache;

    @Mock
    private OverallSettings overallSettings;

    @Mock
    private CharacterOverrideSettings characterOverrideSettings;

    @BeforeEach
    void setUp() {
        freeMarkerParser = new FreeMarkerParser(sanitizeUseCase, templatesCache);
    }

    @Test
    void execute_staticTemplate() throws IOException {
        // prepare
        when(sanitizeUseCase.execute(Map.of())).thenReturn(Map.of());

        String name = "new";
        String stringTemplate = "This is a test";
        Template fakeFreeMarkerTemplate = new Template(name, stringTemplate, CONFIGURATION);
        when(templatesCache.getTemplate(name, stringTemplate)).thenReturn(fakeFreeMarkerTemplate);

        // execute
        String rendered = freeMarkerParser.execute(
                name, stringTemplate, Map.of(), WrappedMessage.wrap(FakeMessages.MESSAGE_1));

        // verify
        assertThat(rendered).isEqualTo("This is a test");
        verify(sanitizeUseCase).execute(Map.of());
    }

    @Test
    void execute_simpleCalculation() throws IOException {
        // prepare
        when(sanitizeUseCase.execute(Map.of())).thenReturn(Map.of());

        String name = "new";
        String stringTemplate = "1 + 2 = ${1 + 2}";
        Template fakeFreeMarkerTemplate = new Template(name, stringTemplate, CONFIGURATION);
        when(templatesCache.getTemplate(name, stringTemplate)).thenReturn(fakeFreeMarkerTemplate);

        // execute
        String rendered = freeMarkerParser.execute(
                name, stringTemplate, Map.of(), WrappedMessage.wrap(FakeMessages.MESSAGE_1));

        // verify
        assertThat(rendered).isEqualTo("1 + 2 = 3");
        verify(sanitizeUseCase).execute(Map.of());
    }

    @Test
    void execute_singleVariable() throws IOException {
        // prepare
        map = Map.of("name", "world");
        when(sanitizeUseCase.execute(map)).thenReturn(map);

        String name = "new";
        String stringTemplate = "Hello ${context.name}";
        Template fakeFreeMarkerTemplate = new Template(name, stringTemplate, CONFIGURATION);
        when(templatesCache.getTemplate(name, stringTemplate)).thenReturn(fakeFreeMarkerTemplate);

        // execute
        String rendered = freeMarkerParser.execute(
                name, stringTemplate, map, WrappedMessage.wrap(FakeMessages.MESSAGE_1));

        // verify
        assertThat(rendered).isEqualTo("Hello world");
        verify(sanitizeUseCase).execute(map);
    }

    @Test
    void execute_nestedVariable() throws IOException {
        // prepare
        map = Map.of("user", Map.of("name", "world"));
        when(sanitizeUseCase.execute(map))
                .thenReturn(map);

        String name = "name";
        String stringTemplate = "Hello ${context.user.name}";
        Template fakeFreeMarkerTemplate = new Template(name, stringTemplate, CONFIGURATION);
        when(templatesCache.getTemplate(name, stringTemplate)).thenReturn(fakeFreeMarkerTemplate);

        // execute
        String rendered = freeMarkerParser.execute(name, stringTemplate, map, WrappedMessage.wrap(FakeMessages.MESSAGE_1));

        // verify
        assertThat(rendered).isEqualTo("Hello world");
        verify(sanitizeUseCase).execute(map);
    }

    @Test
    void execute_processList() throws IOException {
        // prepare
        map = Map.of("users", List.of(Map.of("name", "first"), Map.of("name", "second")));
        when(sanitizeUseCase.execute(map)).thenReturn(map);

        String name = "new";
        String stringTemplate = "<#list context.users as user>" +
                "${user.name} " +
                "</#list>";
        Template fakeFreeMarkerTemplate = new Template(name, stringTemplate, CONFIGURATION);
        when(templatesCache.getTemplate(name, stringTemplate)).thenReturn(fakeFreeMarkerTemplate);

        // execute
        String rendered = freeMarkerParser.execute(name, stringTemplate, map, WrappedMessage.wrap(FakeMessages.MESSAGE_1));
        assertThat(rendered).isEqualTo("first second ");
        verify(sanitizeUseCase).execute(Map.of("users", List.of(
                Map.of("name", "first"),
                Map.of("name", "second"))));
    }

    @Test
    void execute_formatLocale() throws IOException {
        // prepare
        when(sanitizeUseCase.execute(Map.of())).thenReturn(Map.of());

        String name = "new";
        String stringTemplate = "Rounded Number ${numberUtils.formatLocale(100023.99)}";
        Template fakeFreeMarkerTemplate = new Template(name, stringTemplate, CONFIGURATION);
        when(templatesCache.getTemplate(name, stringTemplate)).thenReturn(fakeFreeMarkerTemplate);

        String rendered = freeMarkerParser.execute(
                name, stringTemplate,
                Map.of(),
                WrappedMessage.wrap(FakeMessages.MESSAGE_1));
        assertThat(rendered).isEqualTo("Rounded Number 100,023.99");
    }

    @Test
    void execute_compactNumberFormat() throws IOException {
        // prepare
        when(sanitizeUseCase.execute(Map.of())).thenReturn(Map.of());

        String name = "new";
        String stringTemplate = "Compacted Number ${numberUtils.compactNumberFormat(1259.0,1)}";
        Template fakeFreeMarkerTemplate = new Template(name, stringTemplate, CONFIGURATION);
        when(templatesCache.getTemplate(name, stringTemplate)).thenReturn(fakeFreeMarkerTemplate);

        String rendered = freeMarkerParser.execute(
                name, stringTemplate,
                Map.of(),
                WrappedMessage.wrap(FakeMessages.MESSAGE_1));
        assertThat(rendered).isEqualTo("Compacted Number 1.3K");
    }

    @Test
    void execute_dateUtils() throws IOException {
        // prepare
        when(sanitizeUseCase.execute(Map.of())).thenReturn(Map.of());
        String stringTemplate = "${dateUtils.format(dateUtils.parse(\"2023-02-17 17:09\", \"yyyy-MM-dd HH:mm\"), \"yyyy-MM-dd HH:mm\")}";
        Template fakeFreeMarkerTemplate = new Template(TEMPLATE_NAME, stringTemplate, CONFIGURATION);
        when(templatesCache.getTemplate(TEMPLATE_NAME, stringTemplate)).thenReturn(fakeFreeMarkerTemplate);

        // execute
        String rendered = freeMarkerParser.execute(
                TEMPLATE_NAME,
                stringTemplate,
                Map.of(),
                WrappedMessage.wrap(FakeMessages.MESSAGE_1)
        );
        assertThat(rendered).isEqualTo("2023-02-17 17:09");
    }

    @Test
    void execute_stringUtilsTruncate() throws IOException {
        // prepare
        when(sanitizeUseCase.execute(Map.of())).thenReturn(Map.of());
        String stringTemplate = "${stringUtils.truncate(\"batatas com ervilhas\", 7)}";
        Template fakeFreeMarkerTemplate = new Template(TEMPLATE_NAME, stringTemplate, CONFIGURATION);
        when(templatesCache.getTemplate(TEMPLATE_NAME, stringTemplate)).thenReturn(fakeFreeMarkerTemplate);

        // execute
        String rendered = freeMarkerParser.execute(
                TEMPLATE_NAME,
                stringTemplate,
                Map.of(),
                WrappedMessage.wrap(FakeMessages.MESSAGE_1)
        );
        assertThat(rendered).isEqualTo("batatas");
    }

    @Test
    void execute_characterOverride_replacesCorrectly() throws IOException {
        // GIVEN
        String overrideKey = "testKey";
        List<CharacterOverridePair> overrides = new ArrayList<>();
        overrides.add(new CharacterOverridePair('H', 'J'));
        overrides.add(new CharacterOverridePair('W', 'V'));

        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            mockedBeanUtils.when(() -> BeanUtils.getBean(OverallSettings.class)).thenReturn(overallSettings);
            when(overallSettings.getCharacterOverrideSettings()).thenReturn(characterOverrideSettings);
            when(characterOverrideSettings.getOverrides(overrideKey)).thenReturn(overrides);
            when(sanitizeUseCase.execute(Map.of())).thenReturn(Map.of());
            String stringTemplate = "${characterOverrideUtils.replaceCharacters(\"Hello World!\", \"testKey\")}";
            Template fakeFreeMarkerTemplate = new Template(TEMPLATE_NAME, stringTemplate, CONFIGURATION);
            when(templatesCache.getTemplate(TEMPLATE_NAME, stringTemplate)).thenReturn(fakeFreeMarkerTemplate);

            // WHEN
            String rendered = freeMarkerParser.execute(
                    TEMPLATE_NAME,
                    stringTemplate,
                    Map.of(),
                    WrappedMessage.wrap(FakeMessages.MESSAGE_1)
            );
            // THEN
            assertEquals("Jello Vorld!", rendered);
        }
    }
}
