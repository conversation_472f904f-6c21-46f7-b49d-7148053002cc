package pt.jumia.services.easytaxscan.domain.usecases.queries;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.properties.DatabaseProperties;
import pt.jumia.services.easytaxscan.domain.properties.DatabaseProperties.DatasourceProperties;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceFactory;
import pt.jumia.services.easytaxscan.domain.repository.EntityDataSourceRepository;
import pt.jumia.services.easytaxscan.domain.usecases.subqueries.ReadSubQueryUseCase;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ValidateDataSourceQueryUseCaseTest {

    @Mock
    private DataSourceFactory dataSourceFactory;

    @Mock
    private DatabaseProperties databaseProperties;

    @Mock
    private EntityDataSourceRepository dataSourceRepository;

    @InjectMocks
    private ValidateDataSourceQueryUseCase validateDataSourceQueryUseCase;

    @Mock
    private ReadQueryUseCase readQueryUseCase;
    @Mock
    private ReadSubQueryUseCase readSubQueryUseCase;

    private Query query;
    private Map<String, Object> args;
    private final String countryCode = "KE";
    private final String dataSourceCode = "test_datasource";
    private Map<String, DatabaseProperties.DataSource> dataSourcesMap;
    private DatabaseProperties.DataSource dataSourceConfig;

    @BeforeEach
    void setUp() {
        // Setup test data
        DataSource dataSource = new DataSource();
        dataSource.setCode(dataSourceCode);
        dataSource.setCountrySegregated(false);

        query = new Query();
        query.setDataSource(dataSource);
        query.setSql("SELECT * FROM test_table WHERE id = :id");

        args = new HashMap<>();
        args.put("id", 1);

        // Create database properties configuration
        dataSourceConfig = new DatabaseProperties.DataSource();
        DatabaseProperties.ConnectionConfig connectionConfig = new DatabaseProperties.ConnectionConfig();
        connectionConfig.setUrl("jdbc:h2:mem:test");
        connectionConfig.setUsername("sa");
        connectionConfig.setPassword("");
        connectionConfig.setDriver("org.h2.Driver");
        dataSourceConfig.setConnection(connectionConfig);

        // Setup datasource properties map
        dataSourcesMap = new HashMap<>();
        dataSourcesMap.put(dataSourceCode, dataSourceConfig);

        // Mock database properties
        when(databaseProperties.getDataSources()).thenReturn(dataSourcesMap);
        when(dataSourceFactory.getDatasourceAdapter(any(DatasourceProperties.class))).thenReturn(dataSourceRepository);
    }

    @Test
    void validate_success() {
        // Mock successful query execution
        Map<String, Object> resultRow = new HashMap<>();
        resultRow.put("count", 5);
        List<Map<String, Object>> queryResults = Collections.singletonList(resultRow);

        when(dataSourceRepository.query(anyString(), any())).thenReturn(queryResults);

        // Execute test
        HealthStatus result = validateDataSourceQueryUseCase.validate(query, args, countryCode);

        // Verify
        assertTrue(result.isSuccess());
        assertNull(result.getError());
        verify(dataSourceFactory).getDatasourceAdapter(any(DatasourceProperties.class));
        verify(dataSourceRepository).query(eq(query.getSql()), eq(args));
    }

    @Test
    void validate_noCountColumn() {
        // Mock query execution with no count column
        Map<String, Object> resultRow = new HashMap<>();
        resultRow.put("some_other_column", "value");
        List<Map<String, Object>> queryResults = Collections.singletonList(resultRow);

        when(dataSourceRepository.query(anyString(), any())).thenReturn(queryResults);

        // Execute test
        HealthStatus result = validateDataSourceQueryUseCase.validate(query, args, countryCode);

        // Verify
        assertTrue(result.isSuccess());
        assertNull(result.getError());
    }

    @Test
    void validate_queryExecutionThrowsException() {
        // Create a nested exception structure as in your implementation
        Exception cause = new RuntimeException("Database error");
        RuntimeException dbException = new RuntimeException("Query failed", cause);

        when(dataSourceRepository.query(anyString(), any())).thenThrow(dbException);

        // Execute test
        HealthStatus result = validateDataSourceQueryUseCase.validate(query, args, countryCode);

        // Verify
        assertFalse(result.isSuccess());
        assertNotNull(result.getError());
        assertTrue(result.getError().contains("Failed to validate query"));
    }


    @Test
    void validate_countrySegregatedDataSource() {
        // Setup country segregated data source
        DataSource dataSource = new DataSource();
        dataSource.setCode(dataSourceCode);
        dataSource.setCountrySegregated(true);

        Query segregatedQuery = new Query();
        segregatedQuery.setDataSource(dataSource);
        segregatedQuery.setSql("SELECT * FROM country_table");

        // Create a country-specific datasource properties
        DatabaseProperties.DatasourceProperties countryProps = new DatabaseProperties.DatasourceProperties();
        DatabaseProperties.ConnectionConfig countryConnection = new DatabaseProperties.ConnectionConfig();
        countryConnection.setUrl("jdbc:h2:mem:ke");
        countryConnection.setUsername("ke_user");
        countryConnection.setPassword("ke_pass");
        countryProps.setConnection(countryConnection);

        // Mock database properties for country-specific data source
        when(databaseProperties.getDataSourceProperties(dataSourceCode, countryCode))
                .thenReturn(countryProps);

        // Mock successful query execution
        Map<String, Object> resultRow = new HashMap<>();
        resultRow.put("count", 10);
        List<Map<String, Object>> queryResults = Collections.singletonList(resultRow);

        when(dataSourceRepository.query(anyString(), any())).thenReturn(queryResults);

        // Execute test
        HealthStatus result = validateDataSourceQueryUseCase.validate(segregatedQuery, args, countryCode);

        // Verify
        assertTrue(result.isSuccess());
        assertNull(result.getError());
        verify(databaseProperties).getDataSourceProperties(dataSourceCode, countryCode);
        verify(dataSourceFactory).getDatasourceAdapter(same(countryProps));
    }

    @Test
    void constructPreviewResponse_success() throws Exception {
        QueryPreview preview = new QueryPreview();
        Map<String, Object> resultRow = new HashMap<>();
        resultRow.put("id", 10);
        List<Map<String, Object>> queryResults = Collections.singletonList(resultRow);
        preview.setQueryId(1L);
        when(readQueryUseCase.findById(any())).thenReturn(query);
        List<SubQuery> subQueryList = new ArrayList<>();
        when(readSubQueryUseCase.execute(any(), any(), any(), any())).thenReturn(subQueryList);
        when(dataSourceRepository.query(anyString(), any())).thenReturn(queryResults);
        List<Map<String, Map<String, Object>>> result = validateDataSourceQueryUseCase.constructPreviewResponse(preview);
        assertNotNull(result);
    }

    @Test
    void constructPreviewResponseWithoutQueryId_success() throws Exception {
        QueryPreview preview = new QueryPreview();
        Map<String, Object> resultRow = new HashMap<>();
        resultRow.put("id", 10);
        List<Map<String, Object>> queryResults = Collections.singletonList(resultRow);
        preview.setSql(query.getSql());
        preview.setDataSource(query.getDataSource());
        List<SubQuery> subQueryList = new ArrayList<>();
        SubQuery subQuery = new SubQuery();
        subQuery.setQuery(query);
        subQueryList.add(subQuery);
        preview.setSubQueryList(subQueryList);
        when(readQueryUseCase.findById(any())).thenReturn(query);
        when(dataSourceRepository.query(anyString(), any())).thenReturn(queryResults);
        List<Map<String, Map<String, Object>>> result = validateDataSourceQueryUseCase.constructPreviewResponse(preview);
        assertNotNull(result);
    }
}
