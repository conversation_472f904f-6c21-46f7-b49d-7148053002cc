package pt.jumia.services.easytaxscan.domain.usecases.country;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.domain.exceptions.RecordAlreadyExistsException;
import pt.jumia.services.easytaxscan.domain.repository.CountryRepository;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CreateCountryUseCaseTest {

    @Mock
    private CountryRepository countryRepository;

    @InjectMocks
    private CreateCountryUseCase createCountryUseCase;

    @Test
    void execute_shouldCreateCountrySuccessfully() {
        Country country = FakeCountry.COUNTRY_CREATE;
        when(countryRepository.findByCode(country.getCountryCode())).thenReturn(Optional.empty());
        when(countryRepository.insert(country)).thenReturn(country);
        Country result = createCountryUseCase.execute(country);
        assertNotNull(result);
        assertEquals(country, result);
        verify(countryRepository).findByCode(country.getCountryCode());
        verify(countryRepository).insert(country);
    }

    @Test
    void execute_shouldThrowExceptionWhenCountryAlreadyExists() {
        Country country = FakeCountry.COUNTRY_CREATE;
        when(countryRepository.findByCode(country.getCountryCode())).thenReturn(Optional.of(country));
        assertThrows(RecordAlreadyExistsException.class, () -> createCountryUseCase.execute(country));

        verify(countryRepository).findByCode(country.getCountryCode());
        verify(countryRepository, never()).insert(any());
    }

    @Test
    void execute_shouldThrowExceptionWhenCountryIsNull() {
        assertThrows(IllegalArgumentException.class, () -> createCountryUseCase.execute(null));

        verify(countryRepository, never()).findByCode(any());
        verify(countryRepository, never()).insert(any());
    }
}
