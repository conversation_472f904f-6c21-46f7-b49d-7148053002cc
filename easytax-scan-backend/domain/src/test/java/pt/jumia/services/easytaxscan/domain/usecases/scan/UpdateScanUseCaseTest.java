package pt.jumia.services.easytaxscan.domain.usecases.scan;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.jobs.CreateJobsUseCase;
import pt.jumia.services.easytaxscan.domain.jobs.DeleteJobsUseCase;
import pt.jumia.services.easytaxscan.domain.jobs.UpdateJobsUseCase;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link UpdateScanUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class UpdateScanUseCaseTest {

    @Mock
    private ScanRepository ScanRepository;
    @Mock
    private CreateJobsUseCase createJobsUseCase;
    @Mock
    private DeleteJobsUseCase deleteJobsUseCase;
    @Mock
    private UpdateJobsUseCase updateJobsUseCase;

    @InjectMocks
    private UpdateScanUseCase updateScanUseCase;

    @Test
    public void execute_success() throws NotFoundException {
        long id = 1L;
        Scan updatedScan = FakeScan.SCAN_CREATE;
        updatedScan.setDescription("SCAN1000_UPDATED");

        when(ScanRepository.update(id, updatedScan)).thenReturn(updatedScan);

        when(ScanRepository.findById(id)).thenReturn(updatedScan);

        Scan result = updateScanUseCase.execute(id, updatedScan);

        assertNotNull(result);
        assertEquals("SCAN1000_UPDATED", result.getDescription());

        verify(ScanRepository).update(id, updatedScan);
    }

    @Test
    public void execute_illegalArgumentException_nullScan() {
        Long id = 1L;
        assertThrows(IllegalArgumentException.class, () -> updateScanUseCase.execute(id, null));
    }

    @Test
    public void execute_notFoundException() throws NotFoundException {
        long id = 1L;
        Scan updatedScan = FakeScan.SCAN_UPDATE;
        updatedScan.setCode("SCAN2000_UPDATED");

        when(ScanRepository.update(id, updatedScan)).thenThrow(NotFoundException.class);

        assertThrows(NotFoundException.class, () -> updateScanUseCase.execute(id, updatedScan));

        verify(ScanRepository).update(id, updatedScan);
    }
}

