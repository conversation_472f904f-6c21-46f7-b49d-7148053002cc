package pt.jumia.services.easytaxscan.domain.usecases.acl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.User;

@ExtendWith(MockitoExtension.class)
public class ValidateUserAccessUseCaseTest {

    private static final RequestUser REQUEST_USER = RequestUser.builder()
            .username("<EMAIL>")
            .build();

    private static final User USER = User.builder()
            .username(REQUEST_USER.getUsername())
            .build();


    @Mock
    private GetAclUserUseCase getAclUserUseCase;
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @BeforeEach
    public void setUp() {
        validateUserAccessUseCase = new ValidateUserAccessUseCase(getAclUserUseCase);
    }

}
