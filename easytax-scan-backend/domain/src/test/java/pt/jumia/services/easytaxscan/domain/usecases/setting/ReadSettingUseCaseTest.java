package pt.jumia.services.easytaxscan.domain.usecases.setting;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.SettingRepository;

/**
 * Unit test for {@link ReadSettingUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class ReadSettingUseCaseTest {

    private static final Setting SETTING = Setting.builder()
            .id(1L)
            .property("payout_automatic_max_attempts")
            .value("5")
            .description("Max allowed attempts to process automatic queue payouts, using external API.")
            .overrideKey("seller-ng-jumia")
            .build();

    @Mock
    private SettingRepository settingRepository;

    @InjectMocks
    private ReadSettingUseCase readSettingUseCase;


    @Test
    public void fetchById() {
        when(settingRepository.findById(SETTING.getId())).thenReturn(Optional.of(SETTING));

        Setting setting = readSettingUseCase.findById(SETTING.getId());

        verify(settingRepository).findById(SETTING.getId());
        assertThat(setting).isEqualTo(SETTING);
    }


    @Test

    public void fetchById_notFound() {
        when(settingRepository.findById(1L)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> readSettingUseCase.findById(1L))
                .isInstanceOf(NotFoundException.class);
        verify(settingRepository).findById(1L);
    }


    @Test
    public void fetchAll() {
        when(settingRepository.findAll()).thenReturn(List.of(SETTING));

        List<Setting> settings = readSettingUseCase.fetchAll();

        verify(settingRepository).findAll();
        assertThat(settings).isEqualTo(List.of(SETTING));
    }

}
