package pt.jumia.services.easytaxscan.domain.usecases.audit;

import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDocument;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.DocumentAuditFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.audit.DocumentAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.document.ReadDocumentUseCase;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class ReadDocumentAuditUseCaseTest {

    private static final AuditedEntitySortFilters DEFAULT_AUDITED_ENTITY_SORT_FILTERS = AuditedEntitySortFilters.builder().build();
    private static final PageFilters DEFAULT_PAGE_FILTERS = PageFilters.builder().build();
    private static final Document document = FakeDocument.DOCUMENT_CREATE;
    private static final RequestUser REQUEST_USER = RequestUser.builder().build();

    @Mock
    private DocumentAuditRepository documentAuditRepository;
    @Mock
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @Mock
    private ReadDocumentUseCase readDocumentUseCase;
    @InjectMocks
    private ReadDocumentAuditUseCase readDocumentAuditUseCase;

    @BeforeAll
    static void setup() {
        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    void executeAuditLog_document_success() {
        AuditedEntity<Document> auditedEntity = AuditedEntity.<Document>builder()
                .entity(document)
                .revisionInfo(AuditedEntity.RevisionInfo.builder()
                        .email("<EMAIL>")
                        .datetime(LocalDateTime.now(ZoneOffset.UTC))
                        .build())
                .operationType(AuditedEntity.OperationType.CREATE)
                .auditedEntity(AuditedEntities.DOCUMENT)
                .build();

        DocumentAuditFilters invoiceAuditFilters = DocumentAuditFilters.builder()
                .id(1L)
                .sortFilter(DEFAULT_AUDITED_ENTITY_SORT_FILTERS)
                .pageFilters(DEFAULT_PAGE_FILTERS)
                .build();

        when(readDocumentUseCase.findById(anyLong())).thenReturn(document);
        when(documentAuditRepository.getDocumentAuditLogById(invoiceAuditFilters, document)).thenReturn(List.of(auditedEntity));

        List<AuditedEntity<Document>> auditedEntities = readDocumentAuditUseCase.executeById(1L,
                DEFAULT_AUDITED_ENTITY_SORT_FILTERS, DEFAULT_PAGE_FILTERS);

        verify(documentAuditRepository).getDocumentAuditLogById(invoiceAuditFilters, document);
        assertThat(auditedEntities).containsExactly(auditedEntity);
    }


    @Test
    void executeAuditLogCount_document_success() {
        DocumentAuditFilters documentAuditFilters = DocumentAuditFilters.builder()
                .id(1L)
                .build();

        when(documentAuditRepository.getAuditLogCountById(documentAuditFilters)).thenReturn(2L);
        long count = readDocumentAuditUseCase.executeCountByDocumentId(documentAuditFilters.getId());
        verify(documentAuditRepository).getAuditLogCountById(documentAuditFilters);
        AssertionsForClassTypes.assertThat(count).isEqualTo(2L);
    }

    @Test
    void executeAuditLog_document_NotFoundException() {
        when(readDocumentUseCase.findById(anyLong())).thenThrow(NotFoundException.class);

        assertThrows(NotFoundException.class, () ->
                readDocumentAuditUseCase.executeById(1L,
                        DEFAULT_AUDITED_ENTITY_SORT_FILTERS, DEFAULT_PAGE_FILTERS)
        );

    }
}
