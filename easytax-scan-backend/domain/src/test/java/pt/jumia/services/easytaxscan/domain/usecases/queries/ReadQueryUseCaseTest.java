package pt.jumia.services.easytaxscan.domain.usecases.queries;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.QueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.QuerySortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.QueryRepository;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;

import java.util.List;
import java.util.Optional;

@ExtendWith(MockitoExtension.class)
public class ReadQueryUseCaseTest {

    @Mock
    private QueryRepository queryRepository;

    @InjectMocks
    private ReadQueryUseCase readQueryUseCase;

    @Mock
    private JsonExtractorUseCase jsonExtractorUseCase;

    private Query query;

    @BeforeEach
    public void setUp() {
        query = FakeQuery.QUERY_CREATE;
    }

    @Test
    public void testFindByCode_Success() throws NotFoundException {
        String queryCode = query.getCode();  // using FakeQuery.QUERY_CREATE code
        when(queryRepository.findByCode(queryCode)).thenReturn(Optional.of(query));
        Query result = readQueryUseCase.findByCode(queryCode);
        assertNotNull(result);
        assertEquals(queryCode, result.getCode());
        verify(queryRepository, times(1)).findByCode(queryCode);
    }

    @Test
    public void testFindByCode_NotFound() {
        String queryCode = query.getCode();
        when(queryRepository.findByCode(queryCode)).thenReturn(Optional.empty());
        NotFoundException exception = assertThrows(NotFoundException.class, () -> {
            readQueryUseCase.findByCode(queryCode);
        });
        assertEquals("Query with value " + queryCode + " not found", exception.getMessage());
        verify(queryRepository, times(1)).findByCode(queryCode);
    }

    @Test
    public void testExecute_QueryFound() throws NotFoundException {
        when(queryRepository.findById(query.getId())).thenReturn(Optional.of(query));
        Query result = readQueryUseCase.findById(query.getId());
        assertNotNull(result);
        assertEquals(query.getId(), result.getId());
        verify(queryRepository, times(1)).findById(query.getId());
    }

    @Test
    public void testExecute_QueryNotFound() {
        when(queryRepository.findById(query.getId())).thenReturn(Optional.empty());
        NotFoundException exception = assertThrows(NotFoundException.class, () -> {
            readQueryUseCase.findById(query.getId());
        });
        assertEquals("Query with id " + query.getId() + " not found", exception.getMessage());
        verify(queryRepository, times(1)).findById(query.getId());
    }

    @Test
    public void testExecuteCount() {
        QueryFilters filters = new QueryFilters();
        when(queryRepository.executeCount(filters)).thenReturn(5L);
        Long result = readQueryUseCase.executeCount(filters);
        assertEquals(5L, result);
        verify(queryRepository, times(1)).executeCount(filters);
    }

    @Test
    public void testExecute_WithFilters() {
        QueryFilters queryFilters=  QueryFilters.builder().build();
        QuerySortFilters sortFilters = QuerySortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        List<Query> queries = List.of(query, query);
        when(queryRepository.findAll(queryFilters, sortFilters, pageFilters)).thenReturn(queries);
        List<Query> result = readQueryUseCase.execute(queryFilters, sortFilters, pageFilters);
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(queryRepository, times(1)).findAll(queryFilters, sortFilters, pageFilters);
    }

    @Test
    public void testFetchFields_Success() throws NotFoundException {
        // Arrange
        Long queryId = query.getId();
        String sampleResult = "{\"key\": \"value\"}";
        List<String> expectedFields = List.of("context.key");

        Query queryWithSampleResult = Query.builder()
                .id(queryId)
                .sampleResult(sampleResult)
                .build();

        when(queryRepository.findById(queryId)).thenReturn(Optional.of(queryWithSampleResult));
        when(jsonExtractorUseCase.analyzeJson(sampleResult)).thenReturn(expectedFields);

        // Act
        List<String> result = readQueryUseCase.fetchFields(queryId);

        // Assert
        assertNotNull(result);
        assertEquals(expectedFields, result);
        verify(queryRepository, times(1)).findById(queryId);
        verify(jsonExtractorUseCase, times(1)).analyzeJson(sampleResult);
    }

    @Test
    public void testFetchFields_QueryNotFound() {
        // Arrange
        Long queryId = query.getId();
        when(queryRepository.findById(queryId)).thenReturn(Optional.empty());

        // Act & Assert
        NotFoundException exception = assertThrows(NotFoundException.class, () -> {
            readQueryUseCase.fetchFields(queryId);
        });
        assertEquals("Query with id " + queryId + " not found", exception.getMessage());
        verify(queryRepository, times(1)).findById(queryId);
        verify(jsonExtractorUseCase, never()).analyzeJson(any());
    }

    @Test
    public void testFetchFields_EmptySampleResult() {
        // Arrange
        Long queryId = query.getId();
        Query queryWithEmptySampleResult = Query.builder()
                .id(queryId)
                .sampleResult("")
                .build();

        when(queryRepository.findById(queryId)).thenReturn(Optional.of(queryWithEmptySampleResult));

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            readQueryUseCase.fetchFields(queryId);
        });
        assertEquals("Sample result is empty for query ID: " + queryId, exception.getMessage());
        verify(queryRepository, times(1)).findById(queryId);
        verify(jsonExtractorUseCase, never()).analyzeJson(any());
    }

    @Test
    public void testFetchFields_NullSampleResult() {
        // Arrange
        Long queryId = query.getId();
        Query queryWithNullSampleResult = Query.builder()
                .id(queryId)
                .sampleResult(null)
                .build();

        when(queryRepository.findById(queryId)).thenReturn(Optional.of(queryWithNullSampleResult));

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            readQueryUseCase.fetchFields(queryId);
        });
        assertEquals("Sample result is empty for query ID: " + queryId, exception.getMessage());
        verify(queryRepository, times(1)).findById(queryId);
        verify(jsonExtractorUseCase, never()).analyzeJson(any());
    }
}
