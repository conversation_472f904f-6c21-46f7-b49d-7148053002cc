package pt.jumia.services.easytaxscan.domain.usecases.scan;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.exceptions.RecordAlreadyExistsException;
import pt.jumia.services.easytaxscan.domain.jobs.CreateJobsUseCase;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ReadQueryUseCase;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit test for {@link CreateScanUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class CreateScanUseCaseTest {

    @Mock
    private ScanRepository scanRepository;

    @InjectMocks
    private CreateScanUseCase createScanUseCase;

    @Mock
    private ReadQueryUseCase readQueryUseCase;

    @Mock
    private CreateJobsUseCase createJobsUseCase;

    @Mock
    private ScanCountryValidationUseCase scanCountryValidationUseCase;


    @Test
    public void execute_success() throws RecordAlreadyExistsException {
        Scan scan = FakeScan.SCAN_TEST;

        when(scanRepository.findByCode(scan.getCode())).thenReturn(Optional.empty());
        when(scanRepository.insert(scan)).thenReturn(scan);
        when(readQueryUseCase.findById(scan.getQuery().getId())).thenReturn(FakeQuery.QUERY_CREATE);

        Scan result = createScanUseCase.execute(scan);

        assertNotNull(result);
        assertEquals("SCAN5000", result.getCode());
        assertEquals("Scan Data5 TestCase", result.getDescription());

        verify(scanRepository).findByCode(scan.getCode());
        verify(scanRepository).insert(scan);
    }

    @Test
    public void execute_illegalArgumentException_nullScan() {
        assertThrows(IllegalArgumentException.class, () -> createScanUseCase.execute(null));
    }

    @Test
    public void execute_recordAlreadyExistsException() throws RecordAlreadyExistsException {
        Scan scan = FakeScan.SCAN_CREATE;
        when(scanRepository.findByCode(scan.getCode())).thenReturn(Optional.of(scan));
        assertThrows(RecordAlreadyExistsException.class, () -> createScanUseCase.execute(scan));

        verify(scanRepository).findByCode(scan.getCode());
        verify(scanRepository, never()).insert(scan);
    }
}
