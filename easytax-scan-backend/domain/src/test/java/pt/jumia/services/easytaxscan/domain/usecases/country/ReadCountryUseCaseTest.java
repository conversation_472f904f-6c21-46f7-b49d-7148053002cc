package pt.jumia.services.easytaxscan.domain.usecases.country;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.CountryRepository;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReadCountryUseCaseTest {

    @Mock
    private CountryRepository countryRepository;

    @InjectMocks
    private ReadCountryUseCase readCountryUseCase;

    @Test
    void execute_shouldReturnCountryWhenFound() {
        Long countryId = 1L;
        Country expectedCountry = FakeCountry.COUNTRY_CREATE;
        when(countryRepository.findById(countryId)).thenReturn(Optional.of(expectedCountry));
        Country result = readCountryUseCase.execute(countryId);
        assertNotNull(result);
        assertEquals(expectedCountry, result);
        verify(countryRepository).findById(countryId);
    }

    @Test
    void execute_shouldThrowNotFoundExceptionWhenCountryDoesNotExist() {
        Long countryId = 999L;
        when(countryRepository.findById(countryId)).thenReturn(Optional.empty());
        assertThrows(NotFoundException.class, () -> readCountryUseCase.execute(countryId));
        verify(countryRepository).findById(countryId);
    }
}
