package pt.jumia.services.easytaxscan.domain.usecases.scan;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.quartz.SchedulerException;
import pt.jumia.services.easytaxscan.domain.entities.Job;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.jobs.CreateJobsUseCase;
import pt.jumia.services.easytaxscan.domain.repository.JobsRepository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CreateJobsUseCaseTest {

    @Mock
    private JobsRepository jobsRepository;

    @InjectMocks
    private CreateJobsUseCase createJobsUseCase;

    @Test
    public void createJob_success() throws SchedulerException {
        Scan scan = FakeScan.SCAN_CREATE;
        Job expectedJob = Job.builder()
                .jobName(scan.getCode())
                .cronExpression(scan.getCronExpression())
                .build();
        when(jobsRepository.createJob(any(Job.class), anyString())).thenReturn(expectedJob);
        Job result = createJobsUseCase.createScanJob(scan);
        assertNotNull(result);
        assertEquals("0 0 0 15 * ?", result.getCronExpression());
        verify(jobsRepository).createJob(any(Job.class), anyString());
    }

    @Test
    public void createJob_schedulerException() throws SchedulerException {
        Scan scan = FakeScan.SCAN_CREATE;
        when(jobsRepository.createJob(any(Job.class), anyString())).thenThrow(SchedulerException.class);
        assertThrows(SchedulerException.class, () -> createJobsUseCase.createScanJob(scan));
        verify(jobsRepository).createJob(any(Job.class), anyString());
    }
}
