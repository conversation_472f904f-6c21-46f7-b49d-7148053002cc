package pt.jumia.services.easytaxscan.domain.usecases.audit;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.DataSourceAuditFilters;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.audit.DataSourceAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.datasource.ReadDataSourceUseCase;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReadDataSourceAuditUseCaseTest {

    private static final AuditedEntitySortFilters DEFAULT_SORT_FILTERS = AuditedEntitySortFilters.builder().build();
    private static final PageFilters DEFAULT_PAGE_FILTERS = PageFilters.builder().build();
    private static final DataSource fakeDataSource = FakeDataSource.DATA_SOURCE_CREATE;

    @Mock
    private DataSourceAuditRepository dataSourceAuditRepository;

    @Mock
    private ReadDataSourceUseCase readDataSourceUseCase;

    @InjectMocks
    private ReadDataSourceAuditUseCase readDataSourceAuditUseCase;

    @Test
    void executeAuditLog_dataSource_success() {
        AuditedEntity<DataSource> auditedEntity = AuditedEntity.<DataSource>builder()
                .entity(fakeDataSource)
                .revisionInfo(AuditedEntity.RevisionInfo.builder()
                        .email("<EMAIL>")
                        .datetime(LocalDateTime.now(ZoneOffset.UTC))
                        .build())
                .operationType(AuditedEntity.OperationType.CREATE)
                .auditedEntity(AuditedEntities.DATA_SOURCE)
                .build();

        DataSourceAuditFilters dataSourceAuditFilters = DataSourceAuditFilters.builder()
                .id(1L)
                .sortFilter(DEFAULT_SORT_FILTERS)
                .pageFilters(DEFAULT_PAGE_FILTERS)
                .build();

        when(readDataSourceUseCase.execute(anyLong())).thenReturn(fakeDataSource);
        when(dataSourceAuditRepository.getDataSourceAuditLogById(dataSourceAuditFilters, fakeDataSource))
                .thenReturn(List.of(auditedEntity));

        List<AuditedEntity<DataSource>> auditedEntities = readDataSourceAuditUseCase.executeById(
                1L, DEFAULT_SORT_FILTERS, DEFAULT_PAGE_FILTERS);

        verify(dataSourceAuditRepository).getDataSourceAuditLogById(dataSourceAuditFilters, fakeDataSource);
        assertThat(auditedEntities).containsExactly(auditedEntity);
    }

    @Test
    void executeAuditLogCount_dataSource_success() {
        DataSourceAuditFilters dataSourceAuditFilters = DataSourceAuditFilters.builder()
                .id(1L)
                .build();

        when(dataSourceAuditRepository.getAuditLogCountById(dataSourceAuditFilters)).thenReturn(3L);
        long count = readDataSourceAuditUseCase.executeCountByDataSourceId(dataSourceAuditFilters.getId());

        verify(dataSourceAuditRepository).getAuditLogCountById(dataSourceAuditFilters);
        assertThat(count).isEqualTo(3L);
    }

    @Test
    void executeAuditLog_dataSource_NotFoundException() {
        when(readDataSourceUseCase.execute(anyLong())).thenThrow(NotFoundException.class);

        assertThrows(NotFoundException.class, () ->
                readDataSourceAuditUseCase.executeById(
                        1L, DEFAULT_SORT_FILTERS, DEFAULT_PAGE_FILTERS)
        );
    }
}
