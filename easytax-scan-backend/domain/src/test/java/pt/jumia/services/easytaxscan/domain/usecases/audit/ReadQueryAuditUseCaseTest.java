package pt.jumia.services.easytaxscan.domain.usecases.audit;

import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntities;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.QueryAuditFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.audit.QueryAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ReadQueryUseCase;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class ReadQueryAuditUseCaseTest {

    private static final AuditedEntitySortFilters DEFAULT_AUDITED_ENTITY_SORT_FILTERS = AuditedEntitySortFilters.builder().build();
    private static final PageFilters DEFAULT_PAGE_FILTERS = PageFilters.builder().build();
    private static final Query query = FakeQuery.QUERY_CREATE;
    private static final RequestUser REQUEST_USER = RequestUser.builder().build();

    @Mock
    private QueryAuditRepository queryAuditRepository;
    @Mock
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @Mock
    private ReadQueryUseCase readQueryUseCase;
    @InjectMocks
    private ReadQueryAuditUseCase readQueryAuditUseCase;

    @BeforeAll
    static void setup() {
        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    void executeAuditLog_query_success() {
        AuditedEntity<Query> auditedEntity = AuditedEntity.<Query>builder()
                .entity(query)
                .revisionInfo(AuditedEntity.RevisionInfo.builder()
                        .email("<EMAIL>")
                        .datetime(LocalDateTime.now(ZoneOffset.UTC))
                        .build())
                .operationType(AuditedEntity.OperationType.CREATE)
                .auditedEntity(AuditedEntities.QUERY)
                .build();

        QueryAuditFilters invoiceAuditFilters = QueryAuditFilters.builder()
                .id(1L)
                .sortFilter(DEFAULT_AUDITED_ENTITY_SORT_FILTERS)
                .pageFilters(DEFAULT_PAGE_FILTERS)
                .build();

        when(readQueryUseCase.findById(anyLong())).thenReturn(query);
        when(queryAuditRepository.getQueryAuditLogById(invoiceAuditFilters, query)).thenReturn(List.of(auditedEntity));

        List<AuditedEntity<Query>> auditedEntities = readQueryAuditUseCase.executeById(1L,
                DEFAULT_AUDITED_ENTITY_SORT_FILTERS, DEFAULT_PAGE_FILTERS);

        verify(queryAuditRepository).getQueryAuditLogById(invoiceAuditFilters, query);
        assertThat(auditedEntities).containsExactly(auditedEntity);
    }


    @Test
    void executeAuditLogCount_query_success() {
        QueryAuditFilters queryAuditFilters = QueryAuditFilters.builder()
                .id(1L)
                .build();

        when(queryAuditRepository.getAuditLogCountById(queryAuditFilters)).thenReturn(2L);
        long count = readQueryAuditUseCase.executeCountByQueryId(queryAuditFilters.getId());
        verify(queryAuditRepository).getAuditLogCountById(queryAuditFilters);
        AssertionsForClassTypes.assertThat(count).isEqualTo(2L);
    }

    @Test
    void executeAuditLog_query_NotFoundException() {
        when(readQueryUseCase.findById(anyLong())).thenThrow(NotFoundException.class);

        assertThrows(NotFoundException.class, () ->
                readQueryAuditUseCase.executeById(1L,
                        DEFAULT_AUDITED_ENTITY_SORT_FILTERS, DEFAULT_PAGE_FILTERS)
        );

    }
}
