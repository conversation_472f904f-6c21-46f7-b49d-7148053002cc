package pt.jumia.services.easytaxscan.domain.usecases.webhooks;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeWebhook;
import pt.jumia.services.easytaxscan.domain.entities.filter.WebhooksSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.WebhooksRepository;
import pt.jumia.services.easytaxscan.domain.usecases.webhook.ReadWebhooksUseCase;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ReadWebhooksUseCaseTest {

    @Mock
    private WebhooksRepository webhooksRepository;

    @InjectMocks
    private ReadWebhooksUseCase readWebhooksUseCase;

    private WebHooks webHooks;

    @BeforeEach
    public void setUp() {
        webHooks = FakeWebhook.WEBHOOK;
    }

    @Test
    public void findById_Success() throws NotFoundException {
        Long id = webHooks.getId();
        when(webhooksRepository.findById(id)).thenReturn(Optional.of(webHooks));
        WebHooks result = readWebhooksUseCase.findById(id);
        assertNotNull(result);
        assertEquals(id, result.getId());
        verify(webhooksRepository, times(1)).findById(id);
    }

    @Test
    public void findById_NotFound() {
        Long id = webHooks.getId();
        when(webhooksRepository.findById(id)).thenReturn(Optional.empty());
        NotFoundException exception = assertThrows(NotFoundException.class, () -> {
            readWebhooksUseCase.findById(id);
        });
        assertEquals("WebHooks with id " + id + " not found", exception.getMessage());
        verify(webhooksRepository, times(1)).findById(id);
    }

    @Test
    public void executeCount_success() {
        long expectedCount = 5L;

        when(webhooksRepository.executeCount()).thenReturn(expectedCount);

        Long result = readWebhooksUseCase.executeCount();

        assertNotNull(result);
        assertEquals(expectedCount, result);

        verify(webhooksRepository).executeCount();
    }

    @Test
    public void findAll_success() throws NotFoundException {

        WebhooksSortFilters webhooksSortFilters = Mockito.mock(WebhooksSortFilters.class);
        PageFilters pageFilters = Mockito.mock(PageFilters.class);
        when(webhooksRepository.findAll(any(), any())).thenReturn(List.of(FakeWebhook.WEBHOOK));

        List<WebHooks> result = readWebhooksUseCase.findAll(webhooksSortFilters, pageFilters);

        assertNotNull(result);
        assertEquals(FakeWebhook.WEBHOOK.getId(), result.get(0).getId());
    }
}
