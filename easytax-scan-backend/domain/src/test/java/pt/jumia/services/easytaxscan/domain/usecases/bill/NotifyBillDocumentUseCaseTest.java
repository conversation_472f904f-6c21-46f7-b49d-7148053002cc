package pt.jumia.services.easytaxscan.domain.usecases.bill;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.BillRequester;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.events.Events;
import pt.jumia.services.easytaxscan.domain.entities.kafka.ScanDocumentPayload;
import pt.jumia.services.easytaxscan.domain.entities.settings.DocumentProducerSettings;
import pt.jumia.services.easytaxscan.domain.settings.OverallSettings;
import pt.jumia.services.easytaxscan.domain.usecases.document.UpdateDocumentUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.kafka.NotifyBillDocumentUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.scan.ReadScanUseCase;
import pt.jumia.services.easytaxscan.domain.utils.KafkaProducer;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class NotifyBillDocumentUseCaseTest {

    @Mock
    private KafkaProducer<ScanDocumentPayload> kafkaProducer;

    @Mock
    private ReadScanUseCase readScanUseCase;

    @Mock
    private OverallSettings overallSettings;

    @Mock
    private DocumentProducerSettings documentProducerSettings;

    @Mock
    private BillRequester billRequester;

    @Mock
    private UpdateDocumentUseCase updateDocumentUseCase;

    @InjectMocks
    private NotifyBillDocumentUseCase notifyBillDocumentUseCase;

    private Document document;
    private Scan scan;
    private final String scanCode = "SCAN123";

    @BeforeEach
    void setUp() {
        document = new Document();
        document.setId(1L);
        document.setRequestPayload("test payload");

        scan = new Scan();
        scan.setId(1L);
        scan.setCode(scanCode);

        when(overallSettings.getDocumentProducerSettings()).thenReturn(documentProducerSettings);
        // Don't set up readScanUseCase.findByCode here, we'll do it in each test
    }

    @Test
    void shouldSendMessageToKafkaWhenKafkaProducerEnabled() {
        // Given
        when(documentProducerSettings.getKafkaDocumentProducerEnabled()).thenReturn(true);
        when(readScanUseCase.findByCode(scanCode)).thenReturn(scan);

        // When
        notifyBillDocumentUseCase.execute(scanCode, document);

        // Then
        verify(kafkaProducer).sendMessage(argThat(payload ->
                payload.getDocument().equals(document) &&
                        payload.getScanId().equals(scan.getId()) &&
                        payload.getEvent().equals(Events.SCAN_DOCUMENT.name())
        ));
        verify(billRequester, never()).sendDocumentToBill(any(), any());
    }

    @Test
    void shouldSendDirectlyToBillWhenKafkaProducerDisabled_Success() {
        // Given
        when(documentProducerSettings.getKafkaDocumentProducerEnabled()).thenReturn(false);
        // No need to mock readScanUseCase here as it's not used in this flow

        when(billRequester.sendDocumentToBill(document.getRequestPayload(), document)).thenReturn(true);

        // When
        notifyBillDocumentUseCase.execute(scanCode, document);

        // Then
        verify(kafkaProducer, never()).sendMessage(any());
        verify(billRequester).sendDocumentToBill(document.getRequestPayload(), document);
        verify(updateDocumentUseCase).updateStatus(document, Document.Status.SUBMITTED);
    }

    @Test
    void shouldSendDirectlyToBillWhenKafkaProducerDisabled_Failure() {
        // Given
        when(documentProducerSettings.getKafkaDocumentProducerEnabled()).thenReturn(false);
        // No need to mock readScanUseCase here as it's not used in this flow

        when(billRequester.sendDocumentToBill(document.getRequestPayload(), document)).thenReturn(false);

        // When
        notifyBillDocumentUseCase.execute(scanCode, document);

        // Then
        verify(kafkaProducer, never()).sendMessage(any());
        verify(billRequester).sendDocumentToBill(document.getRequestPayload(), document);
        verify(updateDocumentUseCase).updateStatus(document, Document.Status.FAILED);
    }

    @Test
    void shouldHandleExceptionWhenSendingToBill() {
        // Given
        when(documentProducerSettings.getKafkaDocumentProducerEnabled()).thenReturn(false);
        // No need to mock readScanUseCase here as it's not used in this flow

        when(billRequester.sendDocumentToBill(document.getRequestPayload(), document)).thenThrow(new RuntimeException("Test exception"));

        // When
        notifyBillDocumentUseCase.execute(scanCode, document);

        // Then
        verify(kafkaProducer, never()).sendMessage(any());
        verify(updateDocumentUseCase).updateStatus(document, Document.Status.ERROR);
    }
}