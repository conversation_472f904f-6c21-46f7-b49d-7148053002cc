package pt.jumia.services.easytaxscan.domain.usecases.scan;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Job;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.jobs.UpdateJobsUseCase;
import pt.jumia.services.easytaxscan.domain.repository.JobsRepository;
import org.quartz.SchedulerException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class UpdateJobsUseCaseTest {

    @Mock
    private JobsRepository jobsRepository;

    @InjectMocks
    private UpdateJobsUseCase updateJobsUseCase;

    @Test
    public void updateJob_success() throws SchedulerException {
       Scan scan= FakeScan.SCAN_CREATE;
        Job expectedJob = Job.builder()
                .jobName(scan.getCode())
                .cronExpression(scan.getCronExpression())
                .build();
        when(jobsRepository.updateJob(any(Job.class))).thenReturn(expectedJob);
        expectedJob.setJobName("SCAN5000");
        Job result = updateJobsUseCase.updateScanJob(scan);
        assertNotNull(result);
        assertEquals("SCAN5000", result.getJobName());
        assertEquals("0 0 0 15 * ?", result.getCronExpression());
        verify(jobsRepository).updateJob(any(Job.class));
    }

    @Test
    public void updateJob_schedulerException() throws SchedulerException {
        Scan scan= FakeScan.SCAN_CREATE;
        when(jobsRepository.updateJob(any(Job.class))).thenThrow(SchedulerException.class);
        assertThrows(SchedulerException.class, () -> updateJobsUseCase.updateScanJob(scan));
        verify(jobsRepository).updateJob(any(Job.class));
    }

}
