package pt.jumia.services.easytaxscan.domain.usecases.scan;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeQuery;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeScan;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;
import pt.jumia.services.easytaxscan.domain.repository.SubQueryRepository;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ScanCountryValidationUseCaseTest {

    @Mock
    private SubQueryRepository subQueryRepository;

    @InjectMocks
    private ScanCountryValidationUseCase scanCountryValidationUseCase;

    @Test
    void validateScanCountry_WithValidData_ShouldNotThrowException() {
        // Given
        Scan scan = FakeScan.SCAN_CREATE;
        Query query = FakeQuery.QUERY_CREATE;
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;

        // Ensure query has correct datasource
        query.setDataSource(dataSource);
        scan.setQuery(query);
        dataSource.setCountrySegregated(false);
        scan.setCountry(FakeCountry.COUNTRY_EG);

        when(subQueryRepository.findAll(eq(query.getId()), any(SubQueryFilters.class), any(SubQuerySortFilters.class), any(PageFilters.class)))
                .thenReturn(new ArrayList<>());

        // When & Then
        assertDoesNotThrow(() -> scanCountryValidationUseCase.validateScanCountry(scan));

        verify(subQueryRepository).findAll(eq(query.getId()), any(SubQueryFilters.class), any(SubQuerySortFilters.class), any(PageFilters.class));
    }

    @Test
    void validateScanCountry_WithNullScan_ShouldThrowException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> scanCountryValidationUseCase.validateScanCountry(null));

        assertEquals("Scan or its query cannot be null", exception.getMessage());
        verifyNoInteractions(subQueryRepository);
    }

    @Test
    void validateScanCountry_WithNullQuery_ShouldThrowException() {
        // Given
        Scan scan = FakeScan.SCAN_CREATE;
        scan.setQuery(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> scanCountryValidationUseCase.validateScanCountry(scan));

        assertEquals("Scan or its query cannot be null", exception.getMessage());
        verifyNoInteractions(subQueryRepository);
    }

    @Test
    void validateScanCountry_WithNullQueryId_ShouldThrowException() {
        // Given
        Scan scan = FakeScan.SCAN_CREATE;
        Query query = FakeQuery.QUERY_CREATE;
        query.setId(null);
        scan.setQuery(query);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> scanCountryValidationUseCase.validateScanCountry(scan));

        assertEquals("Scan or its query cannot be null", exception.getMessage());
        verifyNoInteractions(subQueryRepository);
    }

    @Test
    void validateScanCountry_WithSegregatedDataSourceAndNullCountry_ShouldThrowException() {
        // Given
        Scan scan = FakeScan.SCAN_CREATE;
        Query query = FakeQuery.QUERY_CREATE;
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;

        dataSource.setCountrySegregated(true);
        query.setDataSource(dataSource);
        scan.setQuery(query);
        scan.setCountry(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> scanCountryValidationUseCase.validateScanCountry(scan));

        assertEquals("Country cannot be null for segregated data source", exception.getMessage());
        verifyNoInteractions(subQueryRepository);
    }

    @Test
    void validateScanCountry_WithSegregatedDataSourceAndNullCountryId_ShouldThrowException() {
        // Given
        Scan scan = FakeScan.SCAN_CREATE;
        Query query = FakeQuery.QUERY_CREATE;
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
        Country country = FakeCountry.COUNTRY_EG;

        dataSource.setCountrySegregated(true);
        query.setDataSource(dataSource);
        scan.setQuery(query);
        country.setId(null);
        scan.setCountry(country);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> scanCountryValidationUseCase.validateScanCountry(scan));

        assertEquals("Country cannot be null for segregated data source", exception.getMessage());
        verifyNoInteractions(subQueryRepository);
    }

    @Test
    void validateScanCountry_WithSubQueries_ShouldValidateAllSubQueries() {
        // Given
        Scan scan = FakeScan.SCAN_CREATE;
        Query query = FakeQuery.QUERY_CREATE;
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;

        dataSource.setCountrySegregated(false);
        query.setDataSource(dataSource);
        scan.setQuery(query);
        scan.setCountry(FakeCountry.COUNTRY_EG);

        List<SubQuery> subQueries = new ArrayList<>();

        DataSource subDataSource = FakeDataSource.DATA_SOURCE_CREATE;
        subDataSource.setCountrySegregated(false);

        Query subQueryObj = FakeQuery.QUERY_CREATE;
        Long mainQueryId = query.getId();
        Long subQueryId = mainQueryId + 1; // Ensure different ID to avoid circular reference
        subQueryObj.setId(subQueryId);
        subQueryObj.setDataSource(subDataSource);

        SubQuery subQuery = new SubQuery();
        subQuery.setSubQuery(subQueryObj);

        subQueries.add(subQuery);


        when(subQueryRepository.findAll(eq(subQueryId), any(SubQueryFilters.class), any(SubQuerySortFilters.class), any(PageFilters.class)))
                .thenReturn(new ArrayList<>());

        // When & Then
        assertDoesNotThrow(() -> scanCountryValidationUseCase.validateScanCountry(scan));

        verify(subQueryRepository).findAll(eq(subQueryId), any(SubQueryFilters.class), any(SubQuerySortFilters.class), any(PageFilters.class));
    }

    @Test
    void validateScanCountry_WithSegregatedSubQuery_ShouldValidateCountry() {
        // Given
        Scan scan = FakeScan.SCAN_CREATE;
        Query query = FakeQuery.QUERY_CREATE;
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;

        dataSource.setCountrySegregated(false);
        query.setDataSource(dataSource);
        scan.setQuery(query);
        scan.setCountry(FakeCountry.COUNTRY_EG.toBuilder().id(1L).build());

        List<SubQuery> subQueries = new ArrayList<>();

        DataSource subDataSource = FakeDataSource.DATA_SOURCE_CREATE;
        subDataSource.setCountrySegregated(true);

        Query subQueryObj = FakeQuery.QUERY_CREATE;
        Long mainQueryId = query.getId();
        Long subQueryId = mainQueryId + 1; // Ensure different ID
        subQueryObj.setId(subQueryId);
        subQueryObj.setDataSource(subDataSource);

        SubQuery subQuery = new SubQuery();
        subQuery.setSubQuery(subQueryObj);

        subQueries.add(subQuery);

        when(subQueryRepository.findAll(eq(subQueryId), any(SubQueryFilters.class), any(SubQuerySortFilters.class), any(PageFilters.class)))
                .thenReturn(new ArrayList<>());

        // When & Then
        assertDoesNotThrow(() -> scanCountryValidationUseCase.validateScanCountry(scan));

        verify(subQueryRepository).findAll(eq(subQueryId), any(SubQueryFilters.class), any(SubQuerySortFilters.class), any(PageFilters.class));
    }

    @Test
    void validateScanCountry_WithCircularReference_ShouldHandleGracefully() {
        // Given
        Scan scan = FakeScan.SCAN_CREATE;
        Query query = FakeQuery.QUERY_CREATE;
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;

        dataSource.setCountrySegregated(false);
        query.setDataSource(dataSource);
        scan.setQuery(query);
        scan.setCountry(FakeCountry.COUNTRY_EG);

        List<SubQuery> subQueries = new ArrayList<>();

        DataSource subDataSource = FakeDataSource.DATA_SOURCE_CREATE;
        subDataSource.setCountrySegregated(false);

        Query subQueryObj = FakeQuery.QUERY_CREATE;
        Long queryId = query.getId();
        subQueryObj.setId(queryId); // Same ID as parent query, creating a circular reference
        subQueryObj.setDataSource(subDataSource);

        SubQuery subQuery = new SubQuery();
        subQuery.setSubQuery(subQueryObj);

        subQueries.add(subQuery);

        when(subQueryRepository.findAll(eq(queryId), any(SubQueryFilters.class), any(SubQuerySortFilters.class), any(PageFilters.class)))
                .thenReturn(subQueries);

        // When & Then
        assertDoesNotThrow(() -> scanCountryValidationUseCase.validateScanCountry(scan));

        // Should only call repository once despite circular reference
        verify(subQueryRepository, times(1)).findAll(eq(queryId), any(SubQueryFilters.class), any(SubQuerySortFilters.class), any(PageFilters.class));
    }
}
