package pt.jumia.services.easytaxscan.domain.usecases.datasource;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceRepository;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;


/**
 * Unit test for {@link DeleteDataSourceUseCase}
 */
@ExtendWith(MockitoExtension.class)
public class DeleteDataSourceUseCaseTest {

    @Mock
    private DataSourceRepository dataSourceRepository;

    @Mock
    private ReadDataSourceUseCase readDataSourceUseCase;

    @InjectMocks
    private DeleteDataSourceUseCase deleteDataSourceUseCase;

    @Test
    public void execute_success() throws NotFoundException {
        Long id = 1L;
        DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;

        when(readDataSourceUseCase.execute(id)).thenReturn(dataSource);
        deleteDataSourceUseCase.execute(id);

        verify(readDataSourceUseCase).execute(id);
        verify(dataSourceRepository).deleteById(id);
    }

    @Test
    public void execute_notFoundException() {
        Long id = 1L;

        when(readDataSourceUseCase.execute(id)).thenThrow(NotFoundException.class);

        assertThrows(NotFoundException.class, () -> deleteDataSourceUseCase.execute(id));

        verify(readDataSourceUseCase).execute(id);
        verify(dataSourceRepository, never()).deleteById(id);
    }
}

