package pt.jumia.services.easytaxscan.domain.usecases.datasource;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeDataSource;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.CountryRepository;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ValidateDataSourceQuery;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
public class ValidateDataSourceUseCaseTest {
    @Mock
    private ReadDataSourceUseCase readDataSourceUseCase;

    @Mock
    private ValidateDataSourceQuery validateDataSourceQueryUseCase;

    @Mock
    private CountryRepository countryRepository;

    @InjectMocks
    private ValidateDataSourceUseCase validateDataSourceUseCase;

    private final DataSource dataSource = FakeDataSource.DATA_SOURCE_CREATE;
    private Country country;
    private HealthCheckParams healthCheckParams;
    private final String dataSourceCode = "NAV";
    private final String countryCode = "EG";

    @BeforeEach
    void setUp() {

        country = Country.builder()
                .countryCode(countryCode)
                .countryName("Egypt")
                .build();

        healthCheckParams = HealthCheckParams.builder()
                .countryCode(countryCode)
                .build();
    }

    @Test
    void execute_success() throws NotFoundException {
        // Mock successful responses
        when(readDataSourceUseCase.findByCode(dataSourceCode)).thenReturn(dataSource);
        when(countryRepository.findByCode(countryCode)).thenReturn(Optional.ofNullable(country));

        HealthStatus expectedStatus = HealthStatus.builder()
                .success(true)
                .build();

        when(validateDataSourceQueryUseCase.validate(any(Query.class), eq(Map.of()), eq(countryCode)))
                .thenReturn(expectedStatus);

        // Execute test
        HealthStatus result = validateDataSourceUseCase.execute(dataSourceCode, healthCheckParams);

        // Verify
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // Verify correct interactions with mocks
        verify(readDataSourceUseCase).findByCode(dataSourceCode);
        verify(countryRepository).findByCode(countryCode);

        // Verify the query was created correctly
        verify(validateDataSourceQueryUseCase).validate(
                argThat(query ->
                        query.getCode().equals(dataSourceCode) &&
                                query.getSql().equals(AppConstants.SELECT_1_AS_COUNT) &&
                                query.getDataSource() == dataSource),
                eq(Map.of()),
                eq(countryCode)
        );
    }

    @Test
    void execute_dataSourceNotFound() {
        // Mock NotFoundException when data source not found
        when(readDataSourceUseCase.findByCode(dataSourceCode))
                .thenThrow(NotFoundException.build("Data source not found"));

        // Execute test and verify exception is thrown
        assertThrows(NotFoundException.class, () ->
                validateDataSourceUseCase.execute(dataSourceCode, healthCheckParams));

        // Verify interactions
        verify(readDataSourceUseCase).findByCode(dataSourceCode);
        verify(countryRepository, never()).findByCode(any());
        verify(validateDataSourceQueryUseCase, never()).validate(any(), any(), any());
    }

    @Test
    void execute_countryNotFound() {
        // Mock successful data source lookup but country not found
        when(readDataSourceUseCase.findByCode(dataSourceCode)).thenReturn(dataSource);
        when(countryRepository.findByCode(countryCode))
                .thenThrow(NotFoundException.build("Country not found"));

        // Execute test and verify exception is thrown
        assertThrows(NotFoundException.class, () ->
                validateDataSourceUseCase.execute(dataSourceCode, healthCheckParams));

        // Verify interactions
        verify(readDataSourceUseCase).findByCode(dataSourceCode);
        verify(countryRepository).findByCode(countryCode);
        verify(validateDataSourceQueryUseCase, never()).validate(any(), any(), any());
    }

    @Test
    void execute_validationFails() throws NotFoundException {
        // Mock successful lookups but validation fails
        when(readDataSourceUseCase.findByCode(dataSourceCode)).thenReturn(dataSource);
        when(countryRepository.findByCode(countryCode)).thenReturn(Optional.ofNullable(country));

        HealthStatus expectedStatus = HealthStatus.builder()
                .success(false)
                .error("Connection failed")
                .build();

        when(validateDataSourceQueryUseCase.validate(any(Query.class), eq(Map.of()), eq(countryCode)))
                .thenReturn(expectedStatus);

        // Execute test
        HealthStatus result = validateDataSourceUseCase.execute(dataSourceCode, healthCheckParams);

        // Verify
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("Connection failed", result.getError());

        // Verify interactions
        verify(readDataSourceUseCase).findByCode(dataSourceCode);
        verify(countryRepository).findByCode(countryCode);
        verify(validateDataSourceQueryUseCase).validate(any(Query.class), eq(Map.of()), eq(countryCode));
    }
}