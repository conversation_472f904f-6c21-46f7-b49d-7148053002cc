package pt.jumia.services.easytaxscan.domain.usecases.country;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeCountry;
import pt.jumia.services.easytaxscan.domain.repository.CountryRepository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UpdateCountryUseCaseTest {

    @Mock
    private CountryRepository countryRepository;

    @InjectMocks
    private UpdateCountryUseCase updateCountryUseCase;

    @Test
    void execute_shouldUpdateCountrySuccessfully() {
        Long countryId = 1L;
        Country updateRequest = FakeCountry.COUNTRY_CREATE.toBuilder()
                .countryName("Updated Country")
                .build();

        when(countryRepository.update(countryId, updateRequest)).thenReturn(updateRequest);
        Country updatedCountry = updateCountryUseCase.execute(countryId, updateRequest);
        assertNotNull(updatedCountry);
        assertEquals("Updated Country", updatedCountry.getCountryName());
        verify(countryRepository).update(countryId, updateRequest);
    }

    @Test
    void execute_shouldThrowIllegalArgumentExceptionWhenCountryIsNull() {
        Long countryId = 1L;
        assertThrows(IllegalArgumentException.class, () -> updateCountryUseCase.execute(countryId, null));

        verifyNoInteractions(countryRepository);
    }

    @Test
    void execute_shouldThrowExceptionWhenCountryDoesNotExist() {
        Long countryId = 999L;
        Country updateRequest = FakeCountry.COUNTRY_CREATE.toBuilder()
                .countryName("Non-existent Country")
                .build();
        RuntimeException notFoundException = new RuntimeException("Country not found");
        when(countryRepository.update(countryId, updateRequest)).thenThrow(notFoundException);
        RuntimeException thrownException = assertThrows(RuntimeException.class, () ->
                updateCountryUseCase.execute(countryId, updateRequest)
        );

        assertEquals("Country not found", thrownException.getMessage());
        verify(countryRepository).update(countryId, updateRequest);
    }
}
