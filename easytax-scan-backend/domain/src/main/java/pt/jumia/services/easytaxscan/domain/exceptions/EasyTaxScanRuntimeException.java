package pt.jumia.services.easytaxscan.domain.exceptions;

import lombok.Getter;

import java.io.Serial;

@Getter
public abstract class EasyTaxScanRuntimeException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 5325945872461410500L;
    private final ErrorCode errorCode;

    public EasyTaxScanRuntimeException(ErrorCode errorCode) {
        this.errorCode = errorCode;
    }

    public EasyTaxScanRuntimeException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public EasyTaxScanRuntimeException(ErrorCode errorCode, Exception exception) {
        super(exception);
        this.errorCode = errorCode;
    }
}
