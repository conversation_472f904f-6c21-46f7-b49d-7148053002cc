package pt.jumia.services.easytaxscan.domain.caches;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Policy;
import freemarker.core.TemplateClassResolver;
import freemarker.template.Configuration;
import freemarker.template.Template;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.caches.config.CacheConfiguration;

import java.io.IOException;
import java.time.Duration;
import java.util.Optional;

@Component
@Slf4j
public class TemplatesCache implements ReloadableCache {

    private static final String CACHE_METRIC_NAME = "cm.templates.cache";
    private static final Configuration CONFIGURATION = new Configuration(Configuration.VERSION_2_3_32);

    private final Cache<CacheKey, Template> templatesCache;

    private final CacheConfiguration cacheConfiguration;

    @Autowired
    public TemplatesCache(MeterRegistry meterRegistry, CacheConfiguration cacheConfiguration) {
        this.cacheConfiguration = cacheConfiguration;
        templatesCache = CaffeineCacheMetrics.monitor(meterRegistry, Caffeine.newBuilder()
                .recordStats()
                .maximumSize(cacheConfiguration.getTemplate().getMaxSize())
                .expireAfterAccess(cacheConfiguration.getTemplate().getCacheTtl())
                .expireAfterWrite(cacheConfiguration.getTemplate().getCacheTtl())
                .build(), CACHE_METRIC_NAME);
        CONFIGURATION.setNewBuiltinClassResolver(TemplateClassResolver.ALLOWS_NOTHING_RESOLVER);
    }

    public Template getTemplate(String name, String stringTemplate) throws IOException {
        CacheKey cacheKey = CacheKey.of(name, stringTemplate);

        Optional<Template> optionalTemplate = Optional.ofNullable(templatesCache.getIfPresent(cacheKey));
        if (optionalTemplate.isPresent()) {
            return optionalTemplate.get();
        }

        Template template = new Template(name, stringTemplate, CONFIGURATION);
        templatesCache.put(cacheKey, template);
        return template;
    }

    @Override
    public void reload() {
        Integer cacheMaxSize = cacheConfiguration.getTemplate().getMaxSize();
        Duration cacheTtl = cacheConfiguration.getTemplate().getCacheTtl();

        if (settingsChanged(cacheMaxSize, cacheTtl)) {
            log.info("Applying settings to templates cache: Max Size: {} TTL: {}", cacheMaxSize, cacheTtl);
            applyCacheSettings(cacheMaxSize, cacheTtl);
        } else {
            log.debug("Not reloading triggers cache because no settings changed");
        }
    }

    private void applyCacheSettings(Integer cacheMaxSize, Duration cacheTtl) {
        this.templatesCache.policy().eviction().ifPresent(expiration -> expiration.setMaximum(cacheMaxSize));
        this.templatesCache.policy().expireAfterAccess().ifPresent(expiration -> expiration.setExpiresAfter(cacheTtl));
        this.templatesCache.policy().expireAfterWrite().ifPresent(expiration -> expiration.setExpiresAfter(cacheTtl));
    }

    private boolean settingsChanged(Integer cacheMaxSize, Duration cacheTtl) {
        Policy<CacheKey, Template> cachePolicy = this.templatesCache.policy();

        return cachePolicy.expireAfterAccess().filter(e -> !e.getExpiresAfter().equals(cacheTtl)).isPresent()
                || cachePolicy.expireAfterWrite().filter(e -> !e.getExpiresAfter().equals(cacheTtl)).isPresent()
                || cachePolicy.eviction().filter(e -> e.getMaximum() != cacheMaxSize).isPresent();
    }

    @EqualsAndHashCode
    public static class CacheKey {
        private final String name;
        private final String stringTemplate;

        private CacheKey(String name, String stringTemplate) {
            this.name = name;
            this.stringTemplate = stringTemplate;
        }

        public static CacheKey of(String name, String stringTemplate) {
            return new CacheKey(name, stringTemplate);
        }
    }

}
