package pt.jumia.services.easytaxscan.domain.usecases.setting;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.repository.SettingRepository;
import pt.jumia.services.easytaxscan.domain.settings.OverallSettings;


@RequiredArgsConstructor
@Component
public class ReloadSettingUseCase {

    private final SettingRepository settingRepository;
    private final OverallSettings overallSettings;

    public void execute() {

        List<Setting> settings = settingRepository.findAll();
        this.overallSettings.refreshAllSettings(settings);
    }

    public void execute(List<Setting> settings) {

        overallSettings.refreshAllSettings(settings);
    }

}
