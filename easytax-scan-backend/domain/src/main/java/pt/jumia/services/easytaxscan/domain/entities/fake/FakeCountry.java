package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


public interface FakeCountry {

    Country COUNTRY_CREATE = createCountryRequest(null, "MOCK", "Mockland");
    Country COUNTRY_UPDATE = createCountryRequest(1L, "UPD", "Updated Country");
    Country COUNTRY_TEST1 = createCountryRequest(2L, "TEST1", "Test Country 1");
    Country COUNTRY_TEST2 = createCountryRequest(3L, "TEST2", "Test Country 2");
    Country COUNTRY_TEST3 = createCountryRequest(4L, "TEST3", "Test Country 3");
    Country COUNTRY_EG = createCountryRequest(null, "EG", "Egypt");
    Country COUNTRY_IND = createCountryRequest(null, "IND", "India");

    static Country createCountryRequest(Long id, String countryCode, String countryName) {
        return Country.builder()
                .id(id)
                .countryCode(countryCode)
                .countryName(countryName)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .createdBy(AppConstants.SYSTEM)
                .updatedBy(AppConstants.SYSTEM)
                .build();
    }

    List<Country> ALL =
            Collections.unmodifiableList(new ArrayList<>() {{
                add(COUNTRY_TEST1);
                add(COUNTRY_TEST2);
                add(COUNTRY_TEST3);
            }});
}
