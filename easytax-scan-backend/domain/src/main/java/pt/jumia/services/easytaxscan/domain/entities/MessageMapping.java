package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.fake.FakeOriginSystems;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class MessageMapping {

    public enum SortingField {
        ID,
        NAME,
        STATUS,
        CREATED_BY,
        CREATED_AT,
        UPDATED_BY,
        UPDATED_AT
    }

    @Nullable
    private Long id;

    private String name;

    private String description;

    private Status status;

    private MessageType messageType;

    private OriginSystem originSystem;

    private Universe universe;

    private String messageBody;

    private List<ShopMessageMapping> shopMessageMappings;

    private MessageEvent messageEvent;

    private SourceStream sourceStream;

    private String rule;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;

    public enum Status {
        ACTIVE,
        INACTIVE
    }

    public MessageMapping withoutDbFields() {
        return toBuilder()
                .id(null)
                .messageType(messageType.withoutDbFields())
                .universe(universe.withoutDbFields())
                .messageEvent(messageEvent.withoutDbFields())
                .sourceStream(sourceStream.withoutDbFields())
                .originSystem(FakeOriginSystems.ORIGIN_SYSTEM_1)
                .shopMessageMappings(null)
                .createdAt(null)
                .updatedAt(null)
                .build();
    }

    public static void validate(MessageMapping messageMapping) {
        if (Objects.isNull(messageMapping)) {
            throw new IllegalArgumentException("Message mapping cannot be null");
        }
        if (Objects.isNull(messageMapping.getName()) || messageMapping.getName().isEmpty()) {
            throw new IllegalArgumentException("Mapping name cannot be null");
        }
        if (Objects.isNull(messageMapping.getOriginSystem())) {
            throw new IllegalArgumentException("Mapping origin system cannot be null");
        }
        if (Objects.isNull(messageMapping.getMessageBody()) || messageMapping.getMessageBody().isEmpty()) {
            throw new IllegalArgumentException("Message body cannot be null or empty");
        }
        if (Objects.isNull(messageMapping.getMessageType())) {
            throw new IllegalArgumentException("Message type cannot be null ");
        }
        if (Objects.isNull(messageMapping.getMessageEvent())) {
            throw new IllegalArgumentException("Message Event cannot be null ");
        }
        if (Objects.isNull(messageMapping.getSourceStream())) {
            throw new IllegalArgumentException("Message payload cannot be null ");
        }
        if (Objects.isNull(messageMapping.getUniverse())) {
            throw new IllegalArgumentException("Message universe  cannot be null ");
        }
        if (Objects.isNull(messageMapping.getShopMessageMappings())) {
            throw new IllegalArgumentException("Shop message mapping cannot be null ");
        }
        if (messageMapping.getShopMessageMappings().isEmpty()) {
            throw new IllegalArgumentException("Shop message mapping cannot be empty ");
        }
    }


}
