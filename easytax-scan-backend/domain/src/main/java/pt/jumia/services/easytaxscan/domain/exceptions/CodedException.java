package pt.jumia.services.easytaxscan.domain.exceptions;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serial;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class CodedException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = -7611708343328659778L;

    private final ErrorCode errorCode;

    public CodedException(ErrorCode errorCode, String message) {

        super(message);
        this.errorCode = errorCode;
    }

    public CodedException(ErrorCode errorCode, String message, Exception exception) {

        super(message, exception);
        this.errorCode = errorCode;
    }

    public CodedException(ErrorCode errorCode, Exception exception) {

        super(exception);
        this.errorCode = errorCode;
    }

}
