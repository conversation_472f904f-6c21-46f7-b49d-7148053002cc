package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.time.LocalDateTime;

/**
 * Some fake data for {@link Query}, to be used in tests and fill in the MEM DB
 */
public interface FakeWebhook {


    WebHooks WEBHOOK = WebHooks.builder()
            .id(2L).payload("{payload}").document(Document.builder().build())
            .createdBy(AppConstants.SYSTEM)
            .createdAt(LocalDateTime.now())
            .updatedBy(AppConstants.SYSTEM)
            .updatedAt(LocalDateTime.now())
            .build();
}
