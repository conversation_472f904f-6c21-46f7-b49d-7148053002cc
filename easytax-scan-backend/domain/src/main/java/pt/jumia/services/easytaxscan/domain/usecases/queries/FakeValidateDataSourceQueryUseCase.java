package pt.jumia.services.easytaxscan.domain.usecases.queries;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.Profiles;
import pt.jumia.services.easytaxscan.domain.entities.HealthStatus;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.QueryPreview;
import pt.jumia.services.easytaxscan.domain.exceptions.BadRequestException;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
@Profile(Profiles.FAKE_CLIENTS)
public class FakeValidateDataSourceQueryUseCase implements ValidateDataSourceQuery {
    @Override
    public HealthStatus validate(Query query, Map<String, Object> args, String countryCode) throws BadRequestException {
        return HealthStatus.builder().success(true).build();

    }

    @Override
    public List<Map<String, Object>> constructPreviewResponse(QueryPreview queryPreview) throws Exception {
        return List.of();
    }
}
