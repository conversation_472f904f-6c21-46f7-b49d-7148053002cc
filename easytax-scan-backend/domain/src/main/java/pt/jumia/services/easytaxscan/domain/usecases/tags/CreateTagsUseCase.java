package pt.jumia.services.easytaxscan.domain.usecases.tags;

import java.util.Objects;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.monitoring.TagMonitoring;
import pt.jumia.services.easytaxscan.domain.repository.TagRepository;
import pt.jumia.services.easytaxscan.domain.entities.Tag;

/**
 * Use case that will create a new tag in the persistence layer
 *
 */
@Component
@SuppressFBWarnings("EI_EXPOSE_REP2")
public class CreateTagsUseCase {

    private TagRepository tagRepository;

    private TagMonitoring tagMonitoring;

    @Autowired
    public CreateTagsUseCase(TagRepository tagRepository, TagMonitoring tagMonitoring) {
        this.tagRepository = tagRepository;
        this.tagMonitoring = tagMonitoring;
    }

    public Tag execute(Tag tag) {

        if (Objects.isNull(tag)) {
            // If programing error passed a null tag, throw IllegalArgumentException
            throw new IllegalArgumentException("Tag cannot be null");
        }

        this.tagMonitoring.recordTagCreation("ng");

        return tagRepository.insert(tag);
    }
}
