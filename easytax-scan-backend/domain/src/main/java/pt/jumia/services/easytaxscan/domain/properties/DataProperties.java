package pt.jumia.services.easytaxscan.domain.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Data
@Component
@ConfigurationProperties(prefix = "data")
public class DataProperties {

    private Db db;
    private Events events;

    @Data
    public static class Db {
        private String driver = "org.postgresql.Driver";
        private String url = "******************************************************";
        private String username = "postgres";
        private String password = "postgres";

        private String applicationSchema = "public";
        private String auditSchema = "audit";
        private String quartzSchema = "public";

        private int maxPoolSize = 15;

        private Flyway flyway = new Flyway();

        @Data
        public static class Flyway {
            private boolean repair = false;
        }
    }

    @Data
    public static class Events {
        private Duration checkConnectionTimeout = Duration.ofSeconds(15);
    }
}
