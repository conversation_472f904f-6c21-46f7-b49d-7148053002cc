package pt.jumia.services.easytaxscan.domain.exceptions;

import java.io.Serial;
import java.text.MessageFormat;

public class EntityErrorsException extends Exception {

    @Serial
    private static final long serialVersionUID = 48769531486415846L;

    public static EntityErrorsException createNullClassError(Class clazz) {
        return new EntityErrorsException(MessageFormat.format("{0} cannot be null", clazz.getSimpleName()));
    }

    public static EntityErrorsException createEmptyEntityError(String entity, String type) {
        return new EntityErrorsException(MessageFormat.format("{0} cannot be {1}", entity, type));
    }

    public static EntityErrorsException createSortError(String field, Class clazz) {
        return new EntityErrorsException(MessageFormat.format("Sort Field {0} for {1} is invalid",
                field, clazz.getSimpleName()));
    }


    public EntityErrorsException(String message) {
        super(message);
    }

}
