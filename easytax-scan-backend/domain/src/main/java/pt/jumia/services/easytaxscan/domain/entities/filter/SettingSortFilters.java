package pt.jumia.services.easytaxscan.domain.entities.filter;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.Setting;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class SettingSortFilters implements Filter {

    @Builder.Default
    private Setting.SortingFields field = Setting.SortingFields.ID;
    @Builder.Default
    private OrderDirection direction = OrderDirection.DESC;

    @Override
    public Map<String, ?> getAsMap() {

        return Map.ofEntries(
            Map.entry("orderField", field.name()),
            Map.entry("orderDirection", direction.name())
        );
    }

}
