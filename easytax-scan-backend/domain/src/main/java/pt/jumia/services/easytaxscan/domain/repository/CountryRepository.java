package pt.jumia.services.easytaxscan.domain.repository;

import pt.jumia.services.easytaxscan.domain.entities.Country;

import java.util.Optional;


public interface CountryRepository {

    Country insert(Country country);

    Optional<Country> findById(Long id);

    Optional<Country> findByCode(String countryCode);


    Country update(long id, Country updateCountry);

    void deleteById(long id);

}
