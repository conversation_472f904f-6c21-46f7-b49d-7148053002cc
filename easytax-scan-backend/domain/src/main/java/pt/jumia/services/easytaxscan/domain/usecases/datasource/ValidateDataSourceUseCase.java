package pt.jumia.services.easytaxscan.domain.usecases.datasource;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.HealthCheckParams;
import pt.jumia.services.easytaxscan.domain.entities.HealthStatus;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.CountryRepository;
import pt.jumia.services.easytaxscan.domain.usecases.country.ReadCountryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ValidateDataSourceQuery;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.util.Map;


@Component
@RequiredArgsConstructor
public class ValidateDataSourceUseCase {

    private final ReadDataSourceUseCase readDataSourceUseCase;
    private final ValidateDataSourceQuery validateDataSourceQueryUseCase;
    private final CountryRepository countryRepository;

    public HealthStatus execute(String code, HealthCheckParams healthCheckParams) throws NotFoundException {
        DataSource dataSource = readDataSourceUseCase.findByCode(code);
        countryRepository.findByCode(healthCheckParams.getCountryCode());
        Query query = Query.builder()
                .code(code)
                .sql(AppConstants.SELECT_1_AS_COUNT)
                .dataSource(dataSource).build();
        return validateDataSourceQueryUseCase.validate(query, Map.of(),
                healthCheckParams.getCountryCode());
    }

}
