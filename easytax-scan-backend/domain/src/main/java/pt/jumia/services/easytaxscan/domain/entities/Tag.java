package pt.jumia.services.easytaxscan.domain.entities;

import java.time.LocalDateTime;

import lombok.Builder;
import lombok.Value;
import org.jetbrains.annotations.Nullable;

/**
 * Business representation of Tag
 */
@Value
@Builder(toBuilder = true)
public class Tag {

    @Nullable
    private Long id;

    private String name;

    private String description;

    @Nullable
    private String color;

    @Nullable
    private LocalDateTime createdAt;

    public Tag withoutDbFields() {
        return toBuilder()
            .id(null)
            .createdAt(null)
            .build();
    }
}
