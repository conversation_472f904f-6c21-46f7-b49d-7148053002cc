package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.Mode;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Some fake data for {@link pt.jumia.services.easytaxscan.domain.entities.ExecutionLog}, to be used in tests and fill in the MEM DB
 */
public interface FakeExecutionLog {

    // Fake Execution Log Data
    ExecutionLog EXECUTION_LOG_CREATE = createExecutionLogRequest(1L, 100, "ACTIVE", "DRYRUN", "None", 500L);
    ExecutionLog EXECUTION_LOG_FAIL = createExecutionLogRequest(2L, 0, "ACTIVE", "DRYRUN", "Sample Exception", 2000L);
    ExecutionLog EXECUTION_LOG_UPDATE = createExecutionLogRequest(3L, 150, "ACTIVE", "DRYRUN", "No Exception", 1200L);
    // List of all Execution Logs
    List<ExecutionLog> ALL = Collections.unmodifiableList(new ArrayList<>() {{
        add(EXECUTION_LOG_CREATE);
        add(EXECUTION_LOG_FAIL);
        add(EXECUTION_LOG_UPDATE);
    }});

    // Method to create a sample Execution Log
    public static ExecutionLog createExecutionLogRequest(Long id, Integer totalResults,
                                                         String status, String mode, String exception, Long durationMs) {
        return ExecutionLog.builder()
                .id(id)
                .scan(FakeScan.SCAN_CREATE)
                .totalResults(totalResults)
                .status(ExecutionLog.Status.valueOf(status))
                .mode(Mode.valueOf(mode))
                .exception(exception)
                .durationMs(durationMs)
                .createdBy(AppConstants.SYSTEM)
                .createdAt(LocalDateTime.now())
                .updatedBy(AppConstants.SYSTEM)
                .updatedAt(LocalDateTime.now())
                .build();
    }
}
