package pt.jumia.services.easytaxscan.domain.exceptions;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ErrorCode {

    DATA_INTEGRITY_VIOLATION(1000),

    INVALID_SETTING(1001),

    FORBIDDEN(3000),

    CONFLICT(3001),

    BAD_REQUEST(4000),

    INVALID_PAYLOAD(4012),

    ENTITY_NOT_FOUND(4004),

    INTERNAL_SERVER_ERROR(5000),

    DATA_ROLLBACK(5005),

    JUMIA_PAY_REQUEST_ERROR(6000),

    FEED_REQUEST_ERROR(6007),

    ERROR_CREATING_TEMPLATE(4100),
    ERROR_PARSING_TEMPLATE(4101),
    INVALID_TEMPLATE_DOCUMENT_TTL(4102),
    DOCUMENT_TEMPLATE_OVERRIDES_CODE_DUPLICATE(4103),
    SERVICE_UNAVAILABLE(7000);


    private final int code;

}
