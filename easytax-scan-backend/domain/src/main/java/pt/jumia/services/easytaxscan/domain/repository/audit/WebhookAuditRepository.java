package pt.jumia.services.easytaxscan.domain.repository.audit;

import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.WebhookAuditFilters;

import java.util.List;

public interface WebhookAuditRepository {
    List<AuditedEntity<WebHooks>> getWebhookAuditLogById(WebhookAuditFilters filters, WebHooks webHooks);

    long getAuditLogCountById(WebhookAuditFilters filters);
}
