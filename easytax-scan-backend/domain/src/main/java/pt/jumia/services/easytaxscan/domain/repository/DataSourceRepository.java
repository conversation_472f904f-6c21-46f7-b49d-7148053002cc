package pt.jumia.services.easytaxscan.domain.repository;

import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceSortFilters;

import java.util.List;
import java.util.Optional;

/**
 * Contract between application and persistence layer for CRUD operation for {@link DataSource}
 */
public interface DataSourceRepository {

    DataSource insert(DataSource dataSource);

    Optional<DataSource> findById(Long id);

    Optional<DataSource> findByCode(String code);

    List<DataSource> findAll(DataSourceFilters filters,
                             DataSourceSortFilters sortFilters,
                             PageFilters pageFilters);

    DataSource update(long id, DataSource updateDataSource);

    void deleteById(long id);

    Long executeCount(DataSourceFilters filters);
}
