package pt.jumia.services.easytaxscan.domain.repository.audit;

import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.QueryAuditFilters;

import java.util.List;

public interface QueryAuditRepository {
    List<AuditedEntity<Query>> getQueryAuditLogById(QueryAuditFilters filters, Query query);

    long getAuditLogCountById(QueryAuditFilters filters);
}
