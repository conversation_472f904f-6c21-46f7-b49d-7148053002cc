package pt.jumia.services.easytaxscan.domain.usecases.queries;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.QueryRepository;
import pt.jumia.services.easytaxscan.domain.repository.SubQueryRepository;
import pt.jumia.services.easytaxscan.domain.usecases.datasource.ReadDataSourceUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.validation.ValidateQueryUseCase;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class UpdateQueryUseCase {

    private final QueryRepository queryRepository;
    private final ValidateQueryUseCase validateQueryUseCase;
    private final SubQueryRepository subQueryRepository;
    private final ReadDataSourceUseCase readDataSourceUseCase;

    public Query execute(Long id, Query updateQuery) throws Exception {
        if (Objects.isNull(updateQuery)) {
            throw new IllegalArgumentException("Query to update cannot be null");
        }

        readDataSourceUseCase.findById(updateQuery.getDataSource().getId());
        validateQueryUseCase.validate(updateQuery);

        Query query = queryRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(Query.class, id));

        Query updatedQuery = queryRepository.update(id, updateQuery);

        List<Long> newSubQueryIds = CollectionUtils.isEmpty(updateQuery.getSubQueries()) ? List.of() : updateQuery.getSubQueries().stream()
                .map(sq -> sq.getSubQuery().getId())
                .toList();

        List<SubQuery> subQueries = subQueryRepository.findByQueryId(query.getId());
        List<Long> currentSubQueryIds = subQueries.stream()
                .map(sq -> sq.getSubQuery().getId())
                .collect(Collectors.toList());
        List<Long> subQueriesToDelete = subQueries.stream()
                .map(sq -> sq.getSubQuery().getId())
                .filter(subQueryId -> !newSubQueryIds.contains(subQueryId))
                .toList();

        if (!CollectionUtils.isEmpty(updateQuery.getSubQueries())) {
            for (SubQuery subQuery : updateQuery.getSubQueries()) {
                if (currentSubQueryIds.contains(subQuery.getSubQuery().getId())) {
                    subQueryRepository.update(query.getId(), subQuery.getSubQuery().getId(), subQuery);
                } else {
                    subQueryRepository.insert(query.getId(), subQuery);
                }
            }
        }

        if (!CollectionUtils.isEmpty(subQueriesToDelete)) {
            for (Long subQueryId : subQueriesToDelete) {
                subQueryRepository.delete(query.getId(), subQueryId);
            }
        }

        return updatedQuery;
    }
}
