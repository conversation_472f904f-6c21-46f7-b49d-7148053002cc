package pt.jumia.services.easytaxscan.domain.jobs;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogSortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanSortFilters;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;
import pt.jumia.services.easytaxscan.domain.templates.freemarker.FreeMarkerParser;
import pt.jumia.services.easytaxscan.domain.usecases.document.CreateDocumentUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.CreateExecutionLogUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.ReadExecutionLogUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.UpdateExecutionLogUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ExecuteQueryUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ValidateDataSourceQuery;
import pt.jumia.services.easytaxscan.domain.usecases.scan.ReadScanUseCase;
import pt.jumia.services.easytaxscan.domain.utils.JsonUtils;
import pt.jumia.services.easytaxscan.domain.utils.TemplateParsingUtils;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class ScanJobExecutionUseCase {

    private final ScanRepository scanRepository;
    private final ReadScanUseCase readScanUseCase;
    private final CreateExecutionLogUseCase createExecutionLogUseCase;
    private final UpdateExecutionLogUseCase updateExecutionLogUseCase;
    private final CreateDocumentUseCase createDocumentUseCase;
    private final ReadExecutionLogUseCase readExecutionLogUseCase;
    private final ExecuteQueryUseCase executeQueryUseCase;
    private final TemplateParsingUtils templateParsingUtils;

    private final ValidateDataSourceQuery validateDataSourceQueryUseCase;
    private final JsonUtils jsonUtils;

    public void execute(String code) {
        log.info("Starting scan execution. Scan code: {}", code);
        Scan scan = readScanUseCase.execute(code);
        execute(scan);
    }

    public void execute(long id) {
        log.info("Starting scan execution. Scan id: {}", id);
        Scan scan = readScanUseCase.execute(id);
        execute(scan);
    }

    private void execute(Scan scan) {
        log.info("Getting previous execution log for scan code: {}", scan.getCode());

        boolean isLimitOffsetPagination = true;
        if (scan.getQuery().getPaginationField() != null) {
            isLimitOffsetPagination = false;
        }

        Long startRecordId = null;
        if (!isLimitOffsetPagination) {
            ExecutionLog previousExecutionLog = getPreviousExecutionLog(scan);
            startRecordId = 0L;
            if (previousExecutionLog != null) {
                log.info("Found previous execution log for scan code: {}. Last record id: {}", scan.getCode(), previousExecutionLog.getLastRecordId());
                startRecordId = previousExecutionLog.getLastRecordId() == null ? 0L : previousExecutionLog.getLastRecordId();
            } else {
                log.info("No previous execution log found for scan code: {}", scan.getCode());
            }

        }
        log.info("Starting scan execution. Scan code: {}", scan.getCode());
        ExecutionLog executionLog = ExecutionLog.createExecutionlog(scan);
        executionLog.setLastRecordId(startRecordId);
        executionLog = createExecutionLogUseCase.execute(executionLog);
        try {
            boolean isEmpty = false;
            int page = 1;
            int totalRecords = 0;
            do {
                log.info("Starting scan execution. Scan code: {}, page: {}", scan.getCode(), page);
                List<Map<String, Object>> result = executeQueryUseCase.execute(scan.getQuery(), startRecordId, List.of());

                if (!CollectionUtils.isEmpty(result)) {
                    isEmpty = false;

                    totalRecords += result.size();

                    for (Map<String, Object> resultItem : result) {
                        String parsedTemplate = templateParsingUtils.parse(scan.getCode(), scan.getMapping(), resultItem);

                        String queryDataJson = jsonUtils.toJsonOrNull(resultItem);
                        String sid = templateParsingUtils.parse(scan.getCode() + "_sid", scan.getSidColumn(), resultItem);

                        Document documentRequest = Document.createDocument(executionLog, sid, queryDataJson);

                        documentRequest.setRequestPayload(parsedTemplate);
                        createDocumentUseCase.execute(documentRequest);


                        if (!isLimitOffsetPagination) {
                            startRecordId = (long) resultItem.get(scan.getQuery().getPaginationField());
                        }
                    }

                    executionLog.setLastRecordId(startRecordId);
                    executionLog.setTotalResults(totalRecords);
                    updateExecutionLogUseCase.execute(executionLog.getId(), executionLog);
                } else {
                    isEmpty = true;
                }

                page++;
            } while (!isEmpty);

        } catch (Exception processEx) {
            log.error("Error processing scan job for scanCode: {}. Error: {}", scan.getCode(), ExceptionUtils.getStackTrace(processEx));
            if (executionLog != null) {
                executionLog.setException(ExceptionUtils.getStackTrace(processEx));
                updateExecutionLogUseCase.execute(executionLog.getId(), executionLog);
            }
        }
    }

    private ExecutionLog getPreviousExecutionLog(Scan scan) {
        ExecutionLogFilters executionLogFilters = ExecutionLogFilters.builder()
                .scanId(scan.getId())
                .build();
        ExecutionLogSortFilters sortFilters = ExecutionLogSortFilters.builder()
                .field(ExecutionLog.SortingFields.ID)
                .direction(OrderDirection.DESC)
                .build();
        PageFilters pageFilters = PageFilters.builder()
                .size(1)
                .build();
        List<ExecutionLog> executionLogs = readExecutionLogUseCase.execute(executionLogFilters, sortFilters, pageFilters);

        return CollectionUtils.isEmpty(executionLogs) ? null : executionLogs.get(0);
    }
}
