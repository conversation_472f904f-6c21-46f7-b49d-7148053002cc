package pt.jumia.services.easytaxscan.domain.jobs;

import lombok.AllArgsConstructor;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.repository.JobsRepository;

@Component
@AllArgsConstructor
public class PauseJobsUseCase {
    private JobsRepository jobsRepository;

    public void pauseAllJobs() throws SchedulerException {
        jobsRepository.pauseAllJobs();
    }
}
