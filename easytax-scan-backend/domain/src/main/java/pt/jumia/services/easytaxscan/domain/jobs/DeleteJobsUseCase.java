package pt.jumia.services.easytaxscan.domain.jobs;

import lombok.AllArgsConstructor;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Job;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.repository.JobsRepository;

@Component
@AllArgsConstructor
public class DeleteJobsUseCase {

    private JobsRepository jobsRepository;

    public void deleteScan<PERSON>ob(Scan scanSaved) throws SchedulerException {
        Job job = Job.builder().jobName(scanSaved.getCode())
                .cronExpression(scanSaved.getCronExpression()).build();
        jobsRepository.deleteJob(job);
    }

    public void deleteRetryJob(String jobName) throws SchedulerException {
        Job job = Job.builder().jobName(jobName).build();
        jobsRepository.deleteJob(job);
    }
}
