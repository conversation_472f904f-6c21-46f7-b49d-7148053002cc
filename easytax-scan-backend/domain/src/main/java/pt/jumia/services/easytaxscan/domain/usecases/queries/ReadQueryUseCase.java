package pt.jumia.services.easytaxscan.domain.usecases.queries;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.filter.QueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.QuerySortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.QueryRepository;

import java.util.List;
import java.util.Optional;


@Component
@RequiredArgsConstructor
public class ReadQueryUseCase {

    private final QueryRepository queryRepository;
    private final JsonExtractorUseCase jsonExtractorUseCase;


    public Query findByCode(String code) throws NotFoundException {
        return queryRepository.findByCode(code)
                .orElseThrow(() -> NotFoundException.build(Query.class, code));
    }

    public List<Query> execute(QueryFilters filters, QuerySortFilters sortFilters, PageFilters pageFilters) {
        return queryRepository.findAll(filters, sortFilters, pageFilters);
    }

    public Query findById(Long id) throws NotFoundException {
        return queryRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(Query.class, id));
    }

    public List<String> fetchFields(Long id) throws NotFoundException {
        Query query = queryRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(Query.class, id));

        return Optional.ofNullable(query.getSampleResult())
                .filter(StringUtils::isNotEmpty)
                .map(jsonExtractorUseCase::analyzeJson)
                .orElseThrow(() -> new IllegalArgumentException("Sample result is empty for query ID: " + id));
    }

    public Long executeCount(QueryFilters filters) {
        return queryRepository.executeCount(filters);
    }

}
