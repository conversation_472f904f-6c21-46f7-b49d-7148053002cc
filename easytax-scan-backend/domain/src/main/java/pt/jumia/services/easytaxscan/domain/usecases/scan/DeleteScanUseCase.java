package pt.jumia.services.easytaxscan.domain.usecases.scan;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;


@Component
@RequiredArgsConstructor
public class DeleteScanUseCase {

    private final ScanRepository scanRepository;
    private final ReadScanUseCase readScanUseCase;

    public void execute(Long id) throws NotFoundException {
        readScanUseCase.execute(id);
        scanRepository.deleteById(id);
    }

}
