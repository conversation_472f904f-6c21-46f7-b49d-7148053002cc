package pt.jumia.services.easytaxscan.domain.templates.utils;

import java.text.NumberFormat;
import java.util.Locale;

import static java.lang.Math.abs;

/**
 * Utility class with number related methods to be used on templates.
 */
public class NumberTemplateUtils {

    public static final String NAME = "numberUtils";
    private static final String[] SHORT_NUMBER_SUFFIX = {"", "K", "M", "B", "T"};
    private static final String LANGUAGE_EN = "en";
    private static final String COUNTRY_US = "US";

    public String formatLocale(Double number, String language, String country) {
        return number == null ? null : NumberFormat.getInstance(new Locale(language, country)).format(number);
    }

    public String formatNumericWithoutSpaces(Double number) {
        return formatLocale(number, LANGUAGE_EN, COUNTRY_US).replace(",", "");
    }

    public String formatMaxFractionDigits(Double number, String language, String country, Integer maxFractionDigit){
        if (number == null) {
            return null;
        }

        NumberFormat numberFormat = NumberFormat.getInstance(new Locale(language, country));
        numberFormat.setMaximumFractionDigits(maxFractionDigit);
        return numberFormat.format(number);
    }

    public String compactNumberFormat(Double number, String language, String country, Integer maxFractionDigit){
        if (number == null) {
            return null;
        }

        int idx = 0;
        double valueTo = abs(number);
        while ((valueTo > 1000) && (SHORT_NUMBER_SUFFIX.length - 1 > idx)){
            valueTo /= 1000;
            idx++;
        }
        valueTo = number < 0 ? valueTo *= -1 : valueTo;
        return formatMaxFractionDigits(valueTo , language, country, maxFractionDigit) + SHORT_NUMBER_SUFFIX[idx];
    }

    public String formatLocale(Double number) {
        return formatLocale(number, LANGUAGE_EN, COUNTRY_US);
    }

    public String formatMaxFractionDigits(Double number, Integer maxFractionDigit){
        return formatMaxFractionDigits(number, LANGUAGE_EN, COUNTRY_US, maxFractionDigit);
    }

    public String compactNumberFormat(Double number, Integer maxFractionDigit){
        return compactNumberFormat(number, LANGUAGE_EN, COUNTRY_US, maxFractionDigit);
    }

}
