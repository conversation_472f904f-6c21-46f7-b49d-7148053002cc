package pt.jumia.services.easytaxscan.domain.entities.mappingtemplates;

/**
 * Wrapper contract to be followed by every wrapper that aims at using a given entity from Frodo.
 * By being wrapped by this interface, we will be able to process this objects, assert rules, generate templates, etc
 */
public interface WrappedObject {

    /**
     * @return the object we are wrapping, which will be injected to JEXL and FreeMarker
     */
    Object getObject();

    /**
     * @return the name that we should use when injecting the object
     */
    String getObjectName();

}
