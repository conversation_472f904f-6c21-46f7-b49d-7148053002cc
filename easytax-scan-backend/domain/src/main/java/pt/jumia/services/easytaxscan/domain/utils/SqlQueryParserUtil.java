package pt.jumia.services.easytaxscan.domain.utils;

import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.FromItem;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class SqlQueryParserUtil {
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile(":(\\w+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern COLUMN_PATTERN = Pattern.compile("(?<=\\.)\\w+$");

    public static List<String> extractColumns(String sql) throws Exception {
        Statement statement = CCJSqlParserUtil.parse(sql);
        if (!(statement instanceof Select)) {
            throw new IllegalArgumentException("SQL is not a SELECT statement");
        }

        PlainSelect select = (PlainSelect) ((Select) statement).getSelectBody();
        return select.getSelectItems().stream()
                .map(item -> {
                    if (item.getAlias() != null) {
                        return item.getAlias().getName();
                    } else {
                        return extractColumnName(item.getExpression().toString());
                    }
                })
                .collect(Collectors.toList());
    }

    public static String extractColumnName(String expression) {
        return Optional.of(COLUMN_PATTERN.matcher(expression))
                .filter(Matcher::find)
                .map(Matcher::group)
                .orElse(expression);
    }

    public static List<String> extractPlaceholders(String sql) {
        if (StringUtils.isBlank(sql)) {
            throw new IllegalArgumentException("SQL query cannot be null or empty");
        }
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(sql);
        return matcher.results()
                .map(match -> match.group(1))
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    public static List<String> extractTables(String sql) throws Exception {
        Statement statement = CCJSqlParserUtil.parse(sql);
        if (!(statement instanceof Select)) {
            throw new IllegalArgumentException("SQL is not a SELECT statement");
        }
        PlainSelect select = (PlainSelect) ((Select) statement).getSelectBody();
        return extractTableFromFromItem(select.getFromItem())
                .distinct()
                .collect(Collectors.toList());
    }

    private static Stream<String> extractTableFromFromItem(FromItem fromItem) {
        if (fromItem instanceof Table) {
            return Stream.of(((Table) fromItem).getName());
        }
        return Stream.empty();
    }

}
