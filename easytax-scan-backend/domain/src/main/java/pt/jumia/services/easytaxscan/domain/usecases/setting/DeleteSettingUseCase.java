package pt.jumia.services.easytaxscan.domain.usecases.setting;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.repository.SettingRepository;

@RequiredArgsConstructor
@Component
public class DeleteSettingUseCase {

    private final ReadSettingUseCase readSettingUseCase;
    private final SettingRepository settingRepository;

    public Setting execute(long id) {

        Setting setting = readSettingUseCase.findById(id);
        if (!setting.isTypeOverride()) {
            throw new IllegalArgumentException("Only settings of type OVERRIDE can be deleted.");
        }

        settingRepository.deleteById(id);
        return setting;
    }
}
