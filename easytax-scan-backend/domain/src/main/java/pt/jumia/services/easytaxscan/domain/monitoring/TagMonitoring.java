package pt.jumia.services.easytaxscan.domain.monitoring;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.monitoring.GroupCounter;
import pt.jumia.services.easytaxscan.domain.entities.monitoring.GroupCounterFactory;

@Component
public class TagMonitoring {

    private static final String TAGS_CREATED_COUNTER_NAME = "tags.created";
    private static final String TAGS_CREATED_COUNTER_DESCRIPTION = "Total number of tags created";

    private final GroupCounter tagsCreated;

    @Autowired
    public TagMonitoring(GroupCounterFactory groupCounterFactory){
        this.tagsCreated = groupCounterFactory.createShopsCounters(TAGS_CREATED_COUNTER_NAME, TAGS_CREATED_COUNTER_DESCRIPTION);
    }

    public void recordTagCreation(String shop) {
        this.tagsCreated.increment(shop);
    }

}
