package pt.jumia.services.easytaxscan.domain.usecases.webhook;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.repository.WebhooksRepository;

import java.util.Objects;

@RequiredArgsConstructor
@Component
public class CreateWebhooksUseCase {

    private final WebhooksRepository webhooksRepository;

    public WebHooks execute(WebHooks webhooks) {

        if (Objects.isNull(webhooks)) {
            throw new IllegalArgumentException("Webhooks to save cannot be null.");
        }
        return webhooksRepository.insert(webhooks);
    }
}
