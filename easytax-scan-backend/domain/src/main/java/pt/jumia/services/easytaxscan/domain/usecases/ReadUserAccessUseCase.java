package pt.jumia.services.easytaxscan.domain.usecases;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.AccessController;

import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Component
public class ReadUserAccessUseCase {

    private final AccessController accessController;

    public Map<String, Map<String, List<String>>> execute(RequestUser requestUser) {
        return this.accessController.getPermissions(requestUser);
    }

    public String findRealToken(String temporaryToken) {
        return this.accessController.findRealToken(temporaryToken);
    }

    public void logout(RequestUser requestUser) {
        this.accessController.logout(requestUser);
    }
}
