package pt.jumia.services.easytaxscan.domain.repository;

import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogSortFilters;

import java.util.List;
import java.util.Optional;

/**
 * Contract between application and persistence layer for CRUD operation for {@link ExecutionLog}
 */
public interface ExecutionLogRepository {

    ExecutionLog insert(ExecutionLog executionLog);

    ExecutionLog update(long id, ExecutionLog executionLog);

    Optional<ExecutionLog> findById(Long id);


    List<ExecutionLog> findAll(ExecutionLogFilters filters,
                               ExecutionLogSortFilters sortFilters,
                               PageFilters pageFilters);

    void deleteById(long id);

    Long executeCount(ExecutionLogFilters filters);
}
