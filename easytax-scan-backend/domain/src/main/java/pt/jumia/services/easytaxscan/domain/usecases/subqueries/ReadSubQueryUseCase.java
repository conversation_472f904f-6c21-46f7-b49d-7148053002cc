package pt.jumia.services.easytaxscan.domain.usecases.subqueries;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;
import pt.jumia.services.easytaxscan.domain.repository.SubQueryRepository;

import java.util.List;


@Component
@RequiredArgsConstructor
public class ReadSubQueryUseCase {

    private final SubQueryRepository subQueryRepository;


    public List<SubQuery> execute(Long id, SubQueryFilters filters, SubQuerySortFilters sortFilters, PageFilters pageFilters) {
        return subQueryRepository.findAll(id, filters, sortFilters, pageFilters);
    }

    public Long executeCount(Long id, SubQueryFilters filters) {
        return subQueryRepository.executeCount(id, filters);
    }

}
