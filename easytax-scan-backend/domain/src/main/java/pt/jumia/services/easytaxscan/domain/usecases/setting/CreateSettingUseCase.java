package pt.jumia.services.easytaxscan.domain.usecases.setting;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.DataEventsNotificator;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.exceptions.DataIntegrityViolationException;
import pt.jumia.services.easytaxscan.domain.repository.SettingRepository;

@RequiredArgsConstructor
@Component
public class CreateSettingUseCase {

    private final SettingRepository settingRepository;
    private final DataEventsNotificator dataEventsNotificator;

    public Setting execute(Setting setting) {

        if (Objects.isNull(setting)) {
            throw new IllegalArgumentException("Setting cannot be null.");
        }

        if (!setting.isTypeOverride() || setting.isOverrideKeyEmpty()) {
            throw new IllegalArgumentException("You can only create override settings.");
        }

        List<Setting> settingsWithProperty = settingRepository.findByProperty(setting.getProperty());
        if (settingsWithProperty.isEmpty()) {
            throw new IllegalArgumentException("You must have a default setting to make an override.");
        }

        Setting settingUpdated;
        try {
            settingUpdated = settingRepository.insert(setting);
        } catch (DataIntegrityViolationException e) {
            throw new IllegalArgumentException("This override key has already been used for this property.");
        }

        this.dataEventsNotificator.notifySettingChanges();
        return settingUpdated;
    }
}
