package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Some fake data for {@link pt.jumia.services.easytaxscan.domain.entities.Query}, to be used in tests and fill in the MEM DB
 */
public interface FakeQuery {

    Query QUERY_CREATE = createQueryRequest(1L, "SELECT mainquery, subQuery FROM users WHERE active = true AND id > :fromId AND id IN (:sidList)",
            "SQL001", "Query Create Test", 10, "field1", DataSource.Status.ACTIVE, "Sample Result 1");
    Query QUERY_FAIL = createQueryRequest(null, "SELECT mainquery, subQuery FROM users WHERE active = true AND id > :fromId AND id IN (:sidList)",
            "SQL002FAIL", "Query Create Fail Test", 10, "field2", DataSource.Status.INACTIVE, "Sample Result 2");
    Query QUERY_UPDATE = createQueryRequest(4L, "SELECT mainquery, subQuery FROM users WHERE active = true AND id > :fromId AND id IN (:sidList)",
            "SQL1000", "Query Create Test", 20, "field3", DataSource.Status.INACTIVE, "Sample Result 3");
    Query QUERY_FILTER_DATA1 = createQueryRequest
            (2L, "SELECT mainquery, subQuery FROM users WHERE active = true AND id > :fromId AND id IN (:sidList)",
                    "SQL2000", "Query Data1 Test", 15, "field4", DataSource.Status.ACTIVE, "Sample Result 4");
    Query QUERY_FILTER_DATA2 = createQueryRequest
            (3L, "SELECT mainquery, subQuery FROM users WHERE active = true AND id > :fromId AND id IN (:sidList)",
                    "SQL3000", "Query Data2 Test", 30, "field5", DataSource.Status.INACTIVE, "Sample Result 5");

    public static Query createQueryRequest(Long id, String sql, String code, String desc,
                                           int pageSize, String paginationField, DataSource.Status status, String sampleResult) {
        return Query.builder()
                .id(id)
                .sql(sql)
                .code(code)
                .description(desc)
                .pageSize(pageSize)
                .paginationField(paginationField)
                .dataSource(FakeDataSource.DATA_SOURCE_CREATE.withoutDbFields())
                .sampleResult(sampleResult)
                .createdBy(AppConstants.SYSTEM)
                .createdAt(LocalDateTime.now())
                .updatedBy(AppConstants.SYSTEM)
                .updatedAt(LocalDateTime.now())
                .build();
    }

    List<Query> ALL =
            Collections.unmodifiableList(new ArrayList<>() {{
                add(QUERY_FILTER_DATA1);
                add(QUERY_FILTER_DATA2);
                add(QUERY_UPDATE);
            }});
}
