package pt.jumia.services.easytaxscan.domain.templates.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * Utility class with date and time related methods to be used on templates.
 */
public class DateTemplateUtils {

    public static final String NAME = "dateUtils";
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public String format(LocalDateTime localDateTime, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(dateTimeFormatter);
    }

    public String formatDate(LocalDateTime localDateTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_FORMAT);
        return localDateTime.format(dateTimeFormatter);
    }

    public String formatDateTime(LocalDateTime localDateTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT);
        return localDateTime.format(dateTimeFormatter);
    }

    public LocalDateTime parse(String dateStr, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.parse(dateStr, dateTimeFormatter);
    }

    public LocalDateTime parseDateTime(String dateStr) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT);
        return LocalDateTime.parse(dateStr, dateTimeFormatter);
    }

    public LocalDateTime nowUTC() {
        return LocalDateTime.now(ZoneOffset.UTC);
    }

    public LocalDateTime today() {
        return LocalDate.now(ZoneOffset.UTC).atStartOfDay();
    }

    public LocalDateTime startOfMonth() {
        return LocalDate.now(ZoneOffset.UTC).withDayOfMonth(1).atStartOfDay();
    }

    public LocalDateTime nDaysAgo(long n) {
        return LocalDateTime.now(ZoneOffset.UTC).minusDays(n);
    }

    public LocalDateTime nHoursAgo(long n) {
        return LocalDateTime.now(ZoneOffset.UTC).minusHours(n);
    }

    public LocalDateTime atStartOfDay(LocalDateTime localDateTime) {
        return localDateTime.toLocalDate().atStartOfDay();
    }

    public long toEpochSec(LocalDateTime localDateTime) {
        return localDateTime.toEpochSecond(ZoneOffset.UTC);
    }
}
