package pt.jumia.services.easytaxscan.domain.repository;

import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;

import java.util.List;

/**
 * Contract between application and persistence layer for SubQuery operation for {@link SubQuery}
 */
public interface SubQueryRepository {

    SubQuery insert(Long id, SubQuery subQuery);

    SubQuery update(Long id, Long subQueryId, SubQuery subQuery);

    Long executeCount(Long id, SubQueryFilters filters);

    List<SubQuery> findByQueryId(Long id) throws Exception;

    List<SubQuery> findAll(Long id, SubQueryFilters filters,
                           SubQuerySortFilters sortFilters,
                           PageFilters pageFilters);

    void deleteSubQueryByQueryId(Long queryId);

    public void delete(Long mainQueryId, Long subQueryId);


}
