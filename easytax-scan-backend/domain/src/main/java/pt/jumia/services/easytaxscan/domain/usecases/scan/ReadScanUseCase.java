package pt.jumia.services.easytaxscan.domain.usecases.scan;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;

import java.util.List;


@Component
@RequiredArgsConstructor
public class ReadScanUseCase {

    private final ScanRepository scanRepository;

    public List<Scan> execute(ScanFilters filters, ScanSortFilters sortFilters, PageFilters pageFilters) {
        return scanRepository.findAll(filters, sortFilters, pageFilters);
    }

    public Scan findByCode(String code) throws NotFoundException {
        return scanRepository.findByCode(code)
                .orElseThrow(() -> NotFoundException.build(Scan.class, code));
    }

    public Long executeCount(ScanFilters filters) {
        return scanRepository.executeCount(filters);
    }

    public Scan execute(Long id) throws NotFoundException {
        return scanRepository.findById(id);
    }

    public Scan execute(String code) throws NotFoundException {
        return scanRepository.findByCode(code)
                .orElseThrow(() -> NotFoundException.build(Scan.class, code));
    }

}
