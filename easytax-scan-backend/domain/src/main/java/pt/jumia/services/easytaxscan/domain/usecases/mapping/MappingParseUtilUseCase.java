package pt.jumia.services.easytaxscan.domain.usecases.mapping;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.MappingPreviewRequest;
import pt.jumia.services.easytaxscan.domain.entities.QueryParse;
import pt.jumia.services.easytaxscan.domain.utils.SqlQueryParserUtil;
import pt.jumia.services.easytaxscan.domain.utils.TemplateParsingUtils;

import java.util.List;

@Component
@RequiredArgsConstructor
public class MappingParseUtilUseCase {
    private final TemplateParsingUtils templateParsingUtils;

    public String execute(MappingPreviewRequest mappingPreviewRequest) throws Exception {
        String parsedTemplate = templateParsingUtils.parse("test", mappingPreviewRequest.getMapping(), mappingPreviewRequest.getContext());

        return parsedTemplate;
    }
}
