package pt.jumia.services.easytaxscan.domain.entities.filter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ScheduledExecutionFilters {

    private String filterText;
    private String scanCode;
    private LocalDateTime nextFireTimeFrom;
    private LocalDateTime nextFireTimeTo;
}
