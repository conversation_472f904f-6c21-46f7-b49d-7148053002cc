package pt.jumia.services.easytaxscan.domain.exceptions;

import java.io.Serial;

public class DataIntegrityViolationException extends CodedException {
    @Serial
    private static final long serialVersionUID = 3906017821939427445L;

    private DataIntegrityViolationException(Exception rootException) {

        super(ErrorCode.DATA_INTEGRITY_VIOLATION, rootException);
    }

    public static DataIntegrityViolationException build(Exception rootException) {

        return new DataIntegrityViolationException(rootException);
    }

}
