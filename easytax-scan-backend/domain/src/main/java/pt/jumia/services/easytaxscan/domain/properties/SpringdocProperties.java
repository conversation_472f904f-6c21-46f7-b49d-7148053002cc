package pt.jumia.services.easytaxscan.domain.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "springdoc")
public class SpringdocProperties {

    private SwaggerUi swaggerUi = new SwaggerUi();

    @Data
    public static class SwaggerUi {
        private String path = "swagger-ui.html";
        private boolean enabled = true;
    }
}
