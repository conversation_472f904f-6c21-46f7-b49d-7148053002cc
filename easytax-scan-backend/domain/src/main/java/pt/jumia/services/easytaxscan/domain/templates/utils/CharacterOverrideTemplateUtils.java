package pt.jumia.services.easytaxscan.domain.templates.utils;

import org.apache.commons.lang3.StringEscapeUtils;
import pt.jumia.services.easytaxscan.domain.CharacterOverridePair;
import pt.jumia.services.easytaxscan.domain.settings.OverallSettings;
import pt.jumia.services.easytaxscan.domain.utils.BeanUtils;

import java.util.List;

/**
 * Utility class used to replace special characters on templates.
 */
public class CharacterOverrideTemplateUtils {

    public static final String NAME = "characterOverrideUtils";

    public String replaceCharacters(String message, String overrideKey) {

        OverallSettings overallSettings = BeanUtils.getBean(OverallSettings.class);
        message = StringEscapeUtils.unescapeHtml3(message);
        message = StringEscapeUtils.unescapeHtml4(message);
        message = StringEscapeUtils.unescapeXml(message);
        final List<CharacterOverridePair> overrides = overallSettings.getCharacterOverrideSettings().getOverrides(overrideKey);

        for (CharacterOverridePair pair : overrides) {
            message = message.replace(pair.getOriginalCharacter(), pair.getReplacementCharacter());
        }
        return message;
    }

}
