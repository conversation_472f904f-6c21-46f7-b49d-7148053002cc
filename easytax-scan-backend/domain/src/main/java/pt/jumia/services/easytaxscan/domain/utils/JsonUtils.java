package pt.jumia.services.easytaxscan.domain.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.function.ThrowingSupplier;

/**
 * Some JSON utilities
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class JsonUtils {

    private final ObjectMapper objectMapper;

    public String toJsonOrNull(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize json", e);
            return null;
        }
    }

    public String toJsonOrGet(Object object, ThrowingSupplier<String> fallback) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            try {
                return fallback.get();
            } catch (Exception a) {
                log.error("Failed to serialize json", a);
                return null;
            }
        }
    }

    public String toPrettyJsonOrNull(Object object) {
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize json", e);
            return null;
        }
    }

    public <T> T fromJsonOrNull(String json, Class<T> clazz) {
        if (json == null) {
            return null;
        }

        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            log.error("Failed to deserialize json", e);
            return null;
        }
    }

    public <T> T fromJsonOrNull(String json, TypeReference<T> valueTypeRef) {
        if (json == null) {
            return null;
        }

        try {
            return objectMapper.readValue(json, valueTypeRef);
        } catch (Exception e) {
            log.error("Failed to deserialize json", e);
            return null;
        }
    }

    public <T> List<T> fromJsonListOrNull(String list, Class<T> clazz){
        if (list == null) {
            return List.of();
        }

        try {
            return objectMapper.reader(clazz).readValue(list);
        } catch (Exception e) {
            log.error("Failed to deserialize json", e);
            return List.of();
        }
    }
}
