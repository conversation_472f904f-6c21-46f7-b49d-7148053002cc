package pt.jumia.services.easytaxscan.domain.repository;


import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.filter.WebhooksSortFilters;

import java.util.List;
import java.util.Optional;

public interface WebhooksRepository {

    WebHooks insert(WebHooks webHooks);

    Optional<WebHooks> findById(long id);

    List<WebHooks> findAll(WebhooksSortFilters webhooksSortFilters, PageFilters pageFilters);

    Long executeCount();

    void deleteById(long id);
}
