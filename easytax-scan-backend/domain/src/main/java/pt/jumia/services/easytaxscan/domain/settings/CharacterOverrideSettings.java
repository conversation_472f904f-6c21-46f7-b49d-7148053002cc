package pt.jumia.services.easytaxscan.domain.settings;

import pt.jumia.services.acl.lib.utils.JsonUtils;
import pt.jumia.services.easytaxscan.domain.CharacterOverridePair;

import java.util.List;

public class CharacterOverrideSettings {

    public static final String DEFAULT_VALUE = "[{\n"
            + "  \"originalCharacter\": \"c\",\n"
            + "  \"replacementCharacter\": \"c\"\n"
            + "}]";
    private static final String SPECIAL_CHARACTER_OVERRIDES = "special_character_overrides";

    private final OverallSettings.OverridableSetting<String> specialCharacterOverrides;

    public CharacterOverrideSettings(OverallSettings overallSettings) {

        this.specialCharacterOverrides = overallSettings.createSetting(
                SPECIAL_CHARACTER_OVERRIDES,
                DEFAULT_VALUE,
                String::new);
    }

    public List<CharacterOverridePair> getOverrides(String overrideKey) {

        final String override = this.specialCharacterOverrides.getOverride(overrideKey);

        return JsonUtils.fromJsonList(override, CharacterOverridePair.class);

    }

}
