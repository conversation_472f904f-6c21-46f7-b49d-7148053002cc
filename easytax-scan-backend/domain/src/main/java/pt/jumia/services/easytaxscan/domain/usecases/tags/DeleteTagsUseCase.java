package pt.jumia.services.easytaxscan.domain.usecases.tags;

import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Tag;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.TagRepository;

/**
 * Use case that will delete a tag by its id from persistence layer
 */
@Component
public class DeleteTagsUseCase {

    @Autowired
    private TagRepository tagRepository;

    public void execute(long id) throws NotFoundException {
        
        Optional<Tag> tagToDelete = tagRepository.findById(id);

        if (!tagToDelete.isPresent()) {
            throw NotFoundException.createNotFound(Tag.class, id);
        }

        tagRepository.deleteById(id);
    }
}
