package pt.jumia.services.easytaxscan.domain.usecases.validation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.exceptions.BadRequestException;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ReadQueryUseCase;
import pt.jumia.services.easytaxscan.domain.utils.SqlQueryParserUtil;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ValidateQueryUseCase {

    private final ReadQueryUseCase readQueryUseCase;

    public void validate(Query query) throws Exception {
        validatePagination(query);
        validateSubQuery(query);
    }

    private List<String> extractPlaceholders(Query query) {
        return SqlQueryParserUtil.extractPlaceholders(query.getSql());
    }

    private void validatePagination(Query query) {
        List<String> placeholders = extractPlaceholders(query);

        if (!StringUtils.hasLength(query.getPaginationField())) {
            if (!placeholders.contains(Query.QUERY_ARG_OFFSET)) {
                throw BadRequestException.build(Query.QUERY_ARG_OFFSET + " qyery argument is required when paginationField is null.");
            }
        } else {
            if (!placeholders.contains(Query.QUERY_ARG_PAGINATION_FIELD)) {
                throw BadRequestException.build(Query.QUERY_ARG_PAGINATION_FIELD + " query argument is required when paginationField is not null.");
            }
        }

        if (!placeholders.contains(Query.QUERY_ARG_LIMIT)) {
            throw BadRequestException.build(Query.QUERY_ARG_LIMIT + " placeholder is required.");
        }
    }

    public void validateSubQuery(Query query) throws Exception {
        List<SubQuery> subQueryList = query.getSubQueries();

        if (!CollectionUtils.isEmpty(subQueryList)) {
            List<String> listOfColumns = SqlQueryParserUtil.extractColumns(query.getSql());
            subQueryList.forEach(subQuery -> {


                // validating main query column presence
                validateQueryColumn(listOfColumns, subQuery.getMainQueryColumn());
                Query subQueryObject = readQueryUseCase.findById(subQuery.getSubQuery().getId());

                validateSidList(subQueryObject);
            });
        }
    }

    private List<String> extractColumnsWithExceptionHandling(String sql) {
        try {
            return SqlQueryParserUtil.extractColumns(sql);
        } catch (Exception e) {
            log.error("Error extracting columns from SQL query: {}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException("Error extracting columns from SQL query: " + e.getMessage(), e);
        }
    }

    private void validateQueryColumn(List<String> listOfColumns, String queryColumn) {
        boolean isQueryColumnMatch = listOfColumns.stream()
                .anyMatch(column -> column.equals(queryColumn));

        if (!isQueryColumnMatch) {
            log.error(" query column is not matched: {}", queryColumn);
            throw BadRequestException.build(queryColumn + " is not part of the main query columns: " + queryColumn);
        }
    }


    private void validateSidList(Query subQueryObject) {
        List<String> listOfPlaceHolders = SqlQueryParserUtil.extractPlaceholders(subQueryObject.getSql());
        if (!listOfPlaceHolders.contains(Query.QUERY_ARG_SID_LIST)) {
            log.error("sidList is not present in the subquery");
            throw BadRequestException.build(Query.QUERY_ARG_SID_LIST + " is not present");
        }
    }
}
