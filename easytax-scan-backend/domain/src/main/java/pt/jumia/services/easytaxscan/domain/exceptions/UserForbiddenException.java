package pt.jumia.services.easytaxscan.domain.exceptions;

import pt.jumia.services.acl.lib.AclErrorException;

import java.io.Serial;

/**
 * Exception thrown when user is not authorized execute tasks
 */
public class UserForbiddenException extends CodedException {
    @Serial
    private static final long serialVersionUID = -8652215746483266031L;

    public UserForbiddenException(String message) {
        super(ErrorCode.FORBIDDEN, message);
    }

    public static UserForbiddenException create(String msg) {
        return new UserForbiddenException(msg);
    }

    public static UserForbiddenException createFromAclErrorException(AclErrorException aclErrorException) {
        return new UserForbiddenException(aclErrorException.getMessage());
    }

    public static UserForbiddenException createDontHavePermission(String username, String permissionCode) {
        return new UserForbiddenException(String.format("The user '%s' does not have permission '%s' on application easytaxscan",
                username,
                permissionCode));
    }

}
