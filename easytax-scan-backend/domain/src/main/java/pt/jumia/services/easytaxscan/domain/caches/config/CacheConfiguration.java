package pt.jumia.services.easytaxscan.domain.caches.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "cache")
public class CacheConfiguration {
    private CacheProperties template = new CacheProperties();
    private CacheProperties rule = new CacheProperties();
}
