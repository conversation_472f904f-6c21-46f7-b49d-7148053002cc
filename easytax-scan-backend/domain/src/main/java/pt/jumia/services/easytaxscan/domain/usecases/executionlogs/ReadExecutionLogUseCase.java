package pt.jumia.services.easytaxscan.domain.usecases.executionlogs;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ExecutionLogSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.ExecutionLogRepository;

import java.util.List;


@Component
@RequiredArgsConstructor
public class ReadExecutionLogUseCase {

    private final ExecutionLogRepository executionLogsRepository;

    public List<ExecutionLog> execute(ExecutionLogFilters filters, ExecutionLogSortFilters sortFilters, PageFilters pageFilters) {
        return executionLogsRepository.findAll(filters, sortFilters, pageFilters);
    }

    public Long executeCount(ExecutionLogFilters filters) {
        return executionLogsRepository.executeCount(filters);
    }

    public ExecutionLog execute(Long id) throws NotFoundException {
        return executionLogsRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(ExecutionLog.class, id));
    }

}
