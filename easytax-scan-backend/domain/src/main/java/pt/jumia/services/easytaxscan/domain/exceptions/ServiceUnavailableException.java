package pt.jumia.services.easytaxscan.domain.exceptions;

import java.io.Serial;

public class ServiceUnavailableException extends CodedException {
    @Serial
    private static final long serialVersionUID = 123456789L; // Use a unique serial version ID

    public static ServiceUnavailableException build(String message) {
        return new ServiceUnavailableException(ErrorCode.SERVICE_UNAVAILABLE, message);
    }

    protected ServiceUnavailableException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }
}
