package pt.jumia.services.easytaxscan.domain.repository.audit;

import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.ScanAuditFilters;

import java.util.List;

public interface ScanAuditRepository {
    List<AuditedEntity<Scan>> getScanAuditLogById(ScanAuditFilters filters, Scan scan);

    long getAuditLogCountById(ScanAuditFilters filters);
}
