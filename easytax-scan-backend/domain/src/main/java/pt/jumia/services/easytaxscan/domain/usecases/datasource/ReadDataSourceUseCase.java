package pt.jumia.services.easytaxscan.domain.usecases.datasource;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DataSourceSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceRepository;

import java.util.List;


@Component
@RequiredArgsConstructor
public class ReadDataSourceUseCase {

    private final DataSourceRepository dataSourceRepository;

    public List<DataSource> execute(DataSourceFilters filters, DataSourceSortFilters sortFilters, PageFilters pageFilters) {
        return dataSourceRepository.findAll(filters, sortFilters, pageFilters);
    }

    public DataSource findByCode(String code) throws NotFoundException {
        return dataSourceRepository.findByCode(code)
                .orElseThrow(() -> NotFoundException.build(DataSource.class, code));
    }

    public DataSource findById(Long id) {
        return dataSourceRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(DataSource.class, id));
    }

    public Long executeCount(DataSourceFilters filters) {
        return dataSourceRepository.executeCount(filters);
    }

    public DataSource execute(Long id) throws NotFoundException {
        return dataSourceRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(DataSource.class, id));
    }

}
