package pt.jumia.services.easytaxscan.domain.templates.utils;

import org.apache.commons.lang3.StringUtils;


/**
 * Utility class with string related methods to be used on templates.
 */
public class StringTemplateUtils {

    public static final String NAME = "stringUtils";

    public String truncate(String str, int maxWidth) {
        return StringUtils.truncate(str, maxWidth);
    }

    public String substring(String str, int startIdx) {
        return StringUtils.substring(str, startIdx);
    }

    public String substring(String str, int startIdx, int endIdx) {
        return StringUtils.substring(str, startIdx, endIdx);
    }

    public String strip(String str) {
        return StringUtils.strip(str);
    }

    public String strip(String str, String stripChars) {
        return StringUtils.strip(str, stripChars);
    }

    public String replace(String str, CharSequence oldCharSeq, CharSequence newCharSeq) {
        return str.replace(oldCharSeq, newCharSeq);
    }
}
