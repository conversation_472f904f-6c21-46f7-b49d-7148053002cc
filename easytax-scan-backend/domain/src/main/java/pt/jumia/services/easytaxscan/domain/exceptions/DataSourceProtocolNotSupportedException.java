package pt.jumia.services.easytaxscan.domain.exceptions;

import java.io.Serial;
import java.text.MessageFormat;

public class DataSourceProtocolNotSupportedException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -1805991509105760350L;

    public DataSourceProtocolNotSupportedException(String message) {
        super(message);
    }

    public static DataSourceProtocolNotSupportedException createNotSupported(String dataSource) {
        return new DataSourceProtocolNotSupportedException(MessageFormat.format("Datasource '{0}' is not supported", dataSource));
    }
}
