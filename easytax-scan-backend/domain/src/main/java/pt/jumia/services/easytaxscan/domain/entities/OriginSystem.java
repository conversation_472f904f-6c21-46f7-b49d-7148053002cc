package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class OriginSystem {

    @Nullable
    private Integer id;

    private String name;

    private String createdBy;

    private LocalDateTime createdAt;

    private String updatedBy;

    private LocalDateTime updatedAt;

    public OriginSystem withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdBy(null)
                .createdAt(null)
                .updatedBy(null)
                .updatedAt(null)
                .build();
    }
}
