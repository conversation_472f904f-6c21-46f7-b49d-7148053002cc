package pt.jumia.services.easytaxscan.domain.entities.settings;

import pt.jumia.services.easytaxscan.domain.settings.OverallSettings;

public class DocumentProducerSettings {

    private static final String KAFKA_DOCUMENT_PRODUCER = "kafka.document_producer";

    private final OverallSettings.OverridableSetting<Boolean> kafkaDocumentProducerEnabled;

    public DocumentProducerSettings(OverallSettings overallSettings) {
        this.kafkaDocumentProducerEnabled = overallSettings.createSetting(
                KAFKA_DOCUMENT_PRODUCER,
                false,
                Boolean::parseBoolean);
    }

    public Boolean getKafkaDocumentProducerEnabled() {
        return this.kafkaDocumentProducerEnabled.getOverride();
    }


}
