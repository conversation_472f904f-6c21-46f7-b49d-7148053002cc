package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class ShopMessageMapping {


    private Long id;

    private Shop shop;

    private MessageMapping messageMapping;

    @Builder.Default
    private boolean preProdEnabled = true;
    @Builder.Default
    private boolean prodEnabled = true;

    private Status status;

    private Message.Mode mode;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;


    public enum Status {
        ACTIVE,

        INACTIVE;
    }

    public ShopMessageMapping withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .updatedAt(null)
                .updatedBy(null)
                .build();
    }
}
