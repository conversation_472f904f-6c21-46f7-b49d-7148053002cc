package pt.jumia.services.easytaxscan.domain.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.convert.DurationUnit;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

@Data
@Component
@ConfigurationProperties(prefix = "acl")
public class AclProperties {

    private boolean skip = false;
    private String url = "http://internal-api-acl-staging.jumia.services";
    private String appName = "EasyTax-Scan";
    private Cache cache = new Cache();
    private MigratorUser migratorUser = new MigratorUser();

    @Data
    public static class Cache {

        private String strategy = "in-memory";
        private Redis redis = new Redis();
        private InMemory inMemory = new InMemory();

        @Data
        public static class Redis {
            private String host = "dev-communications.2smgfr.0001.euw1.cache.amazonaws.com";
            private int port = 6379;
            private String usernameKeyPrefix = "easytaxscan";
            private String password = "dummy";

            @DurationUnit(ChronoUnit.MINUTES)
            private Duration expirationDuration = Duration.ofMinutes(5);

            @DurationUnit(ChronoUnit.SECONDS)
            private Duration timeout = Duration.ofSeconds(0);
        }

        @Data
        public static class InMemory {
            @DurationUnit(ChronoUnit.MINUTES)
            private Duration expirationDuration = Duration.ofMinutes(5);
        }
    }

    @Data
    public static class MigratorUser {
        private String username = "dummy";
        private String password = "dummy";
    }
}
