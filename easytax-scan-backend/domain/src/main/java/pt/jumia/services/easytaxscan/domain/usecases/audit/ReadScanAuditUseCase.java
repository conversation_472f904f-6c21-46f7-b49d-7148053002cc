package pt.jumia.services.easytaxscan.domain.usecases.audit;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.ScanAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.ScanAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.scan.ReadScanUseCase;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ReadScanAuditUseCase {
    private final ScanAuditRepository scanAuditRepository;
    private final ReadScanUseCase readScanUseCase;

    public List<AuditedEntity<Scan>> executeById(long scanId, AuditedEntitySortFilters sortFilters, PageFilters pageFilters) {
        Scan scan = readScanUseCase.execute(scanId);
        return scanAuditRepository.getScanAuditLogById(ScanAuditFilters.builder()
                .id(scanId)
                .sortFilter(sortFilters)
                .pageFilters(pageFilters)
                .build(), scan);
    }

    public long executeCountByScanId(long scanId) {
        return scanAuditRepository.getAuditLogCountById(ScanAuditFilters.builder()
                .id(scanId)
                .build());
    }
}
