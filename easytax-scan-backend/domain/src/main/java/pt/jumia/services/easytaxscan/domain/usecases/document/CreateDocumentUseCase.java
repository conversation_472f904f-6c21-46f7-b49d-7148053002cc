package pt.jumia.services.easytaxscan.domain.usecases.document;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.BillRequester;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.Mode;
import pt.jumia.services.easytaxscan.domain.entities.bill.RequestResult;
import pt.jumia.services.easytaxscan.domain.repository.DocumentRepository;

import java.util.Objects;

@Component
@RequiredArgsConstructor
@Slf4j
public class CreateDocumentUseCase {

    private final DocumentRepository documentRepository;
    private final BillRequester billRequester;
    private final UpdateDocumentUseCase updateDocumentUseCase;


    public Document execute(Document document) {
        if (Objects.isNull(document)) {
            throw new IllegalArgumentException("Document to save cannot be null");
        }

        Document savedDocument = documentRepository.insert(document);

        if (Mode.DRYRUN.equals(document.getMode())) {
            log.info("Document created in dryrun mode: {}", savedDocument.getSid());
            return savedDocument;
        }

        try {
            RequestResult billResponse = billRequester.sendDocumentToBill(savedDocument.getRequestPayload(), savedDocument);
            if (RequestResult.Status.OK.equals(billResponse.getStatus())) {
                savedDocument.setStatus(Document.Status.SUBMITTED);
                log.info("Updating document status={} and documentSid={}", Document.Status.SUBMITTED, savedDocument.getSid());
            } else {
                savedDocument.setStatus(Document.Status.CREATED);
                log.info("Updating document failed status={} and documentSid={}", Document.Status.CREATED, savedDocument.getSid());
            }

            savedDocument.setResponsePayload(billResponse.getErrorDescription());
            savedDocument.setResponseCode(billResponse.getErrorCode());
            updateDocumentUseCase.update(savedDocument.getId(), savedDocument);
        } catch (Exception ex) {
            log.info("Updating document error status={} and documentSid={}. Error: {}",
                    Document.Status.ERROR, savedDocument.getSid(), ExceptionUtils.getStackTrace(ex));

            savedDocument.setResponsePayload(ExceptionUtils.getStackTrace(ex));
            savedDocument.setStatus(Document.Status.ERROR);
            updateDocumentUseCase.update(savedDocument.getId(), savedDocument);

        }
        return savedDocument;
    }
}
