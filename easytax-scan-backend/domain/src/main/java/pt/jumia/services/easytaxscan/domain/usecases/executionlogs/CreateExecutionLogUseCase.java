package pt.jumia.services.easytaxscan.domain.usecases.executionlogs;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.repository.ExecutionLogRepository;

import java.util.Objects;


@Component
@RequiredArgsConstructor
public class CreateExecutionLogUseCase {

    private final ExecutionLogRepository executionLogsRepository;

    public ExecutionLog execute(ExecutionLog executionLog) {
        if (Objects.isNull(executionLog)) {
            throw new IllegalArgumentException("ExecutionLog to save cannot be null");
        }

        return executionLogsRepository.insert(executionLog);

    }

}
