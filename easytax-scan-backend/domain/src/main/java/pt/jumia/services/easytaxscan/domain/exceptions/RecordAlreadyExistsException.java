package pt.jumia.services.easytaxscan.domain.exceptions;

import java.io.Serial;
import java.text.MessageFormat;

public class RecordAlreadyExistsException extends CodedException {

    @Serial
    private static final long serialVersionUID = 1024381488382429345L;

    public RecordAlreadyExistsException(String message) {
        super(ErrorCode.CONFLICT, message);
    }

    public static RecordAlreadyExistsException build(String message) {
        return new RecordAlreadyExistsException(message);
    }

    public static RecordAlreadyExistsException build(Class clazz, String code) {
        return new RecordAlreadyExistsException(MessageFormat.format("{0} with code {1} already exists.", clazz.getSimpleName(), code));
    }

    public static RecordAlreadyExistsException build(Class clazz, Long id1, Long id2) {
        return new RecordAlreadyExistsException(MessageFormat.format("{0} with ids ({1},{2}) already exists.", clazz.getSimpleName(), id1, id2));
    }

    public static RecordAlreadyExistsException build(Class clazz, String id1, String id2) {
        return new RecordAlreadyExistsException(MessageFormat.format("{0} with ids ({1},{2}) already exists.", clazz.getSimpleName(), id1, id2));
    }
}

