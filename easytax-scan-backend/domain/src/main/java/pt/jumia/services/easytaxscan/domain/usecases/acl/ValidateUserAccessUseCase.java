package pt.jumia.services.easytaxscan.domain.usecases.acl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.Permissions;
import pt.jumia.services.easytaxscan.domain.User;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;

@Component
@RequiredArgsConstructor
@Slf4j
public class ValidateUserAccessUseCase {

    private final GetAclUserUseCase getAclUserUseCase;

    public void checkCanAccess(RequestUser requestUser) {

        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccess()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.CAN_ACCESS);
        }
    }

    public void checkCanAccessSetting(RequestUser requestUser) {

        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessSetting()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.SETTING_ACCESS);
        }
    }

    public void checkCanManageSetting(RequestUser requestUser) {

        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageSetting()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.SETTING_MANAGE);
        }
    }

    public void checkCanManageWebhooks(RequestUser requestUser) {

        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageWebhooks()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.CAN_MANAGE_WEBHOOKS);
        }
    }

    public void checkCanAccessWebhooks(RequestUser requestUser) {

        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessWebhooks()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.CAN_ACCESS_WEBHOOKS);
        }
    }

    public void checkCanAccessDataSource(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessDataSource()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.DATASOURCE_ACCESS);
        }
    }

    public void checkCanAccessDocument(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessDocument()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.DOCUMENT_ACCESS);
        }
    }

    public void checkCanManageDataSource(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageDataSource()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.DATASOURCE_MANAGE);
        }
    }

    public void checkCanManageQuery(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageQuery()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.QUERY_MANAGE);
        }
    }

    public void checkCanAccessQuery(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessQuery()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.QUERY_ACCESS);
        }
    }

    public void checkCanManageScan(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageScan()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.SCAN_MANAGE);
        }
    }

    public void checkCanAccessScan(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessScan()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.SCAN_ACCESS);
        }
    }

    public void checkCanManageExecutionLog(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageExecutionLog()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.EXECUTION_LOG_MANAGE);
        }
    }

    public void checkCanAccessExecutionLog(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessExecutionLog()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.EXECUTION_LOG_ACCESS);
        }
    }

    public void checkCanAccessCountry(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccessCountry()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.COUNTRY_ACCESS);
        }
    }

    public void checkCanManageCountry(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageCountry()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.COUNTRY_MANAGE);
        }
    }

    public void checkCanManageJobs(RequestUser requestUser) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageJobs()) {
            throw UserForbiddenException.createDontHavePermission(requestUser.getUsername(), Permissions.JOBS_MANAGE);
        }
    }

}
