package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class Message {

    public enum SortingField {
        ID,
        SID,
       SUB_TYPE,
        STATUS,
        SHOP,
        MODE,
        ENV,
        RELATED_ENTITY,
        CREATED_AT,
        UPDATED_AT,
        NUMBER_OF_RESENDS
    }

    public enum Environment {
        PROD, PRE_PROD
    }

    @Nullable
    private Long id;

    private String sid;

    private Map<String, Object> payload;

    private String subType;

    private String company;

    private String respCenter;

    private String relatedEntity;

    private Status status;

    private String errorCode;

    private String errorDescription;

    private MessageType messageType;

    private Request request;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private Shop shop;

    private Long numberOfResends;

    private Boolean resent;
    @Builder.Default
    private Mode mode = Mode.LIVE;

    @Builder.Default
    private Environment env = Environment.PROD;

    private MessageMapping messageMapping;

    private MessageEvent messageEvent;

    private Universe universe;


    public Message withoutDbFields() {
        return toBuilder()
                .id(null)
                .request(request.withoutDbFields())
                .messageType(messageType.withoutDbFields())
                .createdAt(null)
                .updatedAt(null)
                .shop(shop.withoutDbFields())
                .numberOfResends(null)
                .messageMapping(messageMapping == null ? null : messageMapping.withoutDbFields())
                .messageEvent(messageEvent == null ? null : messageEvent.withoutDbFields())
                .universe(universe == null ? null : universe.withoutDbFields())
                .build();
    }

    public boolean isLiveMode() {
        return Mode.LIVE.equals(this.mode);
    }

    public boolean isFromMessageMapping() {
        return messageMapping != null;
    }

    public enum Status {
        NEW("New"),
        CREATED("Created successfully"),
        CREATE_FAILED("Creation failed"),
        PUBLISHED("Published successfully"),
        PUBLISH_FAILED("Publishing failed"),
        SUBMITTED("Submitted successfully"),
        SUBMIT_FAILED("Submission failed"),
        PROCESSED("Processed successfully"),
        PROCESS_FAILED("Processing failed"),
        CANCELLED("Cancelled");

        private final String name;

        Status(String name) {
            this.name = name;
        }

        public String toString() {
            return this.name;
        }
    }

    public enum Mode {
        LIVE,
        DRY_RUN;
    }
}
