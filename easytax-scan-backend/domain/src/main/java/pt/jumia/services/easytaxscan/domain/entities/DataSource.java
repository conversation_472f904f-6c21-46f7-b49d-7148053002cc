package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;

/**
 * Business representation of DataSource
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class DataSource {

    @Nullable
    private Long id;

    private String code;

    private Status status;

    private String description;

    private Boolean countrySegregated;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;

    public enum Status {
        ACTIVE, INACTIVE
    }

    public enum SortingFields {
        ID, UPDATED_AT
    }

    public DataSource withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .updatedBy(null)
                .updatedAt(null)
                .build();
    }

}
