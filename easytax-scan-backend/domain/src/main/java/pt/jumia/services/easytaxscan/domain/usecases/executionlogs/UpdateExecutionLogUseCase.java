package pt.jumia.services.easytaxscan.domain.usecases.executionlogs;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.ExecutionLogRepository;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

import java.util.Objects;

@Component
@RequiredArgsConstructor
public class UpdateExecutionLogUseCase {

    private final ExecutionLogRepository executionLogRepository;

    public ExecutionLog execute(Long id, ExecutionLog updatedData) {
        if (Objects.isNull(updatedData)) {
            throw new IllegalArgumentException("ExecutionLog to update cannot be null");
        }

        return executionLogRepository.update(id, updatedData);
    }
}
