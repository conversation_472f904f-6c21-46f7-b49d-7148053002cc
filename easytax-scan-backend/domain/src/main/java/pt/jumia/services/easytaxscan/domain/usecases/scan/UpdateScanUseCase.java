package pt.jumia.services.easytaxscan.domain.usecases.scan;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.jobs.CreateJobsUseCase;
import pt.jumia.services.easytaxscan.domain.jobs.DeleteJobsUseCase;
import pt.jumia.services.easytaxscan.domain.jobs.UpdateJobsUseCase;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;

import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateScanUseCase {

    private final ScanRepository scanRepository;
    private final CreateJobsUseCase createJobsUseCase;
    private final DeleteJobsUseCase deleteJobsUseCase;
    private final UpdateJobsUseCase updateJobsUseCase;

    public Scan execute(Long id, Scan toUpdate) throws NotFoundException {
        if (Objects.isNull(toUpdate)) {
            throw new IllegalArgumentException("Scan to update cannot be null");
        }
        Scan existingScan = scanRepository.findById(id);
        Scan updatedScan = scanRepository.update(id, toUpdate);
        addOrUpdateJob(existingScan, updatedScan);
        return updatedScan;
    }

    private void addOrUpdateJob(Scan existingScan, Scan updatedScan) {
        String oldStatus = existingScan.getStatus().name();
        String newStatus = updatedScan.getStatus().name();

        try {
            if (Scan.Status.ACTIVE.name().equals(oldStatus)) {
                if (Scan.Status.INACTIVE.name().equals(newStatus)) {
                    deleteJobsUseCase.deleteScanJob(updatedScan);
                } else if (!existingScan.getCronExpression().equals(updatedScan.getCronExpression())) {
                    updateJobsUseCase.updateScanJob(updatedScan);
                }
            } else if (Scan.Status.INACTIVE.name().equals(oldStatus) && Scan.Status.ACTIVE.name().equals(newStatus)) {
                createJobsUseCase.createScanJob(updatedScan);
            }
        } catch (SchedulerException e) {
            log.error("Exception while handling job for scan {}: {}", updatedScan.getCode(), e.getMessage());
        }
    }
}
