package pt.jumia.services.easytaxscan.domain.repository;


import java.util.List;
import java.util.Optional;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.entities.filter.SettingFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SettingSortFilters;

public interface SettingRepository {

    Setting insert(Setting setting);

    Setting update(long id, Setting setting);

    Optional<Setting> findById(long id);

    List<Setting> findByProperty(String property);

    List<Setting> findAll();

    void deleteById(long id);

    List<Setting> findAll(SettingFilters settingFilters,
        SettingSortFilters payeeSortFilters);

    long count(SettingFilters settingFilters);

}
