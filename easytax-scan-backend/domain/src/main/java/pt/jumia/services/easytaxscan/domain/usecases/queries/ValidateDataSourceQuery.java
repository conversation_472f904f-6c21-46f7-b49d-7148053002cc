package pt.jumia.services.easytaxscan.domain.usecases.queries;

import pt.jumia.services.easytaxscan.domain.entities.HealthStatus;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.QueryPreview;

import java.util.List;
import java.util.Map;

public interface ValidateDataSourceQuery {
    HealthStatus validate(Query query, Map<String, Object> args, String countryCode);

    List<Map<String, Object>> constructPreviewResponse(QueryPreview queryPreview) throws Exception;
}
