package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Some fake data for {@link pt.jumia.services.easytaxscan.domain.entities.DataSource}, to be used in tests and fill in the MEM DB
 */
public interface FakeDataSource {

    DataSource DATA_SOURCE_CREATE = createDataSourceRequest(null, DataSource.Status.ACTIVE, "DSCR0000", "DataSource Create Test");
    DataSource DATA_SOURCE_FAIL = createDataSourceRequest(null, DataSource.Status.ACTIVE, "DSCR0000FAIL", "DataSource Create Test");
    DataSource DATA_SOURCE_UPDATE = createDataSourceRequest(1L, DataSource.Status.INACTIVE, "DSCR1000", "DataSource Update Test");
    DataSource DATA_SOURCE_FILTER_DATA2 = createDataSourceRequest(2L, DataSource.Status.ACTIVE, "DSCR2000", "DataSource Data1 Test");
    DataSource DATA_SOURCE_FILTER_DATA3 = createDataSourceRequest(3L, DataSource.Status.INACTIVE, "DSCR3000", "DataSource Data2 Test");
    DataSource DATA_SOURCE_FILTER_DATA4 = createDataSourceRequest(4L, DataSource.Status.ACTIVE, "DSCR4000", "DataSource Data4 Test");
    DataSource DATA_SOURCE_FILTER_DATA6 = createDataSourceRequest(6L, DataSource.Status.INACTIVE, "DSCR6000", "DataSource Data6 Test");
    DataSource DATA_SOURCE_FILTER_DATA7 = createDataSourceRequest(7L, DataSource.Status.ACTIVE, "DSCR7000", "DataSource Data7 Test");
    DataSource DATA_SOURCE_TEST = createDataSourceRequest(5L, DataSource.Status.ACTIVE, "DSCR5000", "DataSource Data5 TestCase");
    DataSource DATA_SOURCE_HEALTHCHECK = createDataSourceRequest(null, DataSource.Status.ACTIVE, "OMSTEST", "DataSource OMS TestCase");



    public static DataSource createDataSourceRequest(Long id, DataSource.Status status, String code, String desc) {
        return DataSource.builder()
                .id(id)
                .status(status)
                .code(code)
                .description(desc)
                .countrySegregated(true)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .createdBy(AppConstants.SYSTEM)
                .updatedBy(AppConstants.SYSTEM)
                .build();
    }

    List<DataSource> ALL =
            Collections.unmodifiableList(new ArrayList<>() {{
                add(DATA_SOURCE_FILTER_DATA2);
                add(DATA_SOURCE_FILTER_DATA2);
                add(DATA_SOURCE_FILTER_DATA3);
            }});

}
