package pt.jumia.services.easytaxscan.domain.usecases.acl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.AccessController;
import pt.jumia.services.easytaxscan.domain.Permissions;
import pt.jumia.services.easytaxscan.domain.User;
import pt.jumia.services.easytaxscan.domain.exceptions.UserForbiddenException;
import pt.jumia.services.easytaxscan.domain.properties.AclProperties;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class GetAclUserUseCase {
    private static final String APPLICATION_KEY = "APPLICATION";

    private final AccessController accessController;
    private final String appCode;

    @Autowired
    public GetAclUserUseCase(AccessController accessController,
                             AclProperties aclProperties) {
        this.accessController = accessController;
        this.appCode = aclProperties.getAppName();
    }

    public User execute(RequestUser requestUser) {
        return getUserOrThrow(requestUser);
    }

    private User getUserOrThrow(RequestUser requestUser) {
        try {
            return getUser(requestUser);
        } catch (AclErrorException e) {
            log.error("Error retrieving user: {}", ExceptionUtils.getStackTrace(e));
            throw UserForbiddenException.createFromAclErrorException(e);
        }
    }

    private User getUser(RequestUser requestUser) throws AclErrorException {
        Map<String, Map<String, List<String>>> permissionsInApp = accessController.getPermissions(requestUser);

        List<String> appPermissions = getAppPermissions(permissionsInApp);

        return User.builder()
                .username(requestUser.getUsername())
                .canAccess(appPermissions.contains(Permissions.CAN_ACCESS))
                .canManageSetting(appPermissions.contains(Permissions.SETTING_MANAGE))
                .canAccessSetting(appPermissions.contains(Permissions.SETTING_ACCESS))
                .canAccessDataSource(appPermissions.contains(Permissions.DATASOURCE_ACCESS))
                .canAccessDocument(appPermissions.contains(Permissions.DOCUMENT_ACCESS))
                .canManageDataSource(appPermissions.contains(Permissions.DATASOURCE_MANAGE))
                .canManageQuery(appPermissions.contains(Permissions.QUERY_MANAGE))
                .canAccessQuery(appPermissions.contains(Permissions.QUERY_ACCESS))
                .canManageWebhooks(appPermissions.contains(Permissions.CAN_MANAGE_WEBHOOKS))
                .canAccessWebhooks(appPermissions.contains(Permissions.CAN_ACCESS_WEBHOOKS))
                .canManageScan(appPermissions.contains(Permissions.SCAN_MANAGE))
                .canAccessScan(appPermissions.contains(Permissions.SCAN_ACCESS))
                .canManageExecutionLog(appPermissions.contains(Permissions.EXECUTION_LOG_MANAGE))
                .canAccessExecutionLog(appPermissions.contains(Permissions.EXECUTION_LOG_ACCESS))
                .canManageCountry(appPermissions.contains(Permissions.COUNTRY_MANAGE))
                .canAccessCountry(appPermissions.contains(Permissions.COUNTRY_ACCESS))
                .canManageJobs(appPermissions.contains(Permissions.JOBS_MANAGE))
                .build();
    }

    private List<String> getAppPermissions(Map<String, Map<String, List<String>>> permissionsInApp) {
        if (permissionsInApp != null && permissionsInApp.containsKey(APPLICATION_KEY)) {
            Map<String, List<String>> applications = permissionsInApp.get(APPLICATION_KEY);
            if (applications.containsKey(appCode)) {
                return applications.get(appCode);
            }
        }
        return List.of();
    }


}
