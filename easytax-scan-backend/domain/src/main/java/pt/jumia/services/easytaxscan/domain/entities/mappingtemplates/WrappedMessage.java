package pt.jumia.services.easytaxscan.domain.entities.mappingtemplates;

import lombok.EqualsAndHashCode;
import pt.jumia.services.easytaxscan.domain.entities.Message;

/**
 * Contains tha business logic needed so that we can process {@link pt.jumia.services.frodo.domain.entities.Message} objects on templates
 */
@EqualsAndHashCode
public class WrappedMessage implements WrappedObject {

    private Message message;

    private WrappedMessage(Message message) {
        this.message = message;
    }

    public static WrappedMessage wrap(Message message) {
        return new WrappedMessage(message);
    }

    public static String fieldName() {
        return "message";
    }

    @Override
    public Object getObject() {
        return message;
    }

    @Override
    public String getObjectName() {
        return fieldName();
    }
}
