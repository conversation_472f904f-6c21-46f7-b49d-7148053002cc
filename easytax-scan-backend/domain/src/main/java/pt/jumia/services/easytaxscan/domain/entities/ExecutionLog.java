package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class ExecutionLog {


    @Nullable
    private Long id;

    private Long fkScan;

    private Scan scan;

    private Integer totalResults;

    private Status status;

    private Mode mode;

    private String exception;

    private Long durationMs;

    private Long lastRecordId;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;


    public enum Status {
        ACTIVE, INACTIVE, RUNNING, COMPLETED, FAILED
    }

    public enum SortingFields {
        ID, UPDATED_AT
    }
    public static ExecutionLog createExecutionlog(Scan scan) {
        return ExecutionLog.builder()
                .scan(scan)
                .mode(Mode.valueOf(scan.getMode().name()))
                .status(ExecutionLog.Status.RUNNING)
                .createdBy(AppConstants.SYSTEM)
                .updatedBy(AppConstants.SYSTEM)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .build();
    }

}
