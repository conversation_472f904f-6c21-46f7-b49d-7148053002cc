package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class Document {


    @Nullable
    private Long id;

    private ExecutionLog executionLog;

    private String sid;

    private Status status;

    private Mode mode;

    private String queryData;

    private String requestPayload;

    private String responsePayload;

    private String responseCode;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;

    public enum Status {
        ACTIVE, INACTIVE , FAILED, COMPLETED,
        CREATED, SUBMITTED, ERROR, SUCCESS
    }

    public enum SortingFields {
        ID, UPDATED_AT
    }
    public static Document createDocument(ExecutionLog executionLog, String sid, String queryDataJson) {
        return Document.builder()
                .sid(sid)
                .executionLog(executionLog)
                .queryData(queryDataJson)
                .mode(executionLog.getMode())
                .status(Document.Status.CREATED)
                .createdBy(AppConstants.SYSTEM)
                .updatedBy(AppConstants.SYSTEM)
                .requestPayload(null)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }
}
