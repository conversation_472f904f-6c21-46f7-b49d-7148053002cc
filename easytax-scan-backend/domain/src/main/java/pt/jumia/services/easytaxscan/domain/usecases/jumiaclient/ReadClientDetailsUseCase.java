package pt.jumia.services.easytaxscan.domain.usecases.jumiaclient;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.ClientBankDetails;
import pt.jumia.services.easytaxscan.domain.entities.ClientDetailsDTO;
import pt.jumia.services.easytaxscan.domain.repository.ClientBankDetailsRepository;
import pt.jumia.services.easytaxscan.domain.repository.ClientRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class ReadClientDetailsUseCase {

    private final ClientRepository clientRepository;
    private final ClientBankDetailsRepository bankDetailsRepository;


    public List<ClientDetailsDTO> getAllClientsWithDetails() {
        log.info("Fetching all clients with their details from all systems");

        return clientRepository.findAll().stream()
                .map(client -> {
                    String clientCode = client.getClientCode();

                    // Get all bank details for this client
                    List<ClientBankDetails> bankDetailsList = new ArrayList<>();
                    Optional<ClientBankDetails> optionalBankDetails = bankDetailsRepository.findByClientCode(clientCode);
                    optionalBankDetails.ifPresent(bankDetailsList::add);

                    // Map bank details to DTOs
                    List<ClientDetailsDTO.BankAccountDTO> bankAccountDTOs = bankDetailsList.stream()
                            .map(bd -> ClientDetailsDTO.BankAccountDTO.builder()
                                    .bankName(bd.getBankName())
                                    .accountNumber(bd.getAccountNumber())
                                    .accountHolderName(bd.getAccountHolderName())
                                    .branchName(bd.getBranchName())
                                    .ifscCode(bd.getIfscCode())
                                    .swiftCode(bd.getSwiftCode())
                                    .accountType(bd.getAccountType())
                                    .build())
                            .collect(Collectors.toList());

                    return ClientDetailsDTO.builder()
                            .clientCode(client.getClientCode())
                            .clientName(client.getClientName())
                            .address(client.getAddress())
                            .contactPerson(client.getContactPerson())
                            .phoneNumber(client.getPhoneNumber())
                            .email(client.getEmail())
                            .bankAccounts(bankAccountDTOs)
                            .build();
                })
                .collect(Collectors.toList());
    }
}
