package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class SubQuery {

    @Nullable
    private Long id;
    private Query query;
    private Query subQuery;
    private String mainQueryColumn;
    private String subQueryColumn;
    private Status status;
    private Boolean isList;

    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;

    public enum Status {
        ACTIVE, INACTIVE
    }

    public enum SortingFields {
        ID, UPDATED_AT
    }
}
