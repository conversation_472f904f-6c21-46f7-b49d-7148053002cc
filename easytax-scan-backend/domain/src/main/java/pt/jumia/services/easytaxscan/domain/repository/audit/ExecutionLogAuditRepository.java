package pt.jumia.services.easytaxscan.domain.repository.audit;

import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.ExecutionLogAuditFilters;

import java.util.List;

public interface ExecutionLogAuditRepository {
    List<AuditedEntity<ExecutionLog>> getExecutionLogAuditLogById(ExecutionLogAuditFilters filters, ExecutionLog executionLog);

    long getAuditLogCountById(ExecutionLogAuditFilters filters);
}
