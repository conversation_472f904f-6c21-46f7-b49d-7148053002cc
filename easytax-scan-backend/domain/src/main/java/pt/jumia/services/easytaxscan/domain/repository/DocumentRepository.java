package pt.jumia.services.easytaxscan.domain.repository;

import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentSortFilters;

import java.util.List;
import java.util.Optional;

public interface DocumentRepository {

    Document insert(Document document);

    List<Document> findAll(DocumentFilters filters,
                           DocumentSortFilters sortFilters,
                           PageFilters pageFilters);

    Optional<Document> findById(Long id);

    void deleteById(long id);

    Document findById(String sid);

    Document update(long id, Document updateDocument);

    Long executeCount(DocumentFilters filters);
}
