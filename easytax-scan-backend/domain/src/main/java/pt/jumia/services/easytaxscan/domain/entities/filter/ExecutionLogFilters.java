package pt.jumia.services.easytaxscan.domain.entities.filter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;

import java.time.LocalDateTime;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ExecutionLogFilters {

    private ExecutionLog.Status status;
    private Long scanId;
    private LocalDateTime createdTo;
    private LocalDateTime createdFrom;

}
