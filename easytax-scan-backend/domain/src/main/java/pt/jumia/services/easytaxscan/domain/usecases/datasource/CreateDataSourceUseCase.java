package pt.jumia.services.easytaxscan.domain.usecases.datasource;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.exceptions.RecordAlreadyExistsException;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceRepository;

import java.util.Objects;


@Component
@RequiredArgsConstructor
public class CreateDataSourceUseCase {

    private final DataSourceRepository dataSourceRepository;

    public DataSource execute(DataSource dataSource) throws RecordAlreadyExistsException {
        if (Objects.isNull(dataSource)) {
            throw new IllegalArgumentException("DataSource to save cannot be null");
        }
        dataSourceRepository.findByCode(dataSource.getCode())
                .ifPresent(p -> {
                    throw RecordAlreadyExistsException.build(DataSource.class, dataSource.getCode());
                });

        return dataSourceRepository.insert(dataSource);

    }

}
