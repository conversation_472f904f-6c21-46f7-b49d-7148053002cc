package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.OriginSystem;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

public interface FakeOriginSystems {

    String USER = "<EMAIL>";
    LocalDateTime DATE = LocalDateTime.now(ZoneOffset.UTC);

    OriginSystem ORIGIN_SYSTEM_1 = OriginSystem.builder()
            .name("RING")
            .createdBy(USER)
            .createdAt(DATE)
            .updatedBy(USER)
            .updatedAt(DATE)
            .build();

    OriginSystem ORIGIN_SYSTEM_2 = OriginSystem.builder()
            .name("SMAUG")
            .createdBy(USER)
            .createdAt(DATE)
            .updatedBy(USER)
            .updatedAt(DATE)
            .build();


    List<OriginSystem> ALL = List.of(ORIGIN_SYSTEM_1, ORIGIN_SYSTEM_2);
}
