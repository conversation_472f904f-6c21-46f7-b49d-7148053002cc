package pt.jumia.services.easytaxscan.domain.entities.monitoring;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@SuppressFBWarnings("EI_EXPOSE_REP2")
public class GroupCounterFactory {

    private static final String SHOP_COUNTERS_KEY = "shop";
    private static final String[] SHOPS = {"ng", "ci", "cm", "dz", "tn", "tz", "ug", "ma", "gh", "eg", "ke"};

    private final MeterRegistry registry;

    @Autowired
    public GroupCounterFactory(MeterRegistry registry) {
        this.registry = registry;
    }

    public GroupCounter createShopsCounters(String name, String description) {
        Map<String, List<Tag>> countersKeysTags = new HashMap<>();
        Arrays.asList(SHOPS).forEach(shopKey ->
                countersKeysTags.put(shopKey, Collections.singletonList(Tag.of(SHOP_COUNTERS_KEY, shopKey))));

        return new GroupCounter(name, description, countersKeysTags, registry);
    }
}
