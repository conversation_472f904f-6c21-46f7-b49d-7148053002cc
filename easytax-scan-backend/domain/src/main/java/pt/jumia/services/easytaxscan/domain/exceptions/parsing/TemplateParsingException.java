package pt.jumia.services.easytaxscan.domain.exceptions.parsing;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import pt.jumia.services.easytaxscan.domain.exceptions.ErrorCode;

import java.io.Serial;

/**
 * Exception thrown when we cannot find an entity
 */
@Getter
@EqualsAndHashCode(callSuper = true)
@ToString
public class TemplateParsingException extends ParsingException {

    @Serial
    private static final long serialVersionUID = 7529809051701426868L;

    private final String errorDetails;

    public TemplateParsingException(String message, String errorDetails, Exception exception) {
        super(ErrorCode.ERROR_PARSING_TEMPLATE, message, exception);
        this.errorDetails = errorDetails;
    }

}
