package pt.jumia.services.easytaxscan.domain.repository;

import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScanSortFilters;

import java.util.List;
import java.util.Optional;

/**
 * Contract between application and persistence layer for CRUD operation for {@link Scan}
 */
public interface ScanRepository {

    Scan insert(Scan scan);

    Scan findById(Long id);

    Optional<Scan> findByCode(String code);

    List<Scan> findAll(ScanFilters filters,
                       ScanSortFilters sortFilters,
                       PageFilters pageFilters);

    Scan update(long id, Scan updateScan);

    void deleteById(long id);

    Long executeCount(ScanFilters filters);
}
