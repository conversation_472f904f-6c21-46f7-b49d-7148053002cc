package pt.jumia.services.easytaxscan.domain.entities.filter.shared;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.filter.Filter;

import java.util.Map;

@Data
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
public abstract class SortFilters<E extends Enum<E>> implements Filter {


    protected E field;

    @Builder.Default
    private OrderDirection direction = OrderDirection.DESC;

    @Override
    public Map<String, ?> getAsMap() {
        return Map.ofEntries(
                Map.entry("orderField", field.name()),
                Map.entry("orderDirection", direction.name())
        );
    }
}
