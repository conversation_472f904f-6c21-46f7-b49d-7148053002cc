package pt.jumia.services.easytaxscan.domain.entities.filter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.Scan;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ScanFilters {

    private String text;

    private Scan.Status status;

    private Scan.Mode mode;

    private Long queryId;

    private Long countryId;


}
