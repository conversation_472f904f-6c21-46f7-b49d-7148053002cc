package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@AllArgsConstructor
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
public class Universe {

    private Integer id;

    private String name;

    private String code;

    private String createdBy;

    private LocalDateTime createdAt;

    private String updatedBy;

    private LocalDateTime updatedAt;

    public Universe withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .name(null)
                .code(null)
                .createdBy(null)
                .createdAt(null)
                .updatedBy(null)
                .updatedAt(null)
                .build();
    }

}
