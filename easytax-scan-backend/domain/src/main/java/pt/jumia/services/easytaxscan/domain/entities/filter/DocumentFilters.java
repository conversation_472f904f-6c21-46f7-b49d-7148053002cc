package pt.jumia.services.easytaxscan.domain.entities.filter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class DocumentFilters {

    private String sid;

    private Long executionLogId;

    private LocalDateTime createdTo;

    private LocalDateTime createdFrom;

    private String mode;

    private Long scanId;

    private List<String> statusList;


}
