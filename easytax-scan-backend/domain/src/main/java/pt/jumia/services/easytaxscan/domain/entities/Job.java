package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class Job {
    String jobName;
    String cronExpression;
    String state;
    String timezone;
    LocalDateTime lastFiredTime;
    LocalDateTime nextFireTime;


    public Job toEntity() {
        return Job
                .builder()
                .jobName(this.jobName)
                .cronExpression(this.cronExpression)
                .state(this.state)
                .timezone(this.timezone)
                .lastFiredTime(this.lastFiredTime)
                .nextFireTime(this.nextFireTime)
                .build();
    }
}
