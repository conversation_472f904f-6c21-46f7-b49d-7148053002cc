package pt.jumia.services.easytaxscan.domain.repository.audit;

import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.DocumentAuditFilters;

import java.util.List;

public interface DocumentAuditRepository {
    List<AuditedEntity<Document>> getDocumentAuditLogById(DocumentAuditFilters filters, Document document);

    long getAuditLogCountById(DocumentAuditFilters filters);
}
