package pt.jumia.services.easytaxscan.domain.entities.filter;

import lombok.Builder;
import lombok.Data;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.ScheduledExecution;

@Data
@Builder
public class ScheduledExecutionSortFilters {

    @Builder.Default
    private ScheduledExecution.SortingFields field = ScheduledExecution.SortingFields.STATUS;
    @Builder.Default
    private OrderDirection direction = OrderDirection.DESC;

}
