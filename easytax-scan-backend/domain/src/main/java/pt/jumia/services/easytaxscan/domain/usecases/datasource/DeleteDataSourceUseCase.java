package pt.jumia.services.easytaxscan.domain.usecases.datasource;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceRepository;


@Component
@RequiredArgsConstructor
public class DeleteDataSourceUseCase {

    private final DataSourceRepository dataSourceRepository;
    private final ReadDataSourceUseCase readDataSourceUseCase;

    public void execute(Long id) throws NotFoundException {
        readDataSourceUseCase.execute(id);
        dataSourceRepository.deleteById(id);
    }

}
