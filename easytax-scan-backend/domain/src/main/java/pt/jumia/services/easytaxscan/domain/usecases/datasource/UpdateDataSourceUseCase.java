package pt.jumia.services.easytaxscan.domain.usecases.datasource;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceRepository;

import java.util.Objects;


@Component
@RequiredArgsConstructor
public class UpdateDataSourceUseCase {

    private final DataSourceRepository dataSourceRepository;

    public DataSource execute(Long id, DataSource update) throws NotFoundException {
        if (Objects.isNull(update)) {
            throw new IllegalArgumentException("DataSource to update cannot be null");
        }

        return dataSourceRepository.update(id, update);

    }

}
