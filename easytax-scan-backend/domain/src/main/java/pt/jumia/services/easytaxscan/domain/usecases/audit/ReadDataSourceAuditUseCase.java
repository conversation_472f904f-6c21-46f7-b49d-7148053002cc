package pt.jumia.services.easytaxscan.domain.usecases.audit;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.DataSourceAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.DataSourceAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.datasource.ReadDataSourceUseCase;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ReadDataSourceAuditUseCase {
    private final DataSourceAuditRepository dataSourceAuditRepository;
    private final ReadDataSourceUseCase readDataSourceUseCase;

    public List<AuditedEntity<DataSource>> executeById(long dataSourceId, AuditedEntitySortFilters sortFilters, PageFilters pageFilters) {
        DataSource dataSource = readDataSourceUseCase.execute(dataSourceId);
        return dataSourceAuditRepository.getDataSourceAuditLogById(
                DataSourceAuditFilters.builder()
                        .id(dataSourceId)
                        .sortFilter(sortFilters)
                        .pageFilters(pageFilters)
                        .build(),
                dataSource
        );
    }

    public long executeCountByDataSourceId(long dataSourceId) {
        return dataSourceAuditRepository.getAuditLogCountById(
                DataSourceAuditFilters.builder()
                        .id(dataSourceId)
                        .build()
        );
    }
}
