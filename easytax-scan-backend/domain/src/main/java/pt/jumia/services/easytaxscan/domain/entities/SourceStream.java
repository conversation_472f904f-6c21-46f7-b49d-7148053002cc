package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class SourceStream {

    public enum SortingField {
        ID,
        NAME,
        CREATED_BY,
        CREATED_AT,
        UPDATED_BY,
        UPDATED_AT
    }

    private Integer id;

    private String name;

    private OriginSystem originSystem;

    private String payload;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;

    public SourceStream withoutDbFields() {
        return toBuilder()
                .id(null)
                .originSystem(originSystem.withoutDbFields())
                .createdAt(null)
                .updatedAt(null)
                .build();
    }

    public static void validate(SourceStream sourceStream) {
        if (sourceStream == null) {
            throw new IllegalArgumentException("Message payload cannot be null");
        }
        if (StringUtils.isEmpty(sourceStream.getName())) {
            throw new IllegalArgumentException("Message name cannot be null");
        }
        if (sourceStream.getOriginSystem() == null) {
            throw new IllegalArgumentException("Message system cannot be null");
        }
        if (StringUtils.isEmpty(sourceStream.getPayload())) {
            throw new IllegalArgumentException("Message payload data cannot be null or empty");
        }
    }

}
