package pt.jumia.services.easytaxscan.domain.utils;

import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.HashMap;
import java.util.Map;

@Component
public final class TemplateParsingUtils {

    private static final String CURRENT_DATE = "date";
    private Map<String, Template> templateCache = new HashMap<>();

    private TemplateParsingUtils() {
    }

    public String parse(String name, String stringTemplate, Map<String, Object> variables) throws TemplateException, IOException {
        return parse(name, stringTemplate, variables, 0);
    }

    public String parse(String name, String stringTemplate, Map<String, Object> variables, int depth)
            throws TemplateException, IOException {

        Template template = templateCache.get(name);

        if (template == null) {
            template = new Template(name, stringTemplate, null);
            templateCache.put(name, template);
        }

        //setup data model
        Map<String, Object> dataModel = new HashMap<>(variables);

        Writer out = new StringWriter();
        template.process(dataModel, out);
        return out.toString();
    }

}
