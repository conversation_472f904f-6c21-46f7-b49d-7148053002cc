package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.Message;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface FakeMessages {

    Map<String, Object> PAYLOAD = Stream.of(new Object[][]{
            {"Type", "PAYMENT"},
            {"Company", "Atol TN"},
            {"MessageID", "SCJUMIATN5016480P"},
            {"RespCenter", "JUMTN"},
            {"MessageType", "PAYOUT"},
            {"DocumentNo", "P-TN17NW0-********"},
            {"ExternalDocNo", "1"},
            {"SourceProvider", "BANKDEPOSIT"},
            {"VendorNo", "329385"},
            {"PostingDate", "03/02/2021"},
            {"DocumentDate", "03/02/2021"},
            {"AccountStatementNo", "TN17NW0-********"},
            {"StartDate", "02/15/2021"},
            {"EndDate", "02/21/2021"},
            {"PaidAmount", 1831.31},
    }).collect(Collectors.toMap(data -> (String) data[0], data -> data[1]));

    Message MESSAGE_1 = Message.builder()
            .id(1L)
            .request(FakeRequests.REQUEST_1.toBuilder().id(1L).build())
            .messageType(FakeMessageTypes.PAYMENT.toBuilder().id(1).build())
            .subType("subType")
            .sid("sid 1")
            .relatedEntity("relatedEntity 1")
            .status(Message.Status.PROCESSED)
            .company("company")
            .respCenter("respCenter")
            .payload(PAYLOAD)
            .shop(FakeShops.TEST_SHOP_KE)
            .numberOfResends(0L)
            .resent(false)
            .build();

    Message MESSAGE_2 = Message.builder()
            .id(2L)
            .request(FakeRequests.REQUEST_1)
            .messageType(FakeMessageTypes.PAYMENT)
            .subType("subType")
            .sid("sid 2")
            .relatedEntity("relatedEntity 2")
            .status(Message.Status.CREATED)
            .errorCode("errorCode")
            .errorDescription("errorDescription")
            .company("company")
            .respCenter("respCenter")
            .payload(PAYLOAD)
            .shop(FakeShops.TEST_SHOP_KE)
            .numberOfResends(0L)
            .resent(false)
            .build();

    List<Message> ALL = List.of(MESSAGE_1, MESSAGE_2);
}
