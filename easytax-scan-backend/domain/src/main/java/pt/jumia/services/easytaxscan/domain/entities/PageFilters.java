package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import pt.jumia.services.easytaxscan.domain.entities.filter.Filter;

import java.util.Map;

/**
 * The filters for a paginated api.
 */
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class PageFilters implements Filter {
    @Builder.Default
    private Integer page = 1;
    @Builder.Default
    private Integer size = 10;

    @Override
    public Map<String, ?> getAsMap() {
        return Map.ofEntries(
                Map.entry("page", page),
                Map.entry("size", size)
        );
    }
}
