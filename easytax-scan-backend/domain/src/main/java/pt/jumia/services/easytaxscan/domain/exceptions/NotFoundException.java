package pt.jumia.services.easytaxscan.domain.exceptions;

import java.io.Serial;
import java.text.MessageFormat;
import java.util.Map;

/**
 * Exception thrown when we cannot find an entity
 */
public class NotFoundException extends CodedException {
    @Serial
    private static final long serialVersionUID = 1024381488382429855L;

    private NotFoundException(String message) {

        super(ErrorCode.ENTITY_NOT_FOUND, message);
    }

    private NotFoundException(Exception exception) {

        super(ErrorCode.ENTITY_NOT_FOUND, exception);
    }


    public static NotFoundException createNotFound(Class clazz, long id) {

        return new NotFoundException(MessageFormat.format("{0} with id {1} not found", clazz.getName(), id));
    }

    public static NotFoundException build(Class clazz, long id) {

        return new NotFoundException(MessageFormat.format("{0} with id {1} not found", clazz.getSimpleName(), id));
    }

    public static NotFoundException build(Class clazz, String value) {

        return new NotFoundException(MessageFormat.format("{0} with value {1} not found", clazz.getSimpleName(), value));
    }

    public static NotFoundException build(Class clazz, String field, String value) {

        return new NotFoundException(MessageFormat.format("{0} with {1} {2} not found", clazz.getSimpleName(), field, value));
    }

    public static NotFoundException build(Class clazz, String field, long value) {

        return new NotFoundException(MessageFormat.format("{0} with {1} {2} not found", clazz.getSimpleName(), field, value));
    }

    public static NotFoundException build(Class clazz, Map<String, Object> keyValMap) {

        return new NotFoundException(MessageFormat.format("{0} with {1} not found", clazz.getSimpleName(), keyValMap));
    }

    public static NotFoundException build(Exception e) {

        return new NotFoundException(e);
    }

    public static NotFoundException build(Class clazz, Long id1, Long id2) {

        return new NotFoundException(MessageFormat.format("{0} with ids ({1},{2}) not found.", clazz.getSimpleName(), id1, id2));
    }

    public static NotFoundException build(String message) {

        return new NotFoundException(message);
    }

}
