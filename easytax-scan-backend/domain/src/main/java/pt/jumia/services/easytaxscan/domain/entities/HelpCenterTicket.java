package pt.jumia.services.easytaxscan.domain.entities;

import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Builder
@Data
public class HelpCenterTicket {
    private String link;
    private String justification;

    public HelpCenterTicket(String link, String justification) {
        this.link = link;
        this.justification = justification;
    }
    public boolean isValid() {
        return !StringUtils.isBlank(link) && !StringUtils.isBlank(justification);
    }
}
