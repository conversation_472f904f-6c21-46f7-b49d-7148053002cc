package pt.jumia.services.easytaxscan.domain.usecases.queries;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.QueryPreview;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;
import pt.jumia.services.easytaxscan.domain.properties.DatabaseProperties;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceFactory;
import pt.jumia.services.easytaxscan.domain.repository.EntityDataSourceRepository;
import pt.jumia.services.easytaxscan.domain.usecases.subqueries.ReadSubQueryUseCase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class ExecuteQueryUseCase {

    private final DataSourceFactory dataSourceFactory;
    private final DatabaseProperties databaseProperties;
    private final ReadSubQueryUseCase readSubQueryUseCase;


    public List<Map<String, Object>> executeStartingFromId(Query query, long startId) {


        return execute(query, false, startId, null);
    }

    public List<Map<String, Object>> execute(Query query, Long startId, List<String> sidList) {
        return execute(query, false, startId, sidList);
    }

    public List<Map<String, Object>> execute(Query query, boolean isSubQuery, Long startId, List<String> sidList) {
        log.info("Executing query: {}", query.getCode());
        Map<String, Object> args = new HashMap();
        args.put(Query.QUERY_ARG_PAGINATION_FIELD, startId);
        args.put(Query.QUERY_ARG_LIMIT, query.getPageSize());
        args.put(Query.QUERY_ARG_OFFSET, 0);
        if (!CollectionUtils.isEmpty(sidList)) {
            args.put(Query.QUERY_ARG_SID_LIST, sidList);
        }

        log.info("Executing query: {}. args: {}", query.getSql(), args);

        int page = 1;
        EntityDataSourceRepository datasourceAdapter = getDatasourceAdapter(query.getDataSource().getCode());
        List<Map<String, Object>> queryResult = datasourceAdapter.query(query.getSql(), args);

        if (isSubQuery && queryResult.size() == query.getPageSize()) {
            List<Map<String, Object>> subResult = new ArrayList<>();
            do {
                args.put(Query.QUERY_ARG_OFFSET, page * query.getPageSize());
                subResult = datasourceAdapter.query(query.getSql(), args);
                queryResult.addAll(subResult);
                page++;
            } while (subResult != null && subResult.size() == query.getPageSize());

        }

        if (queryResult != null && queryResult.isEmpty()) {
            log.info("No results found for query: {}", query.getCode());
            return null;
        }

        // find and execute subqueries
        List<SubQuery> subQueryList = readSubQueryUseCase.execute(query.getId(),
                SubQueryFilters.builder().build(), SubQuerySortFilters.builder().build(), PageFilters.builder().build());

        for (Map<String, Object> mainResult : queryResult) {

            subQueryList.forEach(subQuery -> {
                List<Map<String, Object>> subQueryResults = null;

                Object mainResultSidObj = mainResult.get(subQuery.getMainQueryColumn());

                if (mainResultSidObj == null) {
                    return;
                }

                String mainResultSid = mainResultSidObj.toString();
                try {
                    subQueryResults = execute(subQuery.getSubQuery(),
                            true,
                            startId,
                            List.of(mainResultSid));

                    Object result = subQuery.getIsList() ? subQueryResults : subQueryResults.getFirst();
                    mainResult.put(subQuery.getSubQueryColumn(), result);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
        return queryResult;
    }

    private EntityDataSourceRepository getDatasourceAdapter(String dataSourceCode) {
        return dataSourceFactory.getDatasourceAdapter(
                databaseProperties.getDataSources().get(dataSourceCode));
    }
}
