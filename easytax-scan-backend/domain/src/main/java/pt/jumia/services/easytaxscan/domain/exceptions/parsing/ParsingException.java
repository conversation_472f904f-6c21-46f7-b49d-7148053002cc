package pt.jumia.services.easytaxscan.domain.exceptions.parsing;

import lombok.EqualsAndHashCode;
import pt.jumia.services.easytaxscan.domain.exceptions.CodedException;
import pt.jumia.services.easytaxscan.domain.exceptions.ErrorCode;

import java.io.Serial;

/**
 * Base class for template parsing related exceptions
 */
@EqualsAndHashCode(callSuper = true)
public abstract class ParsingException extends CodedException {

    @Serial
    private static final long serialVersionUID = -2544737949714265930L;

    public ParsingException(ErrorCode errorCode, String message) {

        super(errorCode, message);
    }

    public ParsingException(ErrorCode errorCode, String message, Exception exception) {
        super(errorCode, message, exception);
    }
}
