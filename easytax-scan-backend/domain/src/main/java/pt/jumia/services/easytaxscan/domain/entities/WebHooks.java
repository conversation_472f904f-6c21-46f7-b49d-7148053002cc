package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class WebHooks {


    @Nullable
    private Long id;

    private String payload;

    private Document document;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private String createdBy;

    private String updatedBy;

    public enum SortingFields {
        ID, PAYLOAD, DOCUMENT, CREATED_AT, CREATED_BY, UPDATED_AT, UPDATED_BY
    }

    public enum Status {

    }

    public WebHooks withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .updatedAt(null)
                .updatedBy(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

}
