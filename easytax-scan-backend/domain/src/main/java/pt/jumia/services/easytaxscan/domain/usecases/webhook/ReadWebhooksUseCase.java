package pt.jumia.services.easytaxscan.domain.usecases.webhook;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.filter.WebhooksSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.WebhooksRepository;

import java.util.List;
import java.util.Optional;


@Component
@RequiredArgsConstructor
public class ReadWebhooksUseCase {

    private final WebhooksRepository webhooksRepository;

    public WebHooks findById(long id) {

        Optional<WebHooks> optionalWebHooks = webhooksRepository.findById(id);
        if (optionalWebHooks.isEmpty()) {
            throw NotFoundException.build(WebHooks.class, id);
        }
        return optionalWebHooks.get();
    }

    public List<WebHooks> findAll(WebhooksSortFilters webhooksSortFilters, PageFilters pageFilters) {
        return webhooksRepository.findAll(webhooksSortFilters, pageFilters);
    }

    public Long executeCount() {
        return webhooksRepository.executeCount();
    }
}
