package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class MessageType {

    public static final String MESSAGE_MAPPING = "MESSAGE_MAPPING";

    @Nullable
    private Integer id;

    private String code;

    private String name;

    private Enums.Status status;

    private String template;

    private String navPublishingUrl;

    private String navCreationUrl;

    private String navSubmissionUrl;

    private SoapAction soapAction;

    private String createdBy;

    private LocalDateTime createdAt;

    private String updatedBy;

    private LocalDateTime updatedAt;

    public MessageType withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdAt(null)
                .updatedAt(null)
                .build();
    }
}
