package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.OriginSystem;
import pt.jumia.services.easytaxscan.domain.entities.Request;

import java.util.List;

public interface FakeRequests {

    String ORIGINAL_PAYLOAD = "{\n" +
            "    \"type\":\"PAYMENT\",\n" +
            "    \"company\":\"Atol TN\",\n" +
            "    \"messageID\":\"SCJUMIATN5016480P\",\n" +
            "    \"respCenter\":\"JUMTN\",\n" +
            "    \"messageType\":\"PAYOUT\",\n" +
            "    \"documentNo\":\"P-TN17NW0-********\",\n" +
            "    \"externalDocNo\":\"1\",\n" +
            "    \"sourceProvider\":\"BANKDEPOSIT\",\n" +
            "    \"vendorNo\":\"329385\",\n" +
            "    \"postingDate\":\"03/02/2021\",\n" +
            "    \"documentDate\":\"03/02/2021\",\n" +
            "    \"accountStatementNo\":\"TN17NW0-********\",\n" +
            "    \"startDate\":\"02/15/2021\",\n" +
            "    \"endDate\":\"02/21/2021\",\n" +
            "    \"paidAmount\":1831.31,\n" +
            "    \"universe\":SELLER\n" +
            "}";

    Request REQUEST_1 = Request.builder()
            .originalPayload(ORIGINAL_PAYLOAD)
            .origin("RING")
            .originSystem(OriginSystem.builder().id(1).name("RING").build())
            .build();

    List<Request> ALL = List.of(REQUEST_1);
}
