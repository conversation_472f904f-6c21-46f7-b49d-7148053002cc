package pt.jumia.services.easytaxscan.domain.usecases.templates;


import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.util.HtmlUtils;
import pt.jumia.services.easytaxscan.domain.utils.SanitizationUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Sanitization use case to prevent malicious code execution.
 */
@Component
@RequiredArgsConstructor
public class SanitizeUseCase {
    @SuppressWarnings("unchecked")
    public <T> T execute(T obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof String) {
            return (T) sanitize((String) obj);
        } else if (obj instanceof Map) {
            return (T) sanitize((Map<String, Object>) obj);
        } else if (obj instanceof Set) {
            return (T) sanitize((Set<?>) obj);
        } else if (obj instanceof List) {
            return (T) sanitize((List<?>) obj);
        }
        return obj;
    }

    /**
     * String sanitization method (links whitelist, html, java and javascript).
     * <p>
     * If the given string is a link, checks if it is a whitelisted link, otherwise sanitizes as a JS
     * (JS sanitization affects links, so we cannot perform both at the same time).
     *
     * @param str
     * @return
     */
    private String sanitize(String str) {
        if (str == null) {
            return null;
        }
        String sanitizedString = str;
        sanitizedString = HtmlUtils.htmlEscape(sanitizedString);
        sanitizedString = SanitizationUtils.escapeJavaStr(sanitizedString);
        return sanitizedString;
    }

    /**
     * Sanitizes both the map keys and values.
     *
     * @param map
     * @return
     */
    private Map<String, Object> sanitize(Map<String, Object> map) {
        if (map == null) {
            return null;
        }

        Map<String, Object> sanitizedMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            sanitizedMap.put(sanitize(entry.getKey()), execute(entry.getValue()));
        }
        return sanitizedMap;
    }

    private List<?> sanitize(List<?> list) {
        return list.stream()
                .map(this::execute)
                .collect(Collectors.toList());
    }

    private Set<?> sanitize(Set<?> set) {
        return set.stream()
                .map(this::execute)
                .collect(Collectors.toSet());
    }
}
