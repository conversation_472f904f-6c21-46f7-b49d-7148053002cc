package pt.jumia.services.easytaxscan.domain.entities.filter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.Query;

import java.util.Map;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class QuerySortFilters implements Filter {

    @Builder.Default
    private Query.SortingFields field = Query.SortingFields.ID;
    @Builder.Default
    private OrderDirection direction = OrderDirection.ASC;

    @Override
    public Map<String, ?> getAsMap() {

        return Map.ofEntries(
                Map.entry("orderField", field.name()),
                Map.entry("orderDirection", direction.name())
        );
    }

}
