package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;

/**
 * Represents the auditing information of an entity.
 *
 * @param <E> the entity being audited.
 */
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@SuppressWarnings("PMD.AvoidFieldNameMatchingTypeName")
public class AuditedEntity<E> {

    private E entity;
    private RevisionInfo revisionInfo;
    private OperationType operationType;
    private AuditedEntities auditedEntity;

    /**
     * The revision info for the audited entity.
     */
    @Data
    @Builder(toBuilder = true)
    @AllArgsConstructor
    public static class RevisionInfo {
        private LocalDateTime datetime;
        @Nullable
        private String email;
        @Nullable
        private HelpCenterTicket helpCenterTicket;
    }

    /**
     * The operation types that can occur when auditing.
     */
    public enum OperationType {
        CREATE, UPDATE, DELETE
    }

    public enum SortingFields {
        REV, DATE, EMAIL
    }
}
