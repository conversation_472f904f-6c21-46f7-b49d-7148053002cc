package pt.jumia.services.easytaxscan.domain.usecases.setting;

import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.entities.filter.SettingFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SettingSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.SettingRepository;


@Component
@RequiredArgsConstructor
public class ReadSettingUseCase {

    private final SettingRepository settingRepository;

    public List<Setting> fetchAll() {

        return settingRepository.findAll();
    }

    public Setting findById(long id) {

        Optional<Setting> optSetting = settingRepository.findById(id);
        if (optSetting.isEmpty()) {
            throw NotFoundException.build(Setting.class, id);
        }
        return optSetting.get();
    }

    public List<Setting> execute(SettingFilters settingFilters,
        SettingSortFilters settingSortFilters) {

        return settingRepository.findAll(settingFilters, settingSortFilters);
    }

    public long executeCount(SettingFilters settingFilters) {

        return settingRepository.count(settingFilters);
    }

}
