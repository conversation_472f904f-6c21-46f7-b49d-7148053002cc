package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class ClientBankDetails {

    private Long bankDetailsId;

    private String clientCode;

    private String bankName;

    private String accountNumber;

    private String accountHolderName;

    private String branchName;

    private String ifscCode;

    private String swiftCode;

    private String accountType;

    private LocalDateTime dateAdded;
}
