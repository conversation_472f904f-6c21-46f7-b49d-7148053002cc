package pt.jumia.services.easytaxscan.domain.jobs;

import lombok.AllArgsConstructor;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Job;
import pt.jumia.services.easytaxscan.domain.entities.RetryJob;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.repository.JobsRepository;

@Component
@AllArgsConstructor
public class UpdateJobsUseCase {

    private JobsRepository jobsRepository;

    public Job updateScanJob(Scan scanSaved) throws SchedulerException {
        Job job = Job.builder().jobName(scanSaved.getCode())
                .cronExpression(scanSaved.getCronExpression()).build();
        return jobsRepository.updateJob(job);
    }

    public Job updateRetryJob(Retry<PERSON>ob retryJob) throws SchedulerException {
        Job job = Job.builder().jobName(retryJob.getJob())
                .cronExpression(retryJob.getCronExpression()).build();
        return jobsRepository.updateJob(job);
    }
}
