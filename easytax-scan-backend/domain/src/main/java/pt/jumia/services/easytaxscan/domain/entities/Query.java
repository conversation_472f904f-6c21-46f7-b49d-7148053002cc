package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class Query {

    public static final String QUERY_ARG_LIMIT = "limit";
    public static final String QUERY_ARG_OFFSET = "offset";
    public static final String QUERY_ARG_PAGINATION_FIELD = "paginationField";
    public static final String QUERY_ARG_SID_LIST = "sidList";

    @Nullable
    private Long id;

    private String sql;

    private String code;

    private String description;

    private Integer pageSize;

    private String paginationField;

    private String sampleResult;

    private DataSource dataSource;

    private List<SubQuery> subQueries;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;

    private Long queryId;

    public enum SortingFields {
        ID, UPDATED_AT
    }

    public Query withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .updatedBy(null)
                .updatedAt(null)
                .build();
    }

}
