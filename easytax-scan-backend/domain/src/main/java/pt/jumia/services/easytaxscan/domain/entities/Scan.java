package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;

/**
 * Business representation of Scan
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class Scan {

    @Nullable
    private Long id;

    private Query query;

    private String cronExpression;

    private String code;

    private String description;

    private Status status;

    private Mode mode;

    private String mapping;

    private String sidColumn;

    private Country country;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;

    public enum Status {
        ACTIVE, INACTIVE
    }

    public enum Mode {
        LIVE, DRYRUN
    }

    public enum SortingFields {
        ID, UPDATED_AT
    }

    public Scan withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .updatedBy(null)
                .updatedAt(null)
                .build();
    }

}
