package pt.jumia.services.easytaxscan.domain.usecases.audit;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.ExecutionLog;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.ExecutionLogAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.ExecutionLogAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.executionlogs.ReadExecutionLogUseCase;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ReadExecutionLogAuditUseCase {
    private final ExecutionLogAuditRepository executionLogAuditRepository;
    private final ReadExecutionLogUseCase readExecutionLogUseCase;

    public List<AuditedEntity<ExecutionLog>> executeById(long executionLogId, AuditedEntitySortFilters sortFilters, PageFilters pageFilters) {
        ExecutionLog executionLog = readExecutionLogUseCase.execute(executionLogId);
        return executionLogAuditRepository.getExecutionLogAuditLogById(
                ExecutionLogAuditFilters.builder()
                        .id(executionLogId)
                        .sortFilter(sortFilters)
                        .pageFilters(pageFilters)
                        .build(),
                executionLog
        );
    }

    public long executeCountByExecutionLogId(long executionLogId) {
        return executionLogAuditRepository.getAuditLogCountById(
                ExecutionLogAuditFilters.builder()
                        .id(executionLogId)
                        .build()
        );
    }
}
