package pt.jumia.services.easytaxscan.domain.usecases.setting;

import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.DataEventsNotificator;
import pt.jumia.services.easytaxscan.domain.RequestContext;
import pt.jumia.services.easytaxscan.domain.entities.Setting;
import pt.jumia.services.easytaxscan.domain.exceptions.DataIntegrityViolationException;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.SettingRepository;


@Component
@RequiredArgsConstructor
public class UpdateSettingUseCase {

    private final SettingRepository settingRepository;
    private final DataEventsNotificator dataEventsNotificator;

    public Setting execute(long id, Setting settingToUpdate) {

        Optional<Setting> optionalSetting = settingRepository.findById(id);
        if (optionalSetting.isEmpty()) {
            throw NotFoundException.build(Setting.class, id);
        }
        Setting settingWithUpdatedApplied = validateSettingFieldsAndGetUpdates(optionalSetting.get(), settingToUpdate);
        Setting updatedSetting;
        try {
            settingWithUpdatedApplied.setUpdatedBy(RequestContext.getUsername());
            updatedSetting = settingRepository.update(id, settingWithUpdatedApplied);
        } catch (DataIntegrityViolationException e) {
            throw new IllegalArgumentException("This override key has already been used for this property.");
        }

        this.dataEventsNotificator.notifySettingChanges();
        return updatedSetting;
    }

    private Setting validateSettingFieldsAndGetUpdates(Setting settingRead, Setting settingUpdates) {
        if (settingRead.isTypeOverride()) {
            return validateOverrideSettingFieldsAndGetUpdates(settingRead, settingUpdates);
        } else {
            return validateDefaultSettingFieldsAndGetUpdates(settingRead, settingUpdates);
        }
    }

    private Setting validateDefaultSettingFieldsAndGetUpdates(Setting settingRead, Setting settingUpdates) {
        Setting settingReadToCompare = settingRead.withoutDbFields().toBuilder()
                .value(null).description(null).build();
        Setting settingUpdatesToCompare = settingUpdates.withoutDbFields().toBuilder()
                .value(null).description(null).build();

        if (!settingReadToCompare.equals(settingUpdatesToCompare)) {
            throw new IllegalArgumentException("You can only update the value and description of a default setting.");
        }

        if (Objects.isNull(settingUpdates.getValue())) {
            throw new IllegalArgumentException("Setting values cannot be null.");
        }

        return settingRead.toBuilder()
                .value(settingUpdates.getValue())
                .description(settingUpdates.getDescription())
                .build();
    }

    private Setting validateOverrideSettingFieldsAndGetUpdates(Setting settingRead, Setting settingUpdates) {
        Setting settingReadToCompare = settingRead.withoutDbFields().toBuilder()
                .overrideKey(null).value(null).description(null).build();
        Setting settingUpdatesToCompare = settingUpdates.withoutDbFields().toBuilder()
                .overrideKey(null).value(null).description(null).build();

        if (!settingReadToCompare.equals(settingUpdatesToCompare)) {
            throw new IllegalArgumentException(
                    "You can only update the value, description and overrideKey of a override setting.");
        }

        if (Objects.isNull(settingUpdates.getValue())) {
            throw new IllegalArgumentException("Setting values cannot be null.");
        }

        if (settingUpdates.isOverrideKeyEmpty()) {
            throw new IllegalArgumentException("Override setting key cannot be empty.");
        }

        return settingRead.toBuilder()
                .overrideKey(settingUpdates.getOverrideKey())
                .value(settingUpdates.getValue())
                .description(settingUpdates.getDescription())
                .build();
    }
}
