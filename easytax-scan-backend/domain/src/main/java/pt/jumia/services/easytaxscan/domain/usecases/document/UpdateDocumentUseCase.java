package pt.jumia.services.easytaxscan.domain.usecases.document;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.repository.DocumentRepository;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;
import pt.jumia.services.easytaxscan.domain.utils.DateUtils;

@Component
@RequiredArgsConstructor
public class UpdateDocumentUseCase {

    private final DocumentRepository documentRepository;

    public void updateStatus(Document document, Document.Status status) {
        document.setStatus(status);
        document.setUpdatedAt(DateUtils.getCurrentTimeStamp());
        document.setUpdatedBy(AppConstants.SYSTEM);

    }

    public void update(long id, Document document) {
        documentRepository.update(id, document);
    }
}
