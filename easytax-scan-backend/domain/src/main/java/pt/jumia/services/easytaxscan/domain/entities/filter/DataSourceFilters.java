package pt.jumia.services.easytaxscan.domain.entities.filter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class DataSourceFilters {

    private String text;

    private DataSource.Status status;


}
