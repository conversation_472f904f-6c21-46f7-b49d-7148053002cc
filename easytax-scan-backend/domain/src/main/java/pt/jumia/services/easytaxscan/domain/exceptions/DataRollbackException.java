package pt.jumia.services.easytaxscan.domain.exceptions;

import java.io.Serial;

public class DataRollbackException extends CodedException {
    @Serial
    private static final long serialVersionUID = -3110050752235335849L;

    private DataRollbackException(Exception rootException) {

        super(ErrorCode.DATA_ROLLBACK, rootException);
    }

    public static DataRollbackException build(Exception rootException) {

        return new DataRollbackException(rootException);
    }

}
