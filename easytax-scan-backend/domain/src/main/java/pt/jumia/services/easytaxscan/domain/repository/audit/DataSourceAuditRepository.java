package pt.jumia.services.easytaxscan.domain.repository.audit;

import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.DataSourceAuditFilters;

import java.util.List;

public interface DataSourceAuditRepository {
    List<AuditedEntity<DataSource>> getDataSourceAuditLogById(DataSourceAuditFilters filters, DataSource dataSource);

    long getAuditLogCountById(DataSourceAuditFilters filters);
}
