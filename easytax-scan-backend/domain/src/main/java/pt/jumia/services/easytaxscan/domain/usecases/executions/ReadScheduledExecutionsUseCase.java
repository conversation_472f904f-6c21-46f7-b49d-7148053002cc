package pt.jumia.services.easytaxscan.domain.usecases.executions;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.ScheduledExecution;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScheduledExecutionFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScheduledExecutionSortFilters;
import pt.jumia.services.easytaxscan.domain.repository.ScheduledExecutionsRepository;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ReadScheduledExecutionsUseCase {

    private final ScheduledExecutionsRepository scheduledExecutionsRepository;

    public List<ScheduledExecution> execute(
            ScheduledExecutionFilters filters,
            ScheduledExecutionSortFilters sortFilters) {

        return scheduledExecutionsRepository.findAll(filters, sortFilters);
    }
}
