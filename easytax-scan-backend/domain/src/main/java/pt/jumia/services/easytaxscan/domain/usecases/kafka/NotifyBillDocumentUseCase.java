package pt.jumia.services.easytaxscan.domain.usecases.kafka;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.BillRequester;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.bill.RequestResult;
import pt.jumia.services.easytaxscan.domain.entities.events.Events;
import pt.jumia.services.easytaxscan.domain.entities.kafka.ScanDocumentPayload;
import pt.jumia.services.easytaxscan.domain.settings.OverallSettings;
import pt.jumia.services.easytaxscan.domain.usecases.document.UpdateDocumentUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.scan.ReadScanUseCase;
import pt.jumia.services.easytaxscan.domain.utils.KafkaProducer;

import java.util.Objects;

@Component
@RequiredArgsConstructor
@Slf4j
public class NotifyBillDocumentUseCase {

    //private final KafkaProducer<ScanDocumentPayload> kafkaProducer;
    private final ReadScanUseCase readScanUseCase;
    private final OverallSettings overallSettings;
    private final BillRequester billRequester;
    private final UpdateDocumentUseCase updateDocumentUseCase;

    public void execute(String scanCode, Document document) {
        if (Objects.isNull(document)) {
            throw new IllegalArgumentException("Document cannot be null");
        }
        if (Document.Status.SUBMITTED.equals(document.getStatus())) {
            log.info("Document already submitted, skipping: {}", document);
            return;
        }
        try {
            if (overallSettings.getDocumentProducerSettings().getKafkaDocumentProducerEnabled()) {
                Scan scan = readScanUseCase.findByCode(scanCode);
                ScanDocumentPayload scanDocumentPayload = ScanDocumentPayload.builder()
                        .document(document).scanId(scan.getId()).event(Events.SCAN_DOCUMENT.name()).build();
                //kafkaProducer.sendMessage(scanDocumentPayload);
            } else {
                log.info("Sending document to Bill: {}", document);
                RequestResult result = billRequester.sendDocumentToBill(document.getRequestPayload(),  document);

                document.setResponsePayload(result.getErrorDescription());
                document.setResponseCode(result.getErrorCode());
                document.setStatus(Document.Status.ERROR);

            }
        } catch (Exception e) {
            log.error("Error processing document: {}", ExceptionUtils.getStackTrace(e));
            document.setResponsePayload(ExceptionUtils.getStackTrace(e));
            document.setStatus(Document.Status.ERROR);
        }
        updateDocumentUseCase.update(document.getId(), document);
    }
}

