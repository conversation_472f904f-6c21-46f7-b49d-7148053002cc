package pt.jumia.services.easytaxscan.domain;

import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.easytaxscan.domain.entities.HelpCenterTicket;

/**
 * The context of the current request. The data is stored at the thread level, so we can access this information
 * at any layer.InheritableThreadLocal
 */
public class RequestContext {

    private static final InheritableThreadLocal<RequestUser> USER_THREAD_LOCAL = new InheritableThreadLocal<>();

    private static final InheritableThreadLocal<Boolean> SKIP_USER_VALIDATION = new InheritableThreadLocal<>();


    public static RequestUser getUser() {
        return USER_THREAD_LOCAL.get();
    }

    private static final InheritableThreadLocal<HelpCenterTicket> HELP_CENTER_TICKET = new InheritableThreadLocal<>() {
        @Override
        protected HelpCenterTicket initialValue() {
            return null;
        }
    };
    public static String getUsername() {
        RequestUser requestUser = USER_THREAD_LOCAL.get();
        return requestUser == null ? null : requestUser.getUsername();
    }
    public static void setUser(RequestUser user) {
        SKIP_USER_VALIDATION.set(false);
        USER_THREAD_LOCAL.set(user);
    }
    public static HelpCenterTicket getHelpCenterTicket() {
        return HELP_CENTER_TICKET.get();
    }

    public static void clear() {
        USER_THREAD_LOCAL.remove();
    }

    public static Boolean isValidationSkipped() {
        return SKIP_USER_VALIDATION.get();
    }

}
