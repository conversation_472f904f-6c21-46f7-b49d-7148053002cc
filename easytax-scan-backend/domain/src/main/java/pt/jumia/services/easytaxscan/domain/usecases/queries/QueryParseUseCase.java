package pt.jumia.services.easytaxscan.domain.usecases.queries;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.QueryParse;
import pt.jumia.services.easytaxscan.domain.utils.SqlQueryParserUtil;

import java.util.List;

@Component
@RequiredArgsConstructor
public class QueryParseUseCase {

    public QueryParse execute(QueryParse queryParse) throws Exception {
        List<String> columns = SqlQueryParserUtil.extractColumns(queryParse.getQuery());
        List<String> arguments = SqlQueryParserUtil.extractPlaceholders(queryParse.getQuery());
        return QueryParse.builder().columns(columns)
                .arguments(arguments).build();
    }
}
