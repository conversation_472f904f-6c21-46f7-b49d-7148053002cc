package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Some fake data for {@link pt.jumia.services.easytaxscan.domain.entities.SubQuery}, to be used in tests and fill in the MEM DB.
 */
public interface FakeSubQuery {

    SubQuery SUB_QUERY_CREATE = createSubQueryRequest(1L, "column1", "sub_column1", SubQuery.Status.ACTIVE, false);
    SubQuery SUB_QUERY_UPDATE = createSubQueryRequest(2L, "column2", "sub_column2", SubQuery.Status.INACTIVE, false);

    static SubQuery createSubQueryRequest(Long id, String mainQueryColumn, String subQueryColumn,
                                          SubQuery.Status status, boolean isList) {
        return SubQuery.builder()
                .id(id)
                .mainQueryColumn(mainQueryColumn)
                .subQueryColumn(subQueryColumn)
                .status(status)
                .isList(isList) // ✅ Added isList field
                .createdBy(AppConstants.SYSTEM)
                .createdAt(LocalDateTime.now())
                .updatedBy(AppConstants.SYSTEM)
                .updatedAt(LocalDateTime.now())
                .query(FakeQuery.QUERY_CREATE)
                .subQuery(FakeQuery.QUERY_CREATE)
                .build();
    }

    List<SubQuery> ALL =
            Collections.unmodifiableList(new ArrayList<>() {{
                add(SUB_QUERY_CREATE);
                add(SUB_QUERY_UPDATE);
            }});
}
