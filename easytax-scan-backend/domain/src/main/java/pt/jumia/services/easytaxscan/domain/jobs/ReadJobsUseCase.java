package pt.jumia.services.easytaxscan.domain.jobs;

import lombok.AllArgsConstructor;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Job;
import pt.jumia.services.easytaxscan.domain.repository.JobsRepository;

import java.util.List;

@Component
@AllArgsConstructor
public class ReadJobsUseCase {
    private JobsRepository jobsRepository;

    public List<Job> fetchAllJobs() throws SchedulerException {

        return jobsRepository.findAll();
    }

}
