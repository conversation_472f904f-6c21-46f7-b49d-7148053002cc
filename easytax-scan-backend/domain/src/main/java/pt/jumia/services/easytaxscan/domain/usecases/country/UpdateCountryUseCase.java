package pt.jumia.services.easytaxscan.domain.usecases.country;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.CountryRepository;

import java.util.Objects;

@Component
@RequiredArgsConstructor
public class UpdateCountryUseCase {

    private final CountryRepository countryRepository;

    public Country execute(Long id, Country update) throws NotFoundException {
        if (Objects.isNull(update)) {
            throw new IllegalArgumentException("Country to update cannot be null");
        }

        return countryRepository.update(id, update);
    }
}
