package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.Shop;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

public interface FakeShops {

    Shop TEST_SHOP_ZA = Shop.builder()
            .id(1L)
            .name("Jumia ZA")
            .countryCode("ZA")
            .shopCode("JUMIA")
            .createdBy("<EMAIL>")
            .createdAt(LocalDateTime.now(ZoneOffset.UTC))
            .updatedBy("<EMAIL>")
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
            .timeZone("Africa/Johannesburg")
            .build();

    Shop TEST_SHOP_KE = Shop.builder()
            .id(2L)
            .name("TEST KE")
            .countryCode("KE")
            .shopCode("JUMIA")
            .createdBy("<EMAIL>")
            .createdAt(LocalDateTime.now(ZoneOffset.UTC))
            .updatedBy("<EMAIL>")
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
            .timeZone("Africa/Nairobi")
            .build();

    Shop TEST_SHOP_UG = Shop.builder()
            .name("TEST UG")
            .countryCode("UG")
            .shopCode("JUMIA")
            .createdBy("<EMAIL>")
            .createdAt(LocalDateTime.now(ZoneOffset.UTC))
            .updatedBy("<EMAIL>")
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
            .timeZone("Africa/Nairobi")
            .build();

    List<Shop> ALL = List.of(TEST_SHOP_ZA, TEST_SHOP_KE, TEST_SHOP_UG);
}
