package pt.jumia.services.easytaxscan.domain.repository;

import org.quartz.SchedulerException;
import pt.jumia.services.easytaxscan.domain.entities.Job;

import java.util.List;

public interface JobsRepository {

    List<Job> findAll() throws SchedulerException;

    void pauseAllJobs() throws SchedulerException;

    void resumeAllJobs() throws SchedulerException;

    Job createJob(Job job, String jobCategory) throws SchedulerException;

    Job updateJob(Job job) throws SchedulerException;

    void deleteJob(Job job) throws SchedulerException;
}
