package pt.jumia.services.easytaxscan.domain.templates.freemarker;

import com.newrelic.api.agent.Trace;
import freemarker.core.ParseException;
import freemarker.core.TemplateClassResolver;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.caches.TemplatesCache;
import pt.jumia.services.easytaxscan.domain.entities.mappingtemplates.WrappedObject;
import pt.jumia.services.easytaxscan.domain.exceptions.parsing.TemplateCreationException;
import pt.jumia.services.easytaxscan.domain.exceptions.parsing.TemplateParsingException;
import pt.jumia.services.easytaxscan.domain.templates.TemplateParser;
import pt.jumia.services.easytaxscan.domain.templates.TemplateParsingHelper;
import pt.jumia.services.easytaxscan.domain.templates.utils.CharacterOverrideTemplateUtils;
import pt.jumia.services.easytaxscan.domain.templates.utils.DateTemplateUtils;
import pt.jumia.services.easytaxscan.domain.templates.utils.NumberTemplateUtils;
import pt.jumia.services.easytaxscan.domain.templates.utils.StringTemplateUtils;
import pt.jumia.services.easytaxscan.domain.usecases.templates.SanitizeUseCase;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class FreeMarkerParser implements TemplateParser {

    public static final Configuration FREEMARKER_CONFIGURATION = new Configuration(Configuration.VERSION_2_3_30);

    static {
        FREEMARKER_CONFIGURATION.setNewBuiltinClassResolver(TemplateClassResolver.ALLOWS_NOTHING_RESOLVER);
    }

    private static final String CONTEXT_KEY = "context";

    private final SanitizeUseCase sanitizeUseCase;

    private final TemplatesCache templatesCache;

    @Trace
    public String execute(
            String name, String stringTemplate, Map<String, Object> variables, WrappedObject wrappedObject)
            throws TemplateCreationException, TemplateParsingException {

        return processTemplate(
                name, stringTemplate,
                sanitizeUseCase.execute(variables),
                wrappedObject,
                createTemplate(name, stringTemplate));
    }

    private String processTemplate(
            String name, String stringTemplate, Map<String, Object> variables, WrappedObject wrappedObject, Template freeMarkerTemplate) {

        Map<String, Object> dataModel = new HashMap<>(TemplateParsingHelper.convertWrappedObject(wrappedObject));
        dataModel.put(CONTEXT_KEY, new HashMap<>(variables));
        dataModel.put(DateTemplateUtils.NAME, new DateTemplateUtils());
        dataModel.put(NumberTemplateUtils.NAME, new NumberTemplateUtils());
        dataModel.put(StringTemplateUtils.NAME, new StringTemplateUtils());
        dataModel.put(CharacterOverrideTemplateUtils.NAME, new CharacterOverrideTemplateUtils());

        Writer out = new StringWriter();
        try {
            freeMarkerTemplate.process(dataModel, out);
        } catch (TemplateException e) {
            throw new TemplateParsingException(String.format("Error processing template for %s %s",
                    name, stringTemplate), e.getMessage(), e);
        } catch (IOException e) {
            throw new TemplateCreationException(String.format("Error processing template for %s %s", name,
                    stringTemplate), e);
        }
        return out.toString();
    }

    private Template createTemplate(String name, String stringTemplate) {
        Template freeMarkerTemplate;
        try {
            freeMarkerTemplate = templatesCache.getTemplate(name, stringTemplate);
        } catch (ParseException e) {
            throw new TemplateParsingException(
                    String.format("Error processing template for %s", stringTemplate), e.getEditorMessage(), e);
        } catch (IOException e) {
            throw new TemplateCreationException(
                    String.format("Error creating freemarker template for %s", stringTemplate), e);
        }
        return freeMarkerTemplate;
    }

}
