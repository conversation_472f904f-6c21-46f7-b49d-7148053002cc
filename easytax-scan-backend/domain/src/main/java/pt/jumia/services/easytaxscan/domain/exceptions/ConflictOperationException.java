package pt.jumia.services.easytaxscan.domain.exceptions;


import java.io.Serial;

public class ConflictOperationException extends CodedException {
    @Serial
    private static final long serialVersionUID = 5326845872461410500L;

    public static ConflictOperationException build(String message) {

        return new ConflictOperationException(ErrorCode.CONFLICT, message);
    }

    protected ConflictOperationException(ErrorCode errorCode, String message) {

        super(errorCode, message);
    }

}
