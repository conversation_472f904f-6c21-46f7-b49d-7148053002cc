package pt.jumia.services.easytaxscan.domain;


public final class Permissions {

    public static final String CAN_ACCESS = "easytax-scan_can_access";
    public static final String SETTING_MANAGE = "easytax-scan_can_manage_setting";
    public static final String SETTING_ACCESS = "easytax-scan_can_access_setting";
    public static final String DATASOURCE_MANAGE = "easytax-scan_can_manage_datasources";
    public static final String DATASOURCE_ACCESS = "easytax-scan_can_access_datasources";
    public static final String SCAN_MANAGE = "easytax-scan_can_manage_scan";
    public static final String SCAN_ACCESS = "easytax-scan_can_access_scan";
    public static final String QUERY_MANAGE = "easytax-scan_can_manage_queries";
    public static final String QUERY_ACCESS = "easytax-scan_can_access";
    public static final String CAN_MANAGE_WEBHOOKS = "easytax-scan_can_manage_webhooks";
    public static final String CAN_ACCESS_WEBHOOKS = "easytax-scan_can_access_webhooks";
    public static final String EXECUTION_LOG_MANAGE = "easytax-scan_can_manage_execution_logs";
    public static final String JOBS_MANAGE = "easytax-scan_can_manage_jobs";
    public static final String EXECUTION_LOG_ACCESS = "easytax-scan_can_access_execution_logs";
    public static final String DOCUMENT_ACCESS = "easytax-scan_can_access_document";
    public static final String COUNTRY_ACCESS = "easytax-scan_can_access_countries";
    public static final String COUNTRY_MANAGE = "easytax-scan_can_manage_countries";

}
