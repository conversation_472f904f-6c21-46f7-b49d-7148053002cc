package pt.jumia.services.easytaxscan.domain;

import java.util.List;
import java.util.Map;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.acl.lib.payloads.AclLoginRequest;
import pt.jumia.services.acl.lib.payloads.AclTokenResponse;

public interface AccessController {

    Map<String, Map<String, List<String>>> getPermissions(RequestUser requestUser);

    List<String> getPermissionTargets(RequestUser requestUser, String permissionCode);

    boolean hasPermission(RequestUser requestUser, String permission);

    RequestUser decodeToken(String userToken);

    AclTokenResponse login(String providerName, AclLoginRequest requestPayload);

    void logout(RequestUser requestUser);

    RequestUser authorize(String username, String password);

    String findRealToken(String temporaryToken);
}
