package pt.jumia.services.easytaxscan.domain.usecases.scan;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.exceptions.RecordAlreadyExistsException;
import pt.jumia.services.easytaxscan.domain.jobs.CreateJobsUseCase;
import pt.jumia.services.easytaxscan.domain.repository.ScanRepository;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ReadQueryUseCase;

import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateScanUseCase {

    private final ScanRepository scanRepository;
    private final ReadQueryUseCase readQueryUseCase;
    private final ScanCountryValidationUseCase scanCountryValidationUseCase;
    private final CreateJobsUseCase createJobsUseCase;

    public Scan execute(Scan scan) throws RecordAlreadyExistsException {
        if (Objects.isNull(scan)) {
            throw new IllegalArgumentException("Scan to save cannot be null");
        }
        scanRepository.findByCode(scan.getCode())
                .ifPresent(p -> {
                    throw RecordAlreadyExistsException.build(Scan.class, scan.getCode());
                });

        Query query = readQueryUseCase.findById(scan.getQuery().getId());
        scan.setQuery(query);

        scanCountryValidationUseCase.validateScanCountry(scan);
        Scan scanSaved = scanRepository.insert(scan);
        try {
            createJobsUseCase.createScanJob(scanSaved);
        } catch (SchedulerException e) {
            log.error("Exception occurred while creating Job:{}", e.getMessage());
        }
        return scanSaved;
    }
}
