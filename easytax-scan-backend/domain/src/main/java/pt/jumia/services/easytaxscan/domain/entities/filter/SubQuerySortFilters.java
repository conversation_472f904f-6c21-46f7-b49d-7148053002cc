package pt.jumia.services.easytaxscan.domain.entities.filter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import pt.jumia.services.acl.lib.payloads.client.commons.OrderDirection;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;

import java.util.Map;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class SubQuerySortFilters implements Filter {

    @Builder.Default
    private SubQuery.SortingFields field = SubQuery.SortingFields.ID;
    @Builder.Default
    private OrderDirection direction = OrderDirection.ASC;

    @Override
    public Map<String, ?> getAsMap() {

        return Map.ofEntries(
                Map.entry("orderField", field.name()),
                Map.entry("orderDirection", direction.name())
        );
    }

}
