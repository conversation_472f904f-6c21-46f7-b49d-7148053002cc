package pt.jumia.services.easytaxscan.domain.usecases.queries;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.StreamSupport;

@Component
@RequiredArgsConstructor
public class JsonExtractorUseCase {

    private final ObjectMapper objectMapper;

    /**
     * Analyze a JSON string and extract all keys with "context." prefix
     * @param jsonString The JSON string to analyze
     * @return A list of keys in the format "context.path.to.key"
     */
    public List<String> analyzeJson(String jsonString) {
        try {
            JsonNode rootNode = objectMapper.readTree(jsonString);
            Set<String> allKeys = new HashSet<>();
            extractAllKeys(rootNode, "", allKeys);

            // Add prefix and sort using streams
            return allKeys.stream()
                    .map(key -> "context." + key)
                    .sorted()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("Error analyzing JSON", e);
        }
    }

    private void extractAllKeys(JsonNode node, String currentPath, Set<String> result) {
        Optional.ofNullable(node).ifPresent(jsonNode -> {
            if (!currentPath.isEmpty()) {
                result.add(currentPath);
            }

            if (jsonNode.isObject()) {
                StreamSupport.stream(
                                Spliterators.spliteratorUnknownSize(
                                        jsonNode.fields(),
                                        Spliterator.ORDERED),
                                false)
                        .forEach(entry -> {
                            String key = entry.getKey();
                            String path = currentPath.isEmpty() ? key : currentPath + "." + key;

                            result.add(path);
                            extractAllKeys(entry.getValue(), path, result);
                        });
            } else if (jsonNode.isArray()) {
                IntStream.range(0, jsonNode.size())
                        .forEach(i -> {
                            String arrayPath = currentPath + "[" + i + "]";
                            JsonNode arrayElement = jsonNode.get(i);

                            result.add(arrayPath);
                            extractAllKeys(arrayElement, arrayPath, result);
                        });
            }
        });
    }

    public String getKeysAsJsonArray(List<String> keys) {
        return Optional.ofNullable(keys)
                .map(keyList -> {
                    try {
                        return objectMapper.writeValueAsString(keyList);
                    } catch (Exception e) {
                        throw new RuntimeException("Error converting keys to JSON", e);
                    }
                })
                .orElse("[]");
    }
}

