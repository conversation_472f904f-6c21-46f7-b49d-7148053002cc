package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.Enums;
import pt.jumia.services.easytaxscan.domain.entities.MessageType;
import pt.jumia.services.easytaxscan.domain.entities.SoapAction;

import java.util.List;

public interface FakeMessageTypes {

    String USER = "<EMAIL>";

    MessageType PAYMENT = MessageType.builder()
            .name("Payout Payment")
            .code("PAYMENT")
            .status(Enums.Status.ACTIVE)
            .template("payout xml template")
            .navPublishingUrl("payout nav url")
            .soapAction(SoapAction.CREATE)
            .createdBy(USER)
            .updatedBy(USER)
            .build();

    MessageType MPLTRANS = MessageType.builder()
            .name("MPL Transaction")
            .code("MPLTRANS")
            .status(Enums.Status.ACTIVE)
            .soapAction(SoapAction.CREATE)
            .template("mpl trans xml template")
            .navPublishingUrl("mpl trans nav url")
            .createdBy(USER)
            .updatedBy(USER)
            .build();

    MessageType MPLACC = MessageType.builder()
            .name("Account Statement")
            .code("MPLACC")
            .status(Enums.Status.ACTIVE)
            .soapAction(SoapAction.CREATE)
            .template("account statement xml template")
            .navPublishingUrl("account statement nav url")
            .createdBy(USER)
            .updatedBy(USER)
            .build();

    MessageType PURCHORDER = MessageType.builder()
            .name("Purchase Order")
            .code("PURCHORDER")
            .status(Enums.Status.ACTIVE)
            .soapAction(SoapAction.CREATE)
            .template("purchase order xml template")
            .navPublishingUrl("purchase order nav url")
            .createdBy(USER)
            .updatedBy(USER)
            .build();

    MessageType TRANSFERORDER = MessageType.builder()
            .name("Transfer Order")
            .code("TRANSFERORDER")
            .status(Enums.Status.ACTIVE)
            .soapAction(SoapAction.CREATE)
            .template("transfer order xml template")
            .navPublishingUrl("transfer order nav url")
            .createdBy(USER)
            .updatedBy(USER)
            .build();


    List<MessageType> ALL = List.of(PAYMENT, MPLTRANS, MPLACC, PURCHORDER, TRANSFERORDER);
}
