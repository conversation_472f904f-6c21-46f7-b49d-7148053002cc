package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class Request {

    @Nullable
    private Long id;

    private String originalPayload;

    private String origin;

    @Nullable
    private OriginSystem originSystem;

    private LocalDateTime createdAt;

    public Request withoutDbFields() {
        return toBuilder()
                .id(0L)
                .createdAt(null)
                .originSystem(originSystem == null ? null : originSystem.withoutDbFields())
                .build();
    }
}
