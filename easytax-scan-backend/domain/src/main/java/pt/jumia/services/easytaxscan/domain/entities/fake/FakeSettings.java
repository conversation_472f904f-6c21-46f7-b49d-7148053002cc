package pt.jumia.services.easytaxscan.domain.entities.fake;

import java.util.List;
import pt.jumia.services.easytaxscan.domain.entities.Setting;

public interface FakeSettings {

    Setting SETTING_DEFAULT = Setting.builder()
        .property("dummy.setting.is-dummy")
        .type(Setting.Type.DEFAULT)
        .value("false")
        .description("Dummy test")
        .build();

    List<Setting> ALL_DEFAULT = List.of(
        SETTING_DEFAULT
    );

    List<Setting> ALL = List.of(
        SETTING_DEFAULT
    );

}
