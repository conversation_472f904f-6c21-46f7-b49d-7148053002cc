package pt.jumia.services.easytaxscan.domain.usecases.audit;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.WebHooks;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.WebhookAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.WebhookAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.webhook.ReadWebhooksUseCase;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ReadWebhookAuditUseCase {
    private final WebhookAuditRepository webhookAuditRepository;
    private final ReadWebhooksUseCase readWebhooksUseCase;

    public List<AuditedEntity<WebHooks>> executeById(long webhookId, AuditedEntitySortFilters sortFilters, PageFilters pageFilters) {

        WebHooks webHooks = readWebhooksUseCase.findById(webhookId);

        return webhookAuditRepository.getWebhookAuditLogById(WebhookAuditFilters.builder()
                .id(webhookId)
                .sortFilter(sortFilters)
                .pageFilters(pageFilters)
                .build(), webHooks);
    }

    public long executeCountByWebhookID(long documentId) {
        return webhookAuditRepository.getAuditLogCountById(WebhookAuditFilters.builder()
                .id(documentId)
                .build());
    }
}
