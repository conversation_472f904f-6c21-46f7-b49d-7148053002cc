package pt.jumia.services.easytaxscan.domain.exceptions;

import lombok.Getter;

import java.io.Serial;

@Getter
public class InvalidSettingException extends CodedException {
    @Serial
    private static final long serialVersionUID = 4890832690320514951L;

    private InvalidSettingException(String message) {

        super(ErrorCode.INVALID_SETTING, message);
    }

    public static InvalidSettingException build(String message) {

        return new InvalidSettingException(message);
    }

}
