package pt.jumia.services.easytaxscan.domain.jobs;

import lombok.AllArgsConstructor;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.repository.JobsRepository;

@Component
@AllArgsConstructor
public class ResumeJobsUseCase {
    private JobsRepository jobsRepository;

    public void resumeAllJobs() throws SchedulerException {
        jobsRepository.resumeAllJobs();
    }
}
