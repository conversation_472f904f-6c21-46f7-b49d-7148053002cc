package pt.jumia.services.easytaxscan.domain.utils;


import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.RetryListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EasyTaxRetryListener implements RetryListener {

    @Override
    public <T, E extends Throwable> boolean open(RetryContext context, RetryCallback<T, E> callback) {
        int attemptNumber = context.getRetryCount() + 1;
        return true;
    }

    @Override
    public <T, E extends Throwable> void close(RetryContext context,
                                               RetryCallback<T, E> callback, Throwable throwable) {
        int attemptNumber = context.getRetryCount();
        if (throwable == null) {
            log.info("Retry operation completed successfully after {} attempt(s)", attemptNumber);
        } else {
            log.error("Retry operation failed after {} attempt(s). Final error: {}",
                    attemptNumber,
                    throwable.getMessage());
        }
    }

    @Override
    public <T, E extends Throwable> void onError(RetryContext context,
                                                 RetryCallback<T, E> callback, Throwable throwable) {
        int attemptNumber = context.getRetryCount();
        boolean willRetry = attemptNumber < 3; // maxAttempts = 3
        log.warn("Retry attempt #{} failed. Will{} retry. Error: {}",
                attemptNumber,
                willRetry ? "" : " not",
                throwable.getMessage());
    }
}
