package pt.jumia.services.easytaxscan.domain.usecases.queries;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.easytaxscan.domain.Profiles;
import pt.jumia.services.easytaxscan.domain.entities.*;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.BadRequestException;
import pt.jumia.services.easytaxscan.domain.properties.DatabaseProperties;
import pt.jumia.services.easytaxscan.domain.repository.DataSourceFactory;
import pt.jumia.services.easytaxscan.domain.repository.EntityDataSourceRepository;
import pt.jumia.services.easytaxscan.domain.usecases.datasource.ReadDataSourceUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.subqueries.ReadSubQueryUseCase;
import pt.jumia.services.easytaxscan.domain.utils.SqlQueryParserUtil;

import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Profile({"!" + Profiles.FAKE_CLIENTS})
public class ValidateDataSourceQueryUseCase implements ValidateDataSourceQuery {
    private final DataSourceFactory dataSourceFactory;
    private final DatabaseProperties databaseProperties;
    private final ReadQueryUseCase readQueryUseCase;
    private final ReadSubQueryUseCase readSubQueryUseCase;
    private final ReadDataSourceUseCase readDataSourceUseCase;
    private static Set<Long> processedIds = new HashSet<>();

    @Override
    public HealthStatus validate(Query query, Map<String, Object> args, String countryCode) {
        HealthStatus healthStatus = HealthStatus.builder().success(true).build();
        try {
            validateQueryParameters(query);
            EntityDataSourceRepository dataSourceAdapter = getDataSourceAdapter(query, countryCode);
            executeValidationQuery(dataSourceAdapter, query.getSql(), args);
        } catch (Exception e) {
            healthStatus = HealthStatus.builder()
                    .success(false)
                    .error("Failed to validate query: " + e.getMessage())
                    .build();
        }
        return healthStatus;
    }


    private void validateQueryParameters(Query query) {
        if (query == null) {
            throw BadRequestException.build("Query or data source cannot be null");
        }
        String dataSourceCode = query.getDataSource().getCode();
        if (!databaseProperties.getDataSources().containsKey(dataSourceCode)) {
            throw BadRequestException.build("Unknown data source: " + dataSourceCode);
        }
    }

    private EntityDataSourceRepository getDataSourceAdapter(Query query, String countryCode) {
        if (query.getDataSource().getCountrySegregated()) {
            return dataSourceFactory.getDatasourceAdapter(
                    databaseProperties.getDataSourceProperties(query.getDataSource().getCode(), countryCode));
        } else {
            return dataSourceFactory.getDatasourceAdapter(
                    databaseProperties.getDataSources().get(query.getDataSource().getCode()));
        }
    }

    private Boolean executeValidationQuery(EntityDataSourceRepository dataSourceAdapter,
                                           String countSql, Map<String, Object> args) {
        List<Map<String, Object>> countResults = dataSourceAdapter.query(countSql, args);

        if (countResults != null && !countResults.isEmpty()) {
            Map<String, Object> resultRow = countResults.getFirst();
            if (!resultRow.containsKey("count")) {
                return true; // Query executed successfully but no count column
            }

            Object countObj = resultRow.get("count");
            return !(countObj instanceof Number) || ((Number) countObj).intValue() > 0;
        }

        return false;
    }

    @Override
    public List<Map<String, Object>> constructPreviewResponse(QueryPreview queryPreview) throws Exception {
        List<Map<String, Object>> mainResult = new ArrayList<>();
        if (queryPreview.getQueryId() != null) {
            Query query = readQueryUseCase.findById(queryPreview.getQueryId());

            List<SubQuery> subQueryList = readSubQueryUseCase.execute(query.getId(),
                    SubQueryFilters.builder().build(), SubQuerySortFilters.builder().build(), PageFilters.builder().build());

            mainResult = constructPreviewWithQueryId(query.getPageSize(),
                    query.getDataSource().getCode(),
                    query.getSql(),
                    subQueryList.stream().map(QueryPreview.SubQueryPreview::new).collect(Collectors.toList()));

        } else {
            DataSource dataSource = readDataSourceUseCase.execute(queryPreview.getDataSource().getId());

            mainResult = constructPreviewWithQueryId(1,
                    dataSource.getCode(),
                    queryPreview.getSql(),
                    CollectionUtils.isEmpty(queryPreview.getSubQueryList()) ? List.of() : queryPreview.getSubQueryList().stream()
                            .map(sqp -> QueryPreview.SubQueryPreview.builder()
                                    .subQueryId(sqp.getSubQueryId())
                                    .mainQueryColumn(sqp.getMainQueryColumn())
                                    .subQueryColumn(sqp.getSubQueryColumn())
                                    .isList(sqp.getIsList())
                                    .build())
                            .collect(Collectors.toList()));
        }
        return mainResult;
    }

    private EntityDataSourceRepository getDatasourceAdapter(String dataSourceCode) {


        return dataSourceFactory.getDatasourceAdapter(
                databaseProperties.getDataSources().get(dataSourceCode));
    }

    private List<Map<String, Object>> constructPreviewWithQueryId(int pageSize, String dataSourceCode, String sql,
                                                                  List<QueryPreview.SubQueryPreview> subQueries) throws Exception {

        return constructPreviewWithQueryId(1, pageSize, dataSourceCode, sql, subQueries, null);
    }

    private List<Map<String, Object>> constructPreviewWithQueryId(int iteration, int pageSize, String dataSourceCode,
                                                                  String sql, List<QueryPreview.SubQueryPreview> subQueries, List<String> sidList) throws Exception {
        // execute query
        Map<String, Object> args = new HashMap();
        args.put(Query.QUERY_ARG_PAGINATION_FIELD, 0);
        args.put(Query.QUERY_ARG_LIMIT, pageSize);
        args.put(Query.QUERY_ARG_OFFSET, 0);
        if (!CollectionUtils.isEmpty(sidList)) {
            args.put(Query.QUERY_ARG_SID_LIST, sidList);
        }

        EntityDataSourceRepository datasourceAdapter = getDatasourceAdapter(dataSourceCode);
        List<Map<String, Object>> queryResult = datasourceAdapter.query(sql, args);
        if (queryResult != null && queryResult.isEmpty()) {
            return null;
        }

        for (Map<String, Object> mainResult : queryResult) {

            subQueries.forEach(subQuery -> {
                String subTableName = null;
                List<Map<String, Object>> subQueryResults = null;

                Object mainResultSidObj = mainResult.get(subQuery.getMainQueryColumn());

                if (mainResultSidObj == null) {
                    return;
                }

                Query sQuery = readQueryUseCase.findById(subQuery.getSubQueryId());
                // find and execute subqueries
                List<SubQuery> subQueryList = readSubQueryUseCase.execute(sQuery.getId(),
                        SubQueryFilters.builder().build(), SubQuerySortFilters.builder().build(), PageFilters.builder().build());

                String mainResultSid = mainResultSidObj.toString();
                try {
                    subQueryResults = constructPreviewWithQueryId(iteration + 1,
                            sQuery.getPageSize(),
                            sQuery.getDataSource().getCode(),
                            sQuery.getSql(),
                            subQueryList.stream().map(QueryPreview.SubQueryPreview::new).collect(Collectors.toList()),
                            List.of(mainResultSid));

                    Object result = subQuery.getIsList() ? subQueryResults : subQueryResults.getFirst();
                    mainResult.put(subQuery.getSubQueryColumn(), result);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
        return queryResult;
    }

}
