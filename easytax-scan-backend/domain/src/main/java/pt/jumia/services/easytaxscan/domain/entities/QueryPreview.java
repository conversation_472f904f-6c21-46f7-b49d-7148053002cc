package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class QueryPreview {

    @Nullable
    private Long id;

    private String sql;

    private DataSource dataSource;

    private List<SubQueryPreview> subQueryList;

    private Long queryId;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    public static class SubQueryPreview {
        private long subQueryId;
        private String mainQueryColumn;
        private String subQueryColumn;
        private Boolean isList;

        public SubQueryPreview(SubQuery subQuery) {
            this.subQueryId = subQuery.getSubQuery().getId();
            this.mainQueryColumn = subQuery.getMainQueryColumn();
            this.subQueryColumn = subQuery.getSubQueryColumn();
            this.isList = subQuery.getIsList();
        }
    }
}
