package pt.jumia.services.easytaxscan.domain.entities.filter;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.easytaxscan.domain.entities.Setting;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class SettingFilters {

    private String property;
    private Setting.Type type;
    private String overrideKey;
    private String value;


    /**
     * @return all the fields inside a map, which can be useful for testing
     */
    public Map<String, String> getAsMap() {
        HashMap<String, String> map = new HashMap<>();

        if (this.property != null) {
            map.put("property", property);
        }
        if (this.type != null) {
            map.put("type", String.valueOf(this.type));
        }
        if (this.overrideKey != null) {
            map.put("overrideKey", this.overrideKey);
        }
        if (this.value != null) {
            map.put("value", this.value);
        }
        return map;
    }
}
