package pt.jumia.services.easytaxscan.domain;

import lombok.*;

@Getter
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@Builder(toBuilder = true)
public class User {

    private final String username;
    @Builder.Default
    private final boolean canAccess = false;
    @Builder.Default
    private final boolean canManageSetting = false;
    @Builder.Default
    private final boolean canAccessSetting = false;
    @Builder.Default
    private final boolean canAccessDataSource = false;
    @Builder.Default
    private final boolean canAccessDocument = false;
    @Builder.Default
    private final boolean canManageDataSource = false;
    @Builder.Default
    private final boolean canAccessScan = false;
    @Builder.Default
    private final boolean canManageScan = false;
    @Builder.Default
    private final boolean canManageQuery = false;
    @Builder.Default
    private final boolean canAccessQuery = false;
    @Builder.Default
    private final boolean canManageWebhooks = false;
    @Builder.Default
    private final boolean canAccessWebhooks = false;
    @Builder.Default
    private final boolean canManageExecutionLog = false;
    @Builder.Default
    private final boolean canAccessExecutionLog = false;
    @Builder.Default
    private final boolean canAccessCountry = false;
    @Builder.Default
    private final boolean canManageCountry = false;

    @Builder.Default
    private final boolean canManageJobs = false;

}

