package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Some fake data for {@link Scan}, to be used in tests and fill in the MEM DB
 */
public interface FakeScan {

    Scan SCAN_CREATE = createScanRequest(null, Scan.Status.ACTIVE, "SCAN0000", "Scan Create Test",
            Scan.Mode.DRYRUN, "0 0 0 15 * ?", "mapping", "sid");
    Scan SCAN_FAIL = createScanRequest(null, Scan.Status.ACTIVE, "SCAN0000FAIL", "Scan Create Test",
            Scan.Mode.DRYRUN, "0 0 0 15 * ?", "mapping", "sid");
    Scan SCAN_UPDATE = createScanRequest(1L, Scan.Status.INACTIVE, "SCAN1000", "Scan Update Test",
            Scan.Mode.DRYRUN, "0 0 0 15 * ?", "mapping", "sid");
    Scan SCAN_FILTER_DATA2 = createScanRequest(2L, Scan.Status.ACTIVE, "SCAN2000", "Scan Data1 Test",
            Scan.Mode.DRYRUN, "0 0 0 15 * ?", "mapping", "sid");
    Scan SCAN_FILTER_DATA3 = createScanRequest(3L, Scan.Status.INACTIVE, "SCAN3000", "Scan Data2 Test",
            Scan.Mode.DRYRUN, "0 0 0 15 * ?", "mapping", "sid");
    Scan SCAN_FILTER_DATA4 = createScanRequest(4L, Scan.Status.ACTIVE, "SCAN4000", "Scan Data4 Test",
            Scan.Mode.DRYRUN, "0 0 0 15 * ?", "mapping", "sid");
    Scan SCAN_FILTER_DATA6 = createScanRequest(6L, Scan.Status.INACTIVE, "SCAN6000", "Scan Data6 Test",
            Scan.Mode.DRYRUN, "0 0 0 15 * ?", "mapping", "sid");
    Scan SCAN_FILTER_DATA7 = createScanRequest(7L, Scan.Status.ACTIVE, "SCAN7000", "Scan Data7 Test",
            Scan.Mode.DRYRUN, "0 0 0 15 * ?", "mapping", "sid");
    Scan SCAN_TEST = createScanRequest(5L, Scan.Status.ACTIVE, "SCAN5000", "Scan Data5 TestCase",
            Scan.Mode.DRYRUN, "0 0 0 15 * ?", "mapping", "sid");


    static Scan createScanRequest(Long id, Scan.Status status, String code, String desc
            , Scan.Mode mode, String cronExpression, String mapping,
                                  String sidColumn) {
        return Scan.builder()
                .id(id)
                .cronExpression(cronExpression)
                .query(FakeQuery.QUERY_CREATE.withoutDbFields())
                .status(status)
                .mode(mode)
                .code(code)
                .description(desc)
                .mapping(mapping)
                .sidColumn(sidColumn)
                .country(FakeCountry.COUNTRY_CREATE.withoutDbFields())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .createdBy(AppConstants.SYSTEM)
                .updatedBy(AppConstants.SYSTEM)
                .build();
    }

    List<Scan> ALL =
            Collections.unmodifiableList(new ArrayList<>() {{
                add(SCAN_FILTER_DATA2);
                add(SCAN_FILTER_DATA2);
                add(SCAN_FILTER_DATA3);
            }});

}
