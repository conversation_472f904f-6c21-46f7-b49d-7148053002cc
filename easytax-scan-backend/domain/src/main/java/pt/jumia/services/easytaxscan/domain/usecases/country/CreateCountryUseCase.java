package pt.jumia.services.easytaxscan.domain.usecases.country;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.exceptions.RecordAlreadyExistsException;
import pt.jumia.services.easytaxscan.domain.repository.CountryRepository;

import java.util.Objects;

@Component
@RequiredArgsConstructor
public class CreateCountryUseCase {

    private final CountryRepository countryRepository;

    public Country execute(Country country) throws RecordAlreadyExistsException {
        if (Objects.isNull(country)) {
            throw new IllegalArgumentException("Country to save cannot be null");
        }

        countryRepository.findByCode(country.getCountryCode())
                .ifPresent(p -> {
                    throw RecordAlreadyExistsException.build(Country.class, country.getCountryCode());
                });

        return countryRepository.insert(country);
    }
}
