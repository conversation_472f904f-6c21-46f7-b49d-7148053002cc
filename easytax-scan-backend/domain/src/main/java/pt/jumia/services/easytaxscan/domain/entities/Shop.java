package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class Shop {

    @Nullable
    private Long id;

    private String name;

    private String countryCode;

    private String shopCode;

    private String createdBy;

    private LocalDateTime createdAt;

    private String updatedBy;

    private LocalDateTime updatedAt;

    private String timeZone;

    public Shop withoutDbFields() {
        return this.toBuilder()
                .id(null)
                .updatedAt(null)
                .createdAt(null)
                .updatedBy(null)
                .createdBy(null)
                .build();
    }

}
