package pt.jumia.services.easytaxscan.domain.usecases.country;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Country;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.CountryRepository;

@Component
@RequiredArgsConstructor
public class ReadCountryUseCase {

    private final CountryRepository countryRepository;

    public Country execute(Long id) throws NotFoundException {
        return countryRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(Country.class, id));
    }
}
