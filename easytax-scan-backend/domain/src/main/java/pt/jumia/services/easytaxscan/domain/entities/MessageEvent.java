package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class MessageEvent {

    private Integer id;

    private String name;

    private Status status;
    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;

    public enum Status {
        ACTIVE,
        INACTIVE
    }

    public MessageEvent withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .updatedAt(null)
                .updatedBy(null)
                .build();
    }

    public static void validate(MessageEvent messageEvent) {
        if (Objects.isNull(messageEvent)) {
            throw new IllegalArgumentException("Message Event cannot be null");
        }
    }
}
