package pt.jumia.services.easytaxscan.domain.repository;

import java.util.List;
import java.util.Optional;
import pt.jumia.services.easytaxscan.domain.entities.Tag;

/**
 * Contract between application and persistence layer for CRUD operation for {@link Tag}
 */
public interface TagRepository {

    Tag insert(Tag tag);

    Optional<Tag> findById(long id);

    List<Tag> findAll();

    Tag update(Tag toUpdateTag);

    void deleteById(long id);
}
