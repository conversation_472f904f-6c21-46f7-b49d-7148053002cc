package pt.jumia.services.easytaxscan.domain.usecases.audit;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.QueryAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.QueryAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.queries.ReadQueryUseCase;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ReadQueryAuditUseCase {
    private final QueryAuditRepository queryAuditRepository;
    private final ReadQueryUseCase readQueryUseCase;

    public List<AuditedEntity<Query>> executeById(long queryId, AuditedEntitySortFilters sortFilters, PageFilters pageFilters) {

        Query query = readQueryUseCase.findById(queryId);

        return queryAuditRepository.getQueryAuditLogById(QueryAuditFilters.builder()
                .id(queryId)
                .sortFilter(sortFilters)
                .pageFilters(pageFilters)
                .build(), query);
    }
    public long executeCountByQueryId(long queryId) {
        return queryAuditRepository.getAuditLogCountById(QueryAuditFilters.builder()
                .id(queryId)
                .build());
    }
}
