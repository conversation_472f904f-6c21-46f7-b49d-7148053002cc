package pt.jumia.services.easytaxscan.domain.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "info")
public class InfoProperties {

    private Build build = new Build();

    @Data
    public static class Build {
        private String version = "0.1.0";
    }
}
