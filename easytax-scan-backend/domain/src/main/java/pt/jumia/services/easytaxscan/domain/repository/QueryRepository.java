package pt.jumia.services.easytaxscan.domain.repository;

import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.entities.filter.QueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.QuerySortFilters;

import java.util.List;
import java.util.Optional;

/**
 * Contract between application and persistence layer for Query operation for {@link Query}
 */
public interface QueryRepository {

    Query insert(Query query);

    Optional<Query> findById(Long id);

    Optional<Query> findByCode(String code);

    Query update(long id, Query query);

    Long executeCount(QueryFilters filters);

    List<Query> findAll(QueryFilters filters,
                        QuerySortFilters sortFilters,
                        PageFilters pageFilters);

    void deleteById(Long id);
}
