package pt.jumia.services.easytaxscan.domain.exceptions.parsing;


import pt.jumia.services.easytaxscan.domain.exceptions.ErrorCode;

import java.io.Serial;

/**
 * Exception thrown when we cannot find an entity
 */
public class TemplateCreationException extends ParsingException {

    @Serial
    private static final long serialVersionUID = 7529809051701426868L;

    public TemplateCreationException(String message, Exception exception) {
        super(ErrorCode.ERROR_CREATING_TEMPLATE, message, exception);
    }

    @Override
    public ErrorCode getErrorCode() {
        return ErrorCode.ERROR_CREATING_TEMPLATE;
    }
}
