package pt.jumia.services.easytaxscan.domain.usecases.audit;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.AuditedEntity;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.AuditedEntitySortFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.audit.DocumentAuditFilters;
import pt.jumia.services.easytaxscan.domain.repository.audit.DocumentAuditRepository;
import pt.jumia.services.easytaxscan.domain.usecases.document.ReadDocumentUseCase;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ReadDocumentAuditUseCase {
    private final DocumentAuditRepository documentAuditRepository;
    private final ReadDocumentUseCase readDocumentUseCase;

    public List<AuditedEntity<Document>> executeById(long documentId, AuditedEntitySortFilters sortFilters, PageFilters pageFilters) {

        Document document = readDocumentUseCase.findById(documentId);

        return documentAuditRepository.getDocumentAuditLogById(DocumentAuditFilters.builder()
                .id(documentId)
                .sortFilter(sortFilters)
                .pageFilters(pageFilters)
                .build(), document);
    }
    public long executeCountByDocumentId(long documentId) {
        return documentAuditRepository.getAuditLogCountById(DocumentAuditFilters.builder()
                .id(documentId)
                .build());
    }
}
