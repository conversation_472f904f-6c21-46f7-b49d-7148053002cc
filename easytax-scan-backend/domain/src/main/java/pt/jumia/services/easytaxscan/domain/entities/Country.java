package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class Country {

    @Nullable
    private Long id;

    private String countryCode;

    private String countryName;

    private LocalDateTime createdAt;

    private String createdBy;

    private LocalDateTime updatedAt;

    private String updatedBy;



    public Country withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .updatedBy(null)
                .updatedAt(null)
                .build();
    }

}
