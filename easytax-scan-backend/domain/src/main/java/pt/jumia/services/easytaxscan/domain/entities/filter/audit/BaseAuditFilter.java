package pt.jumia.services.easytaxscan.domain.entities.filter.audit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;

@Data
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
public class BaseAuditFilter {

    private final AuditRevisionFilters revisionFilters;
    private final PageFilters pageFilters;
    private final AuditedEntitySortFilters sortFilter;

}
