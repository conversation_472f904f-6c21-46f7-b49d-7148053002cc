package pt.jumia.services.easytaxscan.domain.templates;

import pt.jumia.services.easytaxscan.domain.entities.mappingtemplates.WrappedObject;

import java.util.HashMap;
import java.util.Map;

public class TemplateParsingHelper {

    private TemplateParsingHelper() {
        // for static use
    }

    /**
     * Decides how the wrapped object data is available in freemarker
     * <p>
     * If it is generic we want to display the map itself, this way the fields are accessed
     * directly like for example "${respCenter}".
     */
    public static Map<String, Object> convertWrappedObject(WrappedObject wrappedObject) {
        Map<String, Object> variables = new HashMap<>();
        variables.put(wrappedObject.getObjectName(), wrappedObject.getObject());
        return variables;
    }
}
