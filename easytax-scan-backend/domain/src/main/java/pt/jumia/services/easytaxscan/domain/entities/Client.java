package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class Client {

    private Long sid;

    private String clientCode;

    private String clientName;

    private String address;

    private String contactPerson;

    private String phoneNumber;

    private String email;
}
