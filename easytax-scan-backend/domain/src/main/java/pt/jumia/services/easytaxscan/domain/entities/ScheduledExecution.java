package pt.jumia.services.easytaxscan.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ScheduledExecution {

    public enum SortingFields {
        NEXT_FIRE_TIME, STATUS
    }

    public enum Status {
        SCHEDULED, RUNNING, PAUSED
    }

    private Long countryId;
    private Long scanId;
    private LocalDateTime nextFireTimeLocal;
    private LocalDateTime nextFireTimeUtc;
    private String localTimeZone;
    private Status status;
    private String scanCode;

}
