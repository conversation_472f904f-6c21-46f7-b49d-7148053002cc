package pt.jumia.services.easytaxscan.domain.usecases.document;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.DocumentSortFilters;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.DocumentRepository;

import java.util.List;


@Component
@RequiredArgsConstructor
public class ReadDocumentUseCase {

    private final DocumentRepository documentRepository;

    public List<Document> execute(DocumentFilters filters, DocumentSortFilters sortFilters, PageFilters pageFilters) {
        return documentRepository.findAll(filters, sortFilters, pageFilters);
    }

    public Document findById(Long id) throws NotFoundException {
        return documentRepository.findById(id)
                .orElseThrow(() -> NotFoundException.build(Document.class, id));
    }

    public Long executeCount(DocumentFilters filters) {
        return documentRepository.executeCount(filters);
    }
}
