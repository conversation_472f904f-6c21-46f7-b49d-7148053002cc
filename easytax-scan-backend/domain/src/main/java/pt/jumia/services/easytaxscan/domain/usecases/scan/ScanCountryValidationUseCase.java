package pt.jumia.services.easytaxscan.domain.usecases.scan;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.PageFilters;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.entities.SubQuery;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQueryFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.SubQuerySortFilters;
import pt.jumia.services.easytaxscan.domain.repository.SubQueryRepository;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
@RequiredArgsConstructor
@Slf4j
public class ScanCountryValidationUseCase {

    private final SubQueryRepository subQueryRepository;

    public void validateScanCountry(Scan scan) {
        if (scan == null || scan.getQuery() == null || scan.getQuery().getId() == null) {
            throw new IllegalArgumentException("Scan or its query cannot be null");
        }

        // Main query checking whether segregated or not
        validateCountryForSegregatedDataSource(scan, scan.getQuery().getDataSource().getCountrySegregated());
        Set<Long> processedQueryIds = new HashSet<>();
        validateSubqueries(scan, scan.getQuery().getId(), processedQueryIds);
    }

    private void validateSubqueries(Scan scan, Long queryId, Set<Long> processedQueryIds) {
        if (!processedQueryIds.add(queryId)) {
            log.warn("Circular reference detected in subquery hierarchy for query ID: {}", queryId);
            return;
        }

        List<SubQuery> subQueryList = subQueryRepository.findAll(queryId,
                SubQueryFilters.builder().build(),
                SubQuerySortFilters.builder().build(),
                PageFilters.builder().build());

        for (SubQuery subQuery : subQueryList) {
            Boolean isSegregated = subQuery.getSubQuery().getDataSource().getCountrySegregated();
            validateCountryForSegregatedDataSource(scan, isSegregated);

            // Recursively check nested subqueries if this subquery has an ID
            if (subQuery.getSubQuery() != null && subQuery.getSubQuery().getId() != null) {
                validateSubqueries(scan, subQuery.getSubQuery().getId(), processedQueryIds);
            }
        }
    }

    private void validateCountryForSegregatedDataSource(Scan scan, Boolean isSegregated) {
        if (isSegregated && (scan.getCountry() == null || scan.getCountry().getId() == null)) {
            throw new IllegalArgumentException("Country cannot be null for segregated data source");
        }
    }
}

