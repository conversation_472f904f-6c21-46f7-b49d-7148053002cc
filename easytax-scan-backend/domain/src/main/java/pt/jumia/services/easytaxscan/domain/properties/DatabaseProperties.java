package pt.jumia.services.easytaxscan.domain.properties;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;

import java.util.HashMap;
import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "data")
public class DatabaseProperties {

    private Map<String, DataSource> dataSources;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DataSource extends DatasourceProperties {
        private ConnectionConfig connection;
        private Map<String, DatasourceProperties> countries = new HashMap<>();

        public boolean isCountrySegregated() {
            return !CollectionUtils.isEmpty(countries);
        }
    }

    @Data
    public static class ConnectionConfig {
        private String datasource;
        private String driver;
        private String url;
        private int port;
        private String database;
        private String schema;
        private String username;
        private String password;
        private Integer threadPoolSize;
        private boolean allowPublicKeyRetrieval;
    }

    @Data
    public static class DatasourceProperties {
        private ConnectionConfig connection = new ConnectionConfig();
        private String timezone = "Z";
        private String dateFormat = "yyyy-MM-dd HH:mm:ss";
        private int readTimeout = 30;
        private int connectionTimeout = 30;
        private String authentication;
        private String urlToken;
        private Map<String, String> headers;
        private Map<String, String> oauth;
        private Map<String, String> oauthHeaders;
    }

    public DatasourceProperties getDataSourceProperties(String application)
            throws NotFoundException {
        DataSource appDataSource = null;
        for (Map.Entry<String, DataSource> dataSourceEntry : this.dataSources.entrySet()) {
            if (dataSourceEntry.getKey().equalsIgnoreCase(application)) {
                appDataSource = dataSourceEntry.getValue();
                break;
            }
        }

        if (appDataSource == null) {
            throw NotFoundException.build("Application datasource not found: " + application);
        }
        return appDataSource;
    }

    public DatasourceProperties getDataSourceProperties(String application, String countryCode)
            throws NotFoundException {
        DataSource appDataSource = null;
        for (Map.Entry<String, DataSource> dataSourceEntry : this.dataSources.entrySet()) {
            if (dataSourceEntry.getKey().equalsIgnoreCase(application)) {
                appDataSource = dataSourceEntry.getValue();
                break;
            }
        }

        if (appDataSource == null) {
            throw NotFoundException.build("Application datasource not found: " + application);
        }

        // If the application has country-specific configurations
        if (appDataSource.isCountrySegregated()) {
            for (Map.Entry<String, DatasourceProperties> countryEntry : appDataSource.getCountries().entrySet()) {
                if (countryEntry.getKey().equalsIgnoreCase(countryCode)) {
                    return countryEntry.getValue();
                }
            }
            throw NotFoundException.build("Country configuration not found for application '" +
                    application + "' and country '" + countryCode + "'");
        }
        return appDataSource;
    }
}

