package pt.jumia.services.easytaxscan.domain.usecases.queries;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.easytaxscan.domain.entities.DataSource;
import pt.jumia.services.easytaxscan.domain.entities.Query;
import pt.jumia.services.easytaxscan.domain.repository.QueryRepository;
import pt.jumia.services.easytaxscan.domain.repository.SubQueryRepository;
import pt.jumia.services.easytaxscan.domain.usecases.datasource.ReadDataSourceUseCase;
import pt.jumia.services.easytaxscan.domain.usecases.validation.ValidateQueryUseCase;

import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateQueryUseCase {

    private final QueryRepository queryRepository;
    private final SubQueryRepository subQueryRepository;
    private final ValidateQueryUseCase validateQueryUseCase;
    private final ReadDataSourceUseCase readDataSourceUseCase;

    public Query execute(Query query) throws Exception {
        if (Objects.isNull(query)) {
            throw new IllegalArgumentException("Query to save cannot be null");
        }

        readDataSourceUseCase.findById(query.getDataSource().getId());

        log.info("Saving query on the database");
        validateQueryUseCase.validate(query);

        Query savedQuery = queryRepository.insert(query);

        log.info("Saving subqueries on the database");
        if (!CollectionUtils.isEmpty(query.getSubQueries())) {
            query.getSubQueries().forEach(subQuery -> subQueryRepository.insert(savedQuery.getId(), subQuery));
        }
        return savedQuery;
    }
}
