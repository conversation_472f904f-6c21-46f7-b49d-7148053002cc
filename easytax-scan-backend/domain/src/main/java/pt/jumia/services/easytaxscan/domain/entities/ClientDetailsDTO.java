package pt.jumia.services.easytaxscan.domain.entities;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ClientDetailsDTO {
    private String clientCode;
    private String clientName;
    private String address;
    private String contactPerson;
    private String phoneNumber;
    private String email;
    private List<BankAccountDTO> bankAccounts;

    @Data
    @Builder
    public static class BankAccountDTO {
        private String bankName;
        private String accountNumber;
        private String accountHolderName;
        private String branchName;
        private String ifscCode;
        private String swiftCode;
        private String accountType;
    }
}


