package pt.jumia.services.easytaxscan.domain.entities.fake;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import pt.jumia.services.easytaxscan.domain.entities.Tag;

/**
 * Some fake data for {@link Tag}, to be used in tests and fill in the MEM DB
 */
public interface FakeTags {

    Tag VIRTUAL_ATTEMPT = Tag
        .builder()
        .name("Virtual-Attempt")
        .description("Virtual attempt tag that marks a trigger for client to come pickup their packages")
        .color("#4C9B4A")
        .createdAt(LocalDateTime.now())
        .build();

    Tag ORDER_CANCELED = Tag
        .builder()
        .name("Order cancelled")
        .description("Order canceled tag that marks a trigger for OMS orders cancelled")
        .color("#E2CA14")
        .createdAt(LocalDateTime.now())
        .build();

    Tag REVERSE_FLOW = Tag
        .builder()
        .name("Reverse flow")
        .description("Reverse flow tag for triggers that match R* service codes")
        .color("#E00707")
        .createdAt(LocalDateTime.now())
        .build();

    Tag DARAZ = Tag
        .builder()
        .name("Daraz")
        .description("Daraz tag that marks a trigger to be used for Daraz")
        .color("#30006E")
        .createdAt(LocalDateTime.now())
        .build();

    Tag VENDOR_JOUNERY = Tag
        .builder()
        .name("Vendor-Journey")
        .description("Vendor journey that marks a trigger to vendors")
        .color("#800040")
        .createdAt(LocalDateTime.now())
        .build();

    Tag CUSTOMER_JOUNERY = Tag
        .builder()
        .name("Customer-Journey")
        .description("Vendor journey that marks a trigger to customers")
        .color("#2A1953")
        .createdAt(LocalDateTime.now())
        .build();

    List<Tag> ALL = Collections.unmodifiableList(new ArrayList<Tag>() {{
        add(VIRTUAL_ATTEMPT);
        add(ORDER_CANCELED);
        add(REVERSE_FLOW);
        add(DARAZ);
        add(VENDOR_JOUNERY);
        add(CUSTOMER_JOUNERY);
    }});

}
