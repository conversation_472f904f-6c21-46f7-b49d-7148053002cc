package pt.jumia.services.easytaxscan.domain.repository;

import pt.jumia.services.easytaxscan.domain.entities.ScheduledExecution;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScheduledExecutionFilters;
import pt.jumia.services.easytaxscan.domain.entities.filter.ScheduledExecutionSortFilters;

import java.util.List;

public interface ScheduledExecutionsRepository {

    List<ScheduledExecution> findAll(ScheduledExecutionFilters filters, ScheduledExecutionSortFilters sortFilters);

}
