package pt.jumia.services.easytaxscan.domain.jobs;

import lombok.AllArgsConstructor;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.entities.Job;
import pt.jumia.services.easytaxscan.domain.entities.RetryJob;
import pt.jumia.services.easytaxscan.domain.entities.Scan;
import pt.jumia.services.easytaxscan.domain.repository.JobsRepository;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

@Component
@AllArgsConstructor
public class CreateJobsUseCase {


    private JobsRepository jobsRepository;

    public Job createScanJob(Scan scanSaved) throws SchedulerException {
        Job job = Job.builder().jobName(scanSaved.getCode())
                .cronExpression(scanSaved.getCronExpression()).build();
        return jobsRepository.createJob(job, AppConstants.SCAN_JOB);
    }

    public Job createRetryJob(RetryJob retryJob) throws SchedulerException {
        Job job = Job.builder().jobName(retryJob.getJob())
                .cronExpression(retryJob.getCronExpression()).build();
        return jobsRepository.createJob(job, AppConstants.RETRY_JOB);
    }
}
