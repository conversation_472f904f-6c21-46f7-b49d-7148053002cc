package pt.jumia.services.easytaxscan.domain.entities.filter;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
public abstract class GroupFilters<E extends Enum<E>> implements Filter {

    protected List<E> fields;

    public List<String> getGroupFieldsAsString() {
        return fields.stream().map(Enum::name).toList();
    }

}
