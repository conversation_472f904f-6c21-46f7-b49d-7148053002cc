package pt.jumia.services.easytaxscan.domain.usecases.tags;

import java.util.Objects;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.repository.TagRepository;
import pt.jumia.services.easytaxscan.domain.entities.Tag;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;

@Component
public class UpdateTagsUseCase {

    @Autowired
    private TagRepository tagRepository;

    public Tag execute(long id, Tag toUpdateTag) throws NotFoundException {

        if (Objects.isNull(toUpdateTag)) {
            // If programing error passed a null tag, throw IllegalArgumentException
            throw new IllegalArgumentException("Tag cannot be null");
        }

        Optional<Tag> optionalTag = tagRepository.findById(id);

        if (!optionalTag.isPresent()) {
            throw NotFoundException.createNotFound(Tag.class, id);
        }

        return tagRepository.update(toUpdateTag);
    }
}
