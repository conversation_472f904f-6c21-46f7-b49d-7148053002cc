package pt.jumia.services.easytaxscan.domain.templates;

import pt.jumia.services.easytaxscan.domain.entities.mappingtemplates.WrappedObject;
import pt.jumia.services.easytaxscan.domain.exceptions.parsing.TemplateCreationException;
import pt.jumia.services.easytaxscan.domain.exceptions.parsing.TemplateParsingException;

import java.util.Map;

public interface TemplateParser {
    String execute(
            String name, String stringTemplate, Map<String, Object> variables, WrappedObject wrappedObject)
            throws TemplateCreationException, TemplateParsingException;
}
