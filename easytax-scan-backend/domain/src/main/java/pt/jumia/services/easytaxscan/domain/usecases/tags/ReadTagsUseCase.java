package pt.jumia.services.easytaxscan.domain.usecases.tags;

import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.easytaxscan.domain.exceptions.NotFoundException;
import pt.jumia.services.easytaxscan.domain.repository.TagRepository;
import pt.jumia.services.easytaxscan.domain.entities.Tag;

/**
 * Use case that will return all tags from persistence layer
 */
@Component
public class ReadTagsUseCase {

    @Autowired
    private TagRepository tagRepository;

    public List<Tag> execute() {
        return tagRepository.findAll();
    }

    public Tag execute(int id) throws NotFoundException {
        Optional<Tag> optionalTag = tagRepository.findById(id);

        if (!optionalTag.isPresent()) {
            throw NotFoundException.createNotFound(Tag.class, id);
        }

        return optionalTag.get();
    }
}
