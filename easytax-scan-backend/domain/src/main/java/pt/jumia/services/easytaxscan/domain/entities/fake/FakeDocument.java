package pt.jumia.services.easytaxscan.domain.entities.fake;

import pt.jumia.services.easytaxscan.domain.entities.Document;
import pt.jumia.services.easytaxscan.domain.entities.Mode;
import pt.jumia.services.easytaxscan.domain.utils.AppConstants;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


public interface FakeDocument {


    Document DOCUMENT_CREATE = createDocument(null, "DOC0000", "CREATED", "Query Data 1", "Request Payload 1", "DRYRUN");
    Document DOCUMENT_UPDATE = createDocument(1L, "DOC1000", "CREATED", "Query Data 2", "Request Payload 2", "DRYRUN");
    Document DOCUMENT_FILTER_1 = createDocument(2L, "DOC2000", "CREATED", "Query Data 3", "Request Payload 3", "DRYRUN");
    Document DOCUMENT_FILTER_2 = createDocument(3L, "DOC3000", "CREATED", "Query Data 4", "Request Payload 4", "DRYRUN");
    Document DOCUMENT_TEST = createDocument(4L, "DOC4000", "CREATED", "Query Data 5", "Request Payload 5", "DRYRUN");


    static Document createDocument(Long id, String sid, String status, String queryData, String requestPayload, String mode) {
        return Document.builder()
                .id(id)
                .sid(sid)
                .status(Document.Status.valueOf(status))
                .mode(Mode.valueOf(mode))
                .queryData(queryData)
                .executionLog(FakeExecutionLog.EXECUTION_LOG_CREATE)
                .requestPayload(requestPayload)
                .createdAt(LocalDateTime.now())
                .createdBy(AppConstants.SYSTEM)
                .updatedAt(LocalDateTime.now())
                .updatedBy(AppConstants.SYSTEM)
                .build();
    }

    List<Document> ALL = Collections.unmodifiableList(new ArrayList<>() {{
        add(DOCUMENT_CREATE);
        add(DOCUMENT_UPDATE);
    }});
}
